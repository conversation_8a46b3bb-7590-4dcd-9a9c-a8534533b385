{"app": {"name": "scheduler-service", "version": "0.0.1"}, "cache": {"connectionString": "cacheservice1.wkhxcg.ng.0001.use1.cache.amazonaws.com:6379", "instance": "", "schemaName": "dbo", "tableName": "<PERSON><PERSON>", "BigPayloadCacheConnectionString": "cacheservice1.wkhxcg.ng.0001.use1.cache.amazonaws.com:6379", "IdempotencyCacheConnectionString": "cacheservice1.wkhxcg.ng.0001.use1.cache.amazonaws.com:6379", "ExternalRequestsCacheConnectionString": "cacheservice1.wkhxcg.ng.0001.use1.cache.amazonaws.com:6379"}, "serilog": {"consoleEnabled": true, "level": "Information", "path": "../logs/scheduler-{0}.txt"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "swagger": {"enabled": true, "reDocEnabled": false, "name": "v1", "title": "scheduler-service", "version": "v1", "routePrefix": "", "includeSecurity": true}, "scheduler": {"StorageType": "PostgreSQL", "DashboardEnabled": true}, "jaeger": {"agentHost": "localhost", "agentPort": 6831}, "AllowedHosts": "*"}