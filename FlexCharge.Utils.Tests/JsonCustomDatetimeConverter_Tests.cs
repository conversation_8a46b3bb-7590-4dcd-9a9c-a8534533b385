using NUnit.Framework;
using System;
using System.Text.Json;
using FlexCharge.Utils.JsonConverters;

namespace FlexCharge.Utils.Tests;

[TestFixture]
public class JsonCustomDateTimeConverterTests
{
    private JsonSerializerOptions _serializerSettings;

    [SetUp]
    public void SetUp()
    {
        // Initialize JsonSerializerSettings with the custom converter
        DateTimeOffsetFormatConverter.SetFormat("yyyy-MM-ddTHH:mm:ssZ");
        _serializerSettings = new JsonSerializerOptions()
        {
            Converters = {new DateTimeOffsetFormatConverter()}
        };
    }

    // [Test]
    // public void ShouldParseValidDate()
    // {
    //     var json = "\"2025-03-26T12:34:56Z\""; // Simple date string (UTC)
    //     var result = JsonSerializer.Deserialize<DateTime?>(json, _serializerSettings);
    //
    //     // The parsed DateTimeOffset should represent the UTC time
    //     var expectedDate = new DateTimeOffset(2025, 3, 26, 12, 34, 56, TimeSpan.Zero); // Date with UTC offset
    //     var expectedUtcDate = expectedDate.UtcDateTime; // Convert to UTC DateTime
    //
    //     Assert.IsNotNull(result);
    //     Assert.AreEqual(expectedUtcDate, result.Value); // Compare UTC DateTime
    // }

    [TestCase("\"2025-03-26T00:05:11Z\"", "2025-03-26T00:05:11Z")]
    [TestCase("\"2024-12-25T08:00:00Z\"", "2024-12-25T08:00:00Z")]
    [TestCase("\"2025-01-01T00:00:00Z\"", "2025-01-01T00:00:00Z")]
    [TestCase("\"2025-03-26T12:34:56Z\"", "2025-03-26T12:34:56Z")]
    //[TestCase("\"12/25/2024 08:00:00\"", "12/25/2024 08:00:00")]
    public void ShouldParseValidDate(string json, string expectedDateString)
    {
        var result = JsonSerializer.Deserialize<DateTime?>(json, _serializerSettings);

        // Convert the expected string to DateTimeOffset
        //var expectedDate = DateTime.Parse(expectedDateString);

        Assert.IsNotNull(result);
        Assert.AreEqual(expectedDateString, result.Value.ToString("yyyy-MM-ddTHH:mm:ssZ")); // Compare UTC DateTime
    }

    [Test]
    public void ShouldHandleNullDate()
    {
        var json = "null"; // Null value
        var result = JsonSerializer.Deserialize<DateTime?>(json, _serializerSettings);

        Assert.IsNull(result);
    }

    [Test]
    public void ShouldHandleEmptyStringDate()
    {
        var json = "\"\""; // Empty string as date
        var result = JsonSerializer.Deserialize<DateTime?>(json, _serializerSettings);

        //expected to be datetime.minvalue
        Assert.AreEqual(DateTime.MinValue, result);
    }

    // [Test]
    // public void ShouldThrowJsonExceptionForInvalidDate()
    // {
    //     var json = "\"invalid-date-string\""; // Invalid date string
    //
    //     var ex = Assert.Throws<JsonException>(() =>
    //         JsonSerializer.Deserialize<DateTime?>(json, _serializerSettings));
    //
    //     Assert.That(ex.Message, Is.EqualTo("Unable to parse date: invalid-date-string"));
    // }

    [Test]
    public void ShouldParseDateWithTimezoneOffset()
    {
        var json = "\"2025-03-26T12:34:56+02:00\""; // Date with timezone offset
        var result = JsonSerializer.Deserialize<DateTimeOffset?>(json, _serializerSettings);

        // The parsed DateTimeOffset should be converted to UTC
        var expectedDate =
            new DateTimeOffset(2025, 3, 26, 12, 34, 56, TimeSpan.FromHours(2)); // Date with offset +02:00
        var expectedUtcDate = expectedDate.UtcDateTime; // Convert to UTC

        Assert.IsNotNull(result);
        Assert.AreEqual(expectedUtcDate, result.Value.UtcDateTime); // Compare UTC DateTime
    }

    //serialization tests
    [Test]
    public void ShouldSerializeDateTimeCorrectly()
    {
        // Test with a valid DateTime
        var date = new DateTime(2025, 3, 26, 12, 34, 56, DateTimeKind.Utc);
        var json = JsonSerializer.Serialize(date, _serializerSettings);

        // Check if the date is serialized to the expected format
        Assert.AreEqual("\"2025-03-26T12:34:56Z\"", json);
    }

    [Test]
    public void ShouldSerializeNullDateTimeCorrectly()
    {
        // Test with a null DateTime
        DateTime? date = null;
        var json = JsonSerializer.Serialize(date, _serializerSettings);

        // Check if the null value is serialized as null
        Assert.AreEqual("null", json);
    }
}