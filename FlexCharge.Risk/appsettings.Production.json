{"app": {"name": "risk-service", "version": "0.0.1"}, "jwt": {"Provider": "Cognito", "secretKey": "JLBMU2VbJZmt42sUwByUpJJF6Y5mG2gPNU9sQFUpJFcGFJdyKxskR3bxh527kax2UcXHvB", "expiryMinutes": 30, "issuer": "identity-service", "validateLifetime": true, "ValidateAudience": true, "ValidAudience": "3v4ll30aqfb435trbds7447524", "Region": "us-east-1", "UserPoolId": "us-east-1_xHv2n5DhZ", "AppClientId": "3v4ll30aqfb435trbds7447524"}, "cache": {"connectionString": "cache-redis-production.wkhxcg.clustercfg.use1.cache.amazonaws.com:6379", "instance": "", "schemaName": "dbo", "tableName": "<PERSON><PERSON>", "BigPayloadCacheConnectionString": "cache-redis-production.wkhxcg.clustercfg.use1.cache.amazonaws.com:6379", "IdempotencyCacheConnectionString": "idempotency.wkhxcg.clustercfg.use1.cache.amazonaws.com:6379", "ExternalRequestsCacheConnectionString": "tracking.wkhxcg.clustercfg.use1.cache.amazonaws.com:6379"}, "dataStream": {"provider": "kinesis"}, "email": {"provider": "sendgrid", "key": "*********************************************************************", "supportEmail": "<EMAIL>", "senderEmail": "<EMAIL>", "senderName": "FlexFactor"}, "sms": {"SID": "", "Token": "", "TwilioPhone": "", "WhatsappPhone": "", "TwilioCompanyName": "", "ServiceSid": "", "TwilioAuthyAPIKey": "", "TwilioVoiceSmsStartUrl": "https://api.authy.com/protected/json/phones/verification/start", "TwilioVoiceSmsChecktUrl": ""}, "serilog": {"consoleEnabled": true, "level": "Information", "path": "../logs/risk-{0}.txt"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "swagger": {"enabled": true, "commentsEnabled": true, "reDocEnabled": false, "name": "v1", "title": "risk-service", "version": "v1", "routePrefix": "", "includeSecurity": true}, "jaeger": {"agentHost": "**************", "agentPort": 6831}, "internalReporting": {"emailTemplateId": "d-d3185eabd44c4469b2f804f55b0fddd2"}, "backgroundWorkerService": {"executionInterval": 500}, "dynamoDb": {"Endpoint": "https://dynamodb.us-east-1.amazonaws.com"}, "AllowedHosts": "*"}