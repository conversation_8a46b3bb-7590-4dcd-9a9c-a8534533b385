using System;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.BackgroundJobs;
using FlexCharge.Common.Telemetry;
using Microsoft.Extensions.DependencyInjection;


namespace FlexCharge.Risk.Services;

public class ExecuteRiskAlertsQueriesCommand : BackgroundWorkerCommand
{
    protected override async Task ExecuteAsync(IServiceProvider serviceProvider, CancellationToken cancellationToken)
    {
        using var workspan = Workspan.Start<ExecuteRiskAlertsQueriesCommand>()
            .LogEnterAndExit();

        try
        {
            var riskAlertsService = serviceProvider.GetRequiredService<IRiskAlertsService>();

            await riskAlertsService.RunRiskAlertQueriesAsync();
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, "EXCEPTION: SendMerchantRiskAlertsCommand > ExecuteAsync");
        }
    }
}