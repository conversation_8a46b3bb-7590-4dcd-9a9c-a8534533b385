using System;

namespace FlexCharge.Merchants.Entities
{
    public class Contact : AuditableEntity
    {
        public Guid UserId { get; set; }
        public bool Primary { get; set; }
        public string Role { get; set; }
        public Guid ExternalId { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Email { get; set; }
        public string Phone { get; set; }
        public string SecondaryPhone { get; set; }

        public bool ConfirmedUser { get; set; }
        //public Application Application { get; set; }
        // public Guid? AccountId { get; set; }
    }
}
