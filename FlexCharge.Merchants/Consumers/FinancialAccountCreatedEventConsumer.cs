using System.Threading.Tasks;
using FlexCharge.Common.Shared.Payments;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.FinancialAccounts;
using MassTransit;

namespace FlexCharge.Merchants.Consumers;

public class FinancialAccountCreatedEventConsumer : IConsumer<FinancialAccountCreatedEvent>
{
    private readonly PostgreSQLDbContext _dbContext;

    public FinancialAccountCreatedEventConsumer(PostgreSQLDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task Consume(ConsumeContext<FinancialAccountCreatedEvent> context)
    {
        using var workspan = Workspan.Start<FinancialAccountCreatedEventConsumer>()
            .Baggage("FinancialAccountId", context.Message.Id);

        switch (context.Message.RelatedEntityType)
        {
            case FinancialAccountsRelatedEntityType.Merchant:
            {
                var merchant = await _dbContext.Merchants.FindAsync(context.Message.RelatedEntityId);
                if (merchant != null)
                {
                    merchant.FinancialAccountId = context.Message.Id;
                    _dbContext.Update(merchant);
                    await _dbContext.SaveChangesAsync();
                }

                break;
            }
            case FinancialAccountsRelatedEntityType.Partner:
            {
                var partner = await _dbContext.Partners.FindAsync(context.Message.RelatedEntityId);
                if (partner != null)
                {
                    partner.FinancialAccountId = context.Message.Id;
                    
                    _dbContext.Update(partner);
                    await _dbContext.SaveChangesAsync();
                }

                break;
            }
            default:
                break;
        }
    }
}