using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Authentication;
using FlexCharge.Common.Shared;
using FlexCharge.Common.Telemetry;
using FlexCharge.Merchants.DTO.PartnersDTOS;
using FlexCharge.Merchants.Entities;
using FlexCharge.Utils;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace FlexCharge.Merchants.Controllers;

public partial class PartnersController
{
    [HttpGet("{partnerId:guid}/fees")]
    [Authorize(policy: MyPolicies.SUPER_ADMINS_ONLY)]
    [ProducesResponseType(200)]
    public async Task<IActionResult> GetFees([FromRoute] Guid partnerId, CancellationToken token)
    {
        using var workspan = Workspan.StartEndpoint<PartnersController>(this, partnerId, _globalData)
            .LogEnterAndExit();

        try
        {
            if (!ModelState.IsValid)
                return ValidationProblem();

            var partner = await _dbContext
                .Partners
                .Include(x => x.PartnerFees)
                .SingleOrDefaultAsync(x => x.Id == partnerId, cancellationToken: token);

            if (partner is null)
                return BadRequest("Partner not found");

            return Ok(partner.PartnerFees.Select(x => new PartnerFeeItemDTO()
            {
                Id = x.Id,
                PartnerId = x.PartnerId,
                Amount = x.Amount,
                AmountFormatted = EnumHelpers.ParseEnum<FEE_TYPE>(x.Type) == FEE_TYPE.Percentage
                    ? Formatters.IntToDecimalFixed2Digits(x.Amount)
                        .ToString("P", CultureInfo.CreateSpecificCulture("en-US"))
                    : Formatters.IntToDecimalFixed2Digits(x.Amount)
                        .ToString("C", CultureInfo.CreateSpecificCulture("en-US")),
                MinimumFeeAmount = x.MinimumFeeAmount,
                MinimumFeeFormatted = x.MinimumFeeAmount?.ToString("C", CultureInfo.CreateSpecificCulture("en-US")),
                Name = x.Name,
                Group = x.Group,
                ApplyOrder = x.ApplyOrder,
                IsActive = x.IsActive,
                Type = EnumHelpers.ParseEnum<FEE_TYPE>(x.Type),
                AppliedOn = EnumHelpers.ParseEnum<CHARGE_TYPE>(x.ChargeType)
            }));
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, $"EXCEPTION: PartnersController > GET > Failed getting fee for partner");
            return StatusCode(StatusCodes.Status500InternalServerError, "Failed");
        }
    }

    [HttpPost("{partnerId:guid}/fees")]
    [Authorize(policy: MyPolicies.SUPER_ADMINS_ONLY)]
    [ProducesResponseType(200)]
    public async Task<IActionResult> AssignFee([FromRoute] Guid partnerId,
        PartnerFeeItemDTO fee,
        CancellationToken token)
    {
        using var workspan = Workspan.StartEndpoint<PartnersController>(this, new {partnerId, fee}, _globalData)
            .LogEnterAndExit();

        try
        {
            if (!ModelState.IsValid)
                return ValidationProblem();

            var partner =
                await _dbContext.Partners.Include(x => x.PartnerFees)
                    .SingleOrDefaultAsync(x => x.Id == partnerId, cancellationToken: token);

            if (partner is null)
                return BadRequest("Partner not found");

            partner.PartnerFees = new List<PartnerFee>
            {
                new()
                {
                    Amount = fee.Amount,
                    MinimumFeeAmount = fee.MinimumFeeAmount,
                    Name = fee.Name,
                    Type = fee.Type.ToString(),
                    ChargeType = fee.AppliedOn.ToString(),
                    Group = fee.Group,
                    ApplyOrder = fee.ApplyOrder,
                    IsActive = fee.IsActive
                }
            };

            _dbContext.Partners.Update(partner);
            await _dbContext.SaveChangesAsync(token);

            return Ok();
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, $"EXCEPTION: PartnersController > POST > Failed assigning fee to partner");
            return StatusCode(StatusCodes.Status500InternalServerError, "Failed");
        }
    }

    [HttpPut("{partnerId:guid}/fees")]
    [Authorize(policy: MyPolicies.SUPER_ADMINS_ONLY)]
    [ProducesResponseType(200)]
    public async Task<IActionResult> UpdateFee([FromRoute] Guid partnerId,
        PartnerFeeItemDTO fee,
        CancellationToken token)
    {
        using var workspan = Workspan.StartEndpoint<PartnersController>(this, new {partnerId, fee}, _globalData)
            .LogEnterAndExit();

        try
        {
            if (!ModelState.IsValid)
                return ValidationProblem();

            var partner = await _dbContext
                .Partners
                .Include(x => x.PartnerFees)
                .SingleOrDefaultAsync(x => x.Id == partnerId, cancellationToken: token);

            if (partner is null)
                return BadRequest("Partner not found");

            var feeToUpdate = partner.PartnerFees.FirstOrDefault(x => x.Id == fee.Id);

            if (feeToUpdate is null)
                return BadRequest("Fee not found");

            feeToUpdate.Amount = fee.Amount;
            feeToUpdate.Name = fee.Name;
            feeToUpdate.MinimumFeeAmount = fee.MinimumFeeAmount;
            feeToUpdate.Type = fee.Type.ToString();
            feeToUpdate.ChargeType = fee.AppliedOn.ToString();
            feeToUpdate.Group = fee.Group;
            feeToUpdate.ApplyOrder = fee.ApplyOrder;
            feeToUpdate.IsActive = fee.IsActive;

            _dbContext.Partners.Update(partner);
            await _dbContext.SaveChangesAsync(token);

            return Ok();
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, $"EXCEPTION: PartnersController > PUT > Failed updating fee for partner");
            return StatusCode(StatusCodes.Status500InternalServerError, "Failed");
        }
    }
}