using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Authentication;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Merchants.DTO;
using FlexCharge.Merchants.Entities;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace FlexCharge.Merchants.Controllers;

public partial class SalesAgenciesController
{
    [HttpDelete("{agencyId:guid}/agent/{id:guid}")]
    [Authorize(policy: MyPolicies.SUPER_ADMINS_ONLY)]
    [ProducesResponseType(200)]
    public async Task<IActionResult> DeleteAgent([FromRoute] Guid agencyId, [FromRoute] Guid id,
        CancellationToken token)
    {
        using var workspan = Workspan.StartEndpoint<SalesAgenciesController>(this, id, _globalData)
            .LogEnterAndExit();

        try
        {
            if (!ModelState.IsValid)
                return ValidationProblem();

            await _salesService.DeleteSalesAgentAsync(id);

            return Ok();
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            throw;
        }
    }

    [HttpPost("{agencyId:guid}/agent")]
    [Authorize(policy: MyPolicies.SUPER_ADMINS_ONLY)]
    [ProducesResponseType(200)]
    public async Task<IActionResult> AddSalesAgent([FromRoute] Guid agencyId, SalesAgentCreateDTO payload,
        CancellationToken token)
    {
        using var workspan = Workspan.StartEndpoint<SalesAgenciesController>(this, payload, _globalData)
            .LogEnterAndExit();

        try
        {
            if (!ModelState.IsValid)
                return ValidationProblem();

            if (await _dbContext.Contacts.AnyAsync(x => x.Email == payload.Email, cancellationToken: token))
            {
                ModelState.AddModelError(nameof(payload.Email), "Email already exist");
                return ValidationProblem();
            }

            var role = SuperAdminGroups.SALESAGENCY_ADMIN;
            if (payload.Role == SuperAdminGroups.SALESAGENCY_AGENT)
            {
                role = SuperAdminGroups.SALESAGENCY_AGENT;
            }

            var salesAgent = await _dbContext.SalesAgents.AddAsync(new SalesAgent()
            {
                AgencyId = agencyId,
                Name = payload.Name,
                Contact = new Contact
                {
                    Primary = true,
                    FirstName = payload.FirstName,
                    LastName = payload.LastName,
                    Email = payload.Email,
                    Phone = payload.Phone,
                    Role = role
                }
            }, token);

            await _dbContext.SaveChangesAsync();

            return Ok(new
            {
                Id = salesAgent.Entity.Id,
                Name = salesAgent.Entity.Name,
                Contact = new
                {
                    FirstName = salesAgent.Entity.Contact.FirstName,
                    LastName = salesAgent.Entity.Contact.LastName,
                    Email = salesAgent.Entity.Contact.Email,
                    Phone = salesAgent.Entity.Contact.Phone,
                    UserActive = salesAgent.Entity.Contact.UserId != Guid.Empty,
                }
            });
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, $"EXCEPTION: SalesController > POST > Failed adding sail user");
            throw;
        }
    }

    [HttpPost("{agencyId:guid}/invite-agent")]
    [Authorize(policy: MyPolicies.SUPER_ADMINS_ONLY)]
    [ProducesResponseType(200)]
    public async Task<IActionResult> InviteSalesAgent([FromRoute] Guid agencyId, Guid salesAgentId,
        CancellationToken token)
    {
        using var workspan = Workspan.StartEndpoint<SalesAgenciesController>(this, salesAgentId, _globalData)
            .LogEnterAndExit();

        try
        {
            if (!ModelState.IsValid)
                return ValidationProblem();

            var salesAgent = await _dbContext.SalesAgents.Include(x => x.Contact)
                .SingleOrDefaultAsync(x => x.Id == salesAgentId, cancellationToken: token);

            if (salesAgent is null)
            {
                return BadRequest();
            }

            await _publisher.Publish(new UserInviteRequestedEvent
            {
                FirstName = salesAgent.Contact?.FirstName,
                LastName = salesAgent.Contact?.LastName,
                Phone = salesAgent.Contact?.Phone,
                Email = salesAgent.Contact?.Email,
                Mid = default(Guid),
                Pid = salesAgent.Id,
                UserId = default(Guid),
                Group = SuperAdminGroups.SALESAGENCY_AGENT
            }, token);

            return Ok();
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, $"EXCEPTION: SalesController > POST > Failed inviting sale user");
            throw;
        }
    }

    [HttpPut("{agencyId:guid}/agent")]
    [Authorize(policy: MyPolicies.SUPER_ADMINS_ONLY)]
    [ProducesResponseType(200)]
    public async Task<IActionResult> UpdateSalesAgent([FromRoute] Guid agencyId, SalesAgentUpdateDTO payload,
        CancellationToken token)
    {
        using var workspan = Workspan.StartEndpoint<SalesAgenciesController>(this, payload, _globalData)
            .LogEnterAndExit();

        try
        {
            if (!ModelState.IsValid)
                return ValidationProblem();


            var salesAgent =
                await _dbContext.SalesAgents.Include(x => x.Contact).Where(x => x.Id == payload.Id)
                    .SingleOrDefaultAsync(token);

            ArgumentNullException.ThrowIfNull(salesAgent);

            salesAgent.Name = payload.Name;
            salesAgent.Contact.Phone = payload.Phone;
            salesAgent.Contact.FirstName = payload.FirstName;
            salesAgent.Contact.LastName = payload.LastName;

            var role = salesAgent.Contact.Role;
            if (payload.Role == SuperAdminGroups.SALESAGENCY_AGENT)
            {
                role = SuperAdminGroups.SALESAGENCY_AGENT;
            }

            if (payload.Role == SuperAdminGroups.SALESAGENCY_ADMIN)
            {
                role = SuperAdminGroups.SALESAGENCY_ADMIN;
            }

            salesAgent.Contact.Role = role;

            var updated = _dbContext.SalesAgents.Update(salesAgent);

            await _dbContext.SaveChangesAsync();

            return Ok();
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, $"EXCEPTION: SalesController > POST > Failed updating sail user");
            throw;
        }
    }

    [HttpGet("{agencyId:guid}/agents")]
    [Authorize(policy: MyPolicies.SUPER_ADMINS_ONLY)]
    [ProducesResponseType(200)]
    public async Task<IActionResult> GetSalesAgents([FromRoute] Guid agencyId, CancellationToken token)
    {
        using var workspan = Workspan.StartEndpoint<SalesAgenciesController>(this, null, _globalData)
            .LogEnterAndExit();

        try
        {
            var salesAgents = await _dbContext
                .SalesAgents
                .Where(x => x.Agency.Id == agencyId)
                .OrderByDescending(x => x.CreatedOn)
                .Include(x => x.Contact)
                .ToListAsync(token);

            return Ok(salesAgents.Select(x => new
            {
                Id = x.Id,
                Name = x.Name,
                Contact = new
                {
                    FirstName = x.Contact?.FirstName,
                    LastName = x.Contact?.LastName,
                    Email = x.Contact?.Email,
                    Phone = x.Contact?.Phone,
                    UserActive = x.Contact?.UserId != Guid.Empty,
                    Role = x.Contact?.Role
                }
            }));
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, $"EXCEPTION: SalesController > GET > Failed getting sales users");
            throw;
        }
    }

    [HttpGet("{agencyId:guid}/agents/{id:guid}")]
    [Authorize(policy: MyPolicies.SUPER_ADMINS_ONLY)]
    [ProducesResponseType(200)]
    public async Task<IActionResult> GetSalesAgent([FromRoute] Guid agencyId, [FromRoute] Guid id,
        CancellationToken token)
    {
        using var workspan = Workspan.StartEndpoint<SalesAgenciesController>(this, null, _globalData)
            .LogEnterAndExit();

        try
        {
            var salesAgent = await _dbContext
                .SalesAgents
                .Where(x => x.Agency.Id == agencyId && x.Id == id)
                .OrderByDescending(x => x.CreatedOn)
                .Include(x => x.Contact)
                .SingleOrDefaultAsync(token);

            if (salesAgent == null) return NotFound("Sales agent not found");

            return Ok(new
            {
                Id = salesAgent.Id,
                Name = salesAgent.Name,
                Contact = new
                {
                    FirstName = salesAgent.Contact?.FirstName,
                    LastName = salesAgent.Contact?.LastName,
                    Email = salesAgent.Contact?.Email,
                    Phone = salesAgent.Contact?.Phone,
                    UserActive = salesAgent.Contact?.UserId != Guid.Empty,
                    Role = salesAgent.Contact?.Role
                }
            });
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, $"EXCEPTION: SalesController > GET > Failed getting sales agent");
            return StatusCode(StatusCodes.Status500InternalServerError, "Failed getting sales agent");
        }
    }
}