using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Merchants.Migrations
{
    public partial class merchantsFieldsUpdates : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "Logo",
                table: "Merchants",
                newName: "Website");

            migrationBuilder.AddColumn<string>(
                name: "Description",
                table: "Merchants",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LogoUrl",
                table: "Merchants",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Type",
                table: "Merchants",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Description",
                table: "Applications",
                type: "text",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Description",
                table: "Merchants");

            migrationBuilder.DropColumn(
                name: "LogoUrl",
                table: "Merchants");

            migrationBuilder.DropColumn(
                name: "Type",
                table: "Merchants");

            migrationBuilder.DropColumn(
                name: "Description",
                table: "Applications");

            migrationBuilder.RenameColumn(
                name: "Website",
                table: "Merchants",
                newName: "Logo");
        }
    }
}
