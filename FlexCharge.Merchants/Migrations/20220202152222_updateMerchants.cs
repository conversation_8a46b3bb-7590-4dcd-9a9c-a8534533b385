using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Merchants.Migrations
{
    public partial class updateMerchants : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AddressLine1",
                table: "Merchants");

            migrationBuilder.RenameColumn(
                name: "ZipCode",
                table: "Merchants",
                newName: "LegalEntityName");

            migrationBuilder.RenameColumn(
                name: "WebsiteUrl",
                table: "Merchants",
                newName: "EcommercePlatform");

            migrationBuilder.RenameColumn(
                name: "State",
                table: "Merchants",
                newName: "Descriptor");

            migrationBuilder.RenameColumn(
                name: "City",
                table: "Merchants",
                newName: "Dba");

            migrationBuilder.RenameColumn(
                name: "AddressLine2",
                table: "Merchants",
                newName: "BusinessEstablishedDate");

            migrationBuilder.AddColumn<bool>(
                name: "Pcidss",
                table: "Merchants",
                type: "boolean",
                nullable: false,
                defaultValue: false);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Pcidss",
                table: "Merchants");

            migrationBuilder.RenameColumn(
                name: "LegalEntityName",
                table: "Merchants",
                newName: "ZipCode");

            migrationBuilder.RenameColumn(
                name: "EcommercePlatform",
                table: "Merchants",
                newName: "WebsiteUrl");

            migrationBuilder.RenameColumn(
                name: "Descriptor",
                table: "Merchants",
                newName: "State");

            migrationBuilder.RenameColumn(
                name: "Dba",
                table: "Merchants",
                newName: "City");

            migrationBuilder.RenameColumn(
                name: "BusinessEstablishedDate",
                table: "Merchants",
                newName: "AddressLine2");

            migrationBuilder.AddColumn<string>(
                name: "AddressLine1",
                table: "Merchants",
                type: "text",
                nullable: true);
        }
    }
}
