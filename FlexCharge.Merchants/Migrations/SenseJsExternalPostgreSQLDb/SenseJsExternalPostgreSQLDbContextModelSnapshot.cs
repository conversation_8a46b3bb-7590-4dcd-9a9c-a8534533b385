// <auto-generated />
using System;
using System.Text.Json;
using FlexCharge.Merchants;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace FlexCharge.Merchants.Migrations.SenseJsExternalPostgreSQLDb
{
    [DbContext(typeof(SenseJsExternalPostgreSQLDbContext))]
    partial class SenseJsExternalPostgreSQLDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "7.0.1")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("FlexCharge.Merchants.Entities.SenseJSMerchantConfiguration", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("ButtonsCornersRadius")
                        .HasColumnType("text");

                    b.Property<string>("CancelUrl")
                        .HasColumnType("text");

                    b.Property<JsonDocument>("CheckoutUrl")
                        .HasColumnType("jsonb");

                    b.Property<int>("ConsentFlowConfiguration")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("createdAt");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("createdBy");

                    b.Property<string>("FontFamily")
                        .HasColumnType("text");

                    b.Property<bool>("IsLogoVisible")
                        .HasColumnType("boolean");

                    b.Property<string>("Logo")
                        .HasColumnType("text");

                    b.Property<Guid>("MerchantId")
                        .HasColumnType("uuid");

                    b.Property<string>("ModalCornersRadius")
                        .HasColumnType("text");

                    b.Property<string>("PrimaryColor")
                        .HasColumnType("text");

                    b.Property<string>("RejectUrl")
                        .HasColumnType("text");

                    b.Property<JsonDocument>("ScrapingData")
                        .HasColumnType("jsonb");

                    b.Property<string>("SecondaryColor")
                        .HasColumnType("text");

                    b.Property<Guid>("SiteId")
                        .HasColumnType("uuid");

                    b.Property<JsonDocument>("SubmitData")
                        .HasColumnType("jsonb");

                    b.Property<string>("SuccessUrl")
                        .HasColumnType("text");

                    b.Property<string>("TokenizationPublicKey")
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updatedAt");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updatedBy");

                    b.HasKey("Id");

                    b.ToTable("configurations");
                });
#pragma warning restore 612, 618
        }
    }
}
