using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Merchants.Migrations
{
    /// <inheritdoc />
    public partial class addsitevalidatedandintegrationguidfields : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsIntegrationGuideEnabled",
                table: "Merchants",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsSiteValidated",
                table: "Merchants",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsSiteValidated",
                table: "Applications",
                type: "boolean",
                nullable: false,
                defaultValue: false);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsIntegrationGuideEnabled",
                table: "Merchants");

            migrationBuilder.DropColumn(
                name: "IsSiteValidated",
                table: "Merchants");

            migrationBuilder.DropColumn(
                name: "IsSiteValidated",
                table: "Applications");
        }
    }
}
