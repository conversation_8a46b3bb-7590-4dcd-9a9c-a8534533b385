using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Merchants.Migrations
{
    /// <inheritdoc />
    public partial class alterordersaddreservecolumns : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "LatestDisputeRate",
                table: "Merchants",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateTable(
                name: "FundsReserveConfigurations",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    DisputeRateMin = table.Column<int>(type: "integer", nullable: false),
                    DisputeRateMax = table.Column<int>(type: "integer", nullable: false),
                    Type = table.Column<string>(type: "text", nullable: true),
                    ReserveRate = table.Column<int>(type: "integer", nullable: false),
                    ReserveRateType = table.Column<string>(type: "text", nullable: true),
                    Period = table.Column<int>(type: "integer", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    ModifiedBy = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FundsReserveConfigurations", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "MerchantFundsReserveConfiguration",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Type = table.Column<string>(type: "text", nullable: true),
                    ReserveRate = table.Column<int>(type: "integer", nullable: false),
                    ReserveRateType = table.Column<string>(type: "text", nullable: true),
                    Period = table.Column<int>(type: "integer", nullable: false),
                    Active = table.Column<bool>(type: "boolean", nullable: false),
                    MerchantId = table.Column<Guid>(type: "uuid", nullable: false),
                    FundsReserveConfigurationId = table.Column<Guid>(type: "uuid", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    ModifiedBy = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MerchantFundsReserveConfiguration", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MerchantFundsReserveConfiguration_FundsReserveConfiguration~",
                        column: x => x.FundsReserveConfigurationId,
                        principalTable: "FundsReserveConfigurations",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_MerchantFundsReserveConfiguration_Merchants_MerchantId",
                        column: x => x.MerchantId,
                        principalTable: "Merchants",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_MerchantFundsReserveConfiguration_FundsReserveConfiguration~",
                table: "MerchantFundsReserveConfiguration",
                column: "FundsReserveConfigurationId");

            migrationBuilder.CreateIndex(
                name: "IX_MerchantFundsReserveConfiguration_MerchantId",
                table: "MerchantFundsReserveConfiguration",
                column: "MerchantId",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "MerchantFundsReserveConfiguration");

            migrationBuilder.DropTable(
                name: "FundsReserveConfigurations");

            migrationBuilder.DropColumn(
                name: "LatestDisputeRate",
                table: "Merchants");
        }
    }
}
