using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Merchants.Migrations
{
    /// <inheritdoc />
    public partial class addaddresstermsepoliciestopartnerstable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "AddressId",
                table: "Partners",
                type: "uuid",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PrivatePolicies",
                table: "Partners",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SpecialTerms",
                table: "Partners",
                type: "text",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Partners_AddressId",
                table: "Partners",
                column: "AddressId");

            migrationBuilder.AddForeignKey(
                name: "FK_Partners_Addresses_AddressId",
                table: "Partners",
                column: "AddressId",
                principalTable: "Addresses",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Partners_Addresses_AddressId",
                table: "Partners");

            migrationBuilder.DropIndex(
                name: "IX_Partners_AddressId",
                table: "Partners");

            migrationBuilder.DropColumn(
                name: "AddressId",
                table: "Partners");

            migrationBuilder.DropColumn(
                name: "PrivatePolicies",
                table: "Partners");

            migrationBuilder.DropColumn(
                name: "SpecialTerms",
                table: "Partners");
        }
    }
}
