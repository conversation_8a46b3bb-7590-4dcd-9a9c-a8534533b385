using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Merchants.Migrations
{
    /// <inheritdoc />
    public partial class alterpartnersfeesaddgroupandorder : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_PartnerFee_Partners_PartnerId",
                table: "PartnerFee");

            migrationBuilder.DropPrimaryKey(
                name: "PK_PartnerFee",
                table: "PartnerFee");

            migrationBuilder.RenameTable(
                name: "PartnerFee",
                newName: "PartnerFees");

            migrationBuilder.RenameIndex(
                name: "IX_PartnerFee_PartnerId",
                table: "PartnerFees",
                newName: "IX_PartnerFees_PartnerId");

            migrationBuilder.AddColumn<int>(
                name: "ApplyOrder",
                table: "PartnerFees",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Group",
                table: "PartnerFees",
                type: "text",
                nullable: true);

            migrationBuilder.AddPrimaryKey(
                name: "PK_PartnerFees",
                table: "PartnerFees",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_PartnerFees_Partners_PartnerId",
                table: "PartnerFees",
                column: "PartnerId",
                principalTable: "Partners",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_PartnerFees_Partners_PartnerId",
                table: "PartnerFees");

            migrationBuilder.DropPrimaryKey(
                name: "PK_PartnerFees",
                table: "PartnerFees");

            migrationBuilder.DropColumn(
                name: "ApplyOrder",
                table: "PartnerFees");

            migrationBuilder.DropColumn(
                name: "Group",
                table: "PartnerFees");

            migrationBuilder.RenameTable(
                name: "PartnerFees",
                newName: "PartnerFee");

            migrationBuilder.RenameIndex(
                name: "IX_PartnerFees_PartnerId",
                table: "PartnerFee",
                newName: "IX_PartnerFee_PartnerId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_PartnerFee",
                table: "PartnerFee",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_PartnerFee_Partners_PartnerId",
                table: "PartnerFee",
                column: "PartnerId",
                principalTable: "Partners",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
