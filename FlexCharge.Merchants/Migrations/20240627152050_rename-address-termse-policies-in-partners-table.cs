using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Merchants.Migrations
{
    /// <inheritdoc />
    public partial class renameaddresstermsepoliciesinpartnerstable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "SpecialTerms",
                table: "Partners",
                newName: "TermsAndConditionsUrl");

            migrationBuilder.RenameColumn(
                name: "PrivatePolicies",
                table: "Partners",
                newName: "PrivacyPolicyUrl");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "TermsAndConditionsUrl",
                table: "Partners",
                newName: "SpecialTerms");

            migrationBuilder.RenameColumn(
                name: "PrivacyPolicyUrl",
                table: "Partners",
                newName: "PrivatePolicies");
        }
    }
}
