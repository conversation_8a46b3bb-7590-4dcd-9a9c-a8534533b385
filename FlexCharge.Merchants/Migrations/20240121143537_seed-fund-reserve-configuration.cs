using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Merchants.Migrations
{
    /// <inheritdoc />
    public partial class seedfundreserveconfiguration : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.InsertData(
                table: "FundsReserveConfigurations",
                columns: new[] { "Id", "DisputeRateMin", "DisputeRateMax", "Type", "ReserveRate", "ReserveRateType", "Period", "IsDeleted", "CreatedOn", "ModifiedOn", "CreatedBy", "ModifiedBy" },
                values: new object[,]
                {
                    { new Guid("309bc8bd-894b-4015-abe5-fbfc4bcdc841"), 1, 4, "2", 1000, "1", 60, false, new DateTime(2024, 1, 21, 16, 22, 5, 340, DateTimeKind.Utc), new DateTime(2024, 1, 21, 16, 22, 6, 75, DateTimeKind.Utc), null, null },
                    { new Guid("fbda3ac5-0e70-4644-911e-85bdb1e937e7"), 4, 6, "2", 1500, "1", 60, false, new DateTime(2024, 1, 21, 16, 22, 5, 340, DateTimeKind.Utc), new DateTime(2024, 1, 21, 16, 22, 6, 75, DateTimeKind.Utc), null, null },
                    { new Guid("2a209139-d472-4325-8f5c-538f47b609a9"), 0, 1, "2", 0, "1", 60, false, new DateTime(2024, 1, 21, 16, 22, 5, 340, DateTimeKind.Utc), new DateTime(2024, 1, 21, 16, 22, 6, 75, DateTimeKind.Utc), null, null },
                    { new Guid("86d9c098-15ae-4a37-849d-************"), 6, 8, "2", 2500, "1", 60, false, new DateTime(2024, 1, 21, 16, 22, 5, 340, DateTimeKind.Utc), new DateTime(2024, 1, 21, 16, 22, 6, 75, DateTimeKind.Utc), null, null }
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {

        }
    }
}
