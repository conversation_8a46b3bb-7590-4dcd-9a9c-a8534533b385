using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using FlexCharge.Common.Authentication;
using FlexCharge.Merchants.DTO;
using FlexCharge.Merchants.Entities;
using FlexCharge.Merchants.Enums;
using FlexCharge.Common.GeoServices;
using FlexCharge.Contracts;
using FlexCharge.Merchants.Controllers;
using FlexCharge.Utils;
using MassTransit;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Twilio.Jwt.AccessToken;
using Twilio.TwiML.Voice;
using Task = System.Threading.Tasks.Task;

namespace FlexCharge.Merchants.Services.SalesAgencyServices;

public interface ISalesService
{
    Task<SalesAgency> CreateSalesAgencyAsync(SalesAgencyCreateDTO payload);
    Task<SalesAgency> UpdateSalesAgencyAsync(SalesAgencyUpdateDTO payload);
    Task<DeleteSalesAgencyDTO> DeleteSalesAgencyAsync(Guid id);
    Task DeleteSalesAgentAsync(Guid id);
}