using FlexCharge.Eligibility.SftpGateway.Entities;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Npgsql;
using System;
using System.Linq;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;

namespace FlexCharge.Eligibility.SftpGateway
{
    public class PostgreSQLDbContext : DbContext
    {
        private readonly IHttpContextAccessor _httpContextAccessor;

        public PostgreSQLDbContext(DbContextOptions<PostgreSQLDbContext> options,
            IHttpContextAccessor httpContextAccessor)
            : base(options)
        {
            _httpContextAccessor = httpContextAccessor;
        }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            // builder.Entity<Activity>().Property<bool>("IsDeleted");
            // builder.Entity<Activity>().HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);

            builder.Entity<Merchant>().Property<bool>("IsDeleted");
            builder.Entity<Merchant>().HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);


            builder.Entity<Batch>().Property<bool>("IsDeleted");
            builder.Entity<Batch>().HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);

            builder.Entity<BatchRequest>().Property<bool>("IsDeleted");
            builder.Entity<BatchRequest>().HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);
        }

        // public DbSet<Activity> Activities { get; set; }

        public DbSet<Merchant> Merchants { get; set; }

        public DbSet<Batch> Batches { get; set; }
        public DbSet<BatchRequest> BatchRequests { get; set; }

        public override int SaveChanges()
        {
            UpdateSoftDeleteStatuses();
            return base.SaveChanges();
        }

        public override async Task<int> SaveChangesAsync(bool acceptAllChangesOnSuccess,
            CancellationToken cancellationToken = default(CancellationToken))
        {
            UpdateSoftDeleteStatuses();
            return await base.SaveChangesAsync(acceptAllChangesOnSuccess, cancellationToken);
        }

        private void UpdateSoftDeleteStatuses()
        {
            var user = _httpContextAccessor?.HttpContext?.User?.Claims.SingleOrDefault(x =>
                x.Type == ClaimTypes.NameIdentifier);
            foreach (var entry in ChangeTracker.Entries())
            {
                switch (entry.State)
                {
                    case EntityState.Modified:
                    {
                        if (user != null)
                        {
                            if (entry.Properties.Any(o => o.Metadata.Name == "ModifiedBy"))
                                entry.Property("ModifiedBy").CurrentValue = user.Value;
                        }


                        if (entry.Properties.Any(o => o.Metadata.Name == "ModifiedOn"))
                            entry.Property("ModifiedOn").CurrentValue = DateTime.Now.ToUniversalTime();
                        entry.CurrentValues["IsDeleted"] = false;
                        break;
                    }
                    case EntityState.Added:
                    {
                        if (user != null)
                        {
                            if (entry.Properties.Any(o => o.Metadata.Name == "CreatedBy"))
                                entry.Property("CreatedBy").CurrentValue = user.Value;
                            if (entry.Properties.Any(o => o.Metadata.Name == "ModifiedBy"))
                                entry.Property("ModifiedBy").CurrentValue = user.Value;
                        }

                        if (entry.Properties.Any(o => o.Metadata.Name == "CreatedOn"))
                            entry.Property("CreatedOn").CurrentValue = DateTime.Now.ToUniversalTime();
                        if (entry.Properties.Any(o => o.Metadata.Name == "ModifiedOn"))

                            entry.Property("ModifiedOn").CurrentValue = DateTime.Now.ToUniversalTime();
                        entry.CurrentValues["IsDeleted"] = false;
                        break;
                    }

                    case EntityState.Deleted:
                        entry.State = EntityState.Modified;
                        entry.CurrentValues["IsDeleted"] = true;
                        break;
                }
            }
        }
    }
}