using System.Security.AccessControl;

namespace FlexCharge.Contracts;

public class Transaction
{
    public string ParentId { get; set; }
    public string DynamicDescriptor { get; set; }

    public string Id { get; set; }
    public string ExternalReference { get; set; }

    public DateTime TimestampUtc { get; set; }
    public int TimezoneUtcOffset { get; set; }

    public string TransactionType { get; set; }

    public int Amount { get; set; }
    public string Currency { get; set; }

    public string ResponseCode { get; set; }
    string ResponseDescription { get; set; }

    public string ResponseStatus { get; set; }
    public string ResponseSubStatus { get; set; }

    //public string Status { get; set; }
    
    /// <summary>
    /// This is the source of the code from the original transaction ( Example: "Paypal" )
    /// in Case this value cannot be provided just leave this as empty string
    /// </summary>
    public string ResponseCodeSource { get; set; }

    public string ProcessorName { get; set; }

    public string AvsResultCode { get; set; }
    public string CvvResultCode { get; set; }

    public string CavvResultCode { get; set; }
    //public bool IsRecurring { get; set; }

    public bool CardNotPresent { get; set; }
}