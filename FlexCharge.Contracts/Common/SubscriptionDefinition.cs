namespace FlexCharge.Contracts;

public record SubscriptionDefinition(
    string Id,
    DateTime Created,
    string Currency,
    string Description,
    DateTime BillingCycleAnchor,
    Dictionary<string, string> Metadata,
    SubscriptionDefinition.SubscriptionBillingCycleAnchorConfig? BillingCycleAnchorConfig,
    DateTime? CancelAt,
    bool CancelAtPeriodEnd,
    DateTime CurrentPeriodEnd,
    DateTime CurrentPeriodStart,
    DateTime? NextPendingInvoiceItemInvoice,
    SubscriptionDefinition.SubscriptionPendingInvoiceItemInterval? PendingInvoiceItemInterval,
    DateTime StartDate,
    DateTime? TrialEnd,
    DateTime? TrialStart
)
{
    public record SubscriptionTrialSettings
    {
    }

    public record SubscriptionPendingInvoiceItemInterval(
        string Interval,
        int IntervalCount
    );

    public record SubscriptionBillingCycleAnchorConfig(
        int DayOfMonth,
        int? Hour,
        int? Minute,
        int? Month,
        int? Second
    );
}