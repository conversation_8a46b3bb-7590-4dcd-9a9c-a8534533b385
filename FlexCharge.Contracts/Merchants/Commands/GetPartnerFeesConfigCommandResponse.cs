namespace FlexCharge.Contracts.Commands;

public record GetPartnerFeesConfigCommandResponse : IdempotentCommand
{
    public List<PartnerFeeModel> Fees { get; set; }

    public record PartnerFeeModel
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public string Type { get; set; }
        public string AppliedOn { get; set; }
        public int Amount { get; set; }
        public bool IsActive { get; set; } = true;
        public int? MinimumFeeAmount { get; set; }
        public string Group { get; set; }
        public int? ApplyOrder { get; set; }
        public Guid PartnerId { get; set; }
    }
}