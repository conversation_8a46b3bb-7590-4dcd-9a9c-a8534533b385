namespace FlexCharge.Contracts;

public record MerchantSiteCreatedEvent
{
    public Guid Mid { get; set; }
    public Guid SiteId { get; set; }
    public string Name { get; set; }
    public string Descriptor { get; set; }
    public string DescriptorCity { get; set; }
    public string CustomerSupportName { get; set; }
    public string CustomerSupportEmail { get; set; }
    public string CustomerSupportPhone { get; set; }
    public string CustomerSupportLink { get; set; }

    public IEnumerable<Uri> WhitelistedUrls { get; set; }
    public IEnumerable<string> Tags { get; set; }
    public string State { get; set; }
}