using FlexCharge.Contracts.Commands;

namespace FlexCharge.Contracts;

public abstract record PaymentDebitEventBase : IdempotentEvent //, IPaymentEventBase
{
    public Guid OrderId { get; set; }
    public Guid TransactionId { get; set; }
    public Guid Mid { get; set; }

    public Guid? PaymentInstrumentId { get; set; }

    //public Guid SupportedGatewayId { get; set; }
    public string TransactionType { get; set; }

    public string PaymentMethodType { get; set; }
    public string Descriptor { get; set; }
    public string Description { get; set; }
    public string AmountFormatted { get; set; }
    public int Amount { get; set; }
    public int? AuthorizationAmount { get; set; }
    public int DiscountAmount { get; set; }
    public int? FeeAmount { get; set; }

    public DateTime PaymentDate { get; set; }
    public Guid? ProviderId { get; set; }
    public Guid SupportedGatewayId { get; set; }
    public string Provider { get; set; }
    public string ProviderResponseCode { get; set; }
    public string ProviderResponseDescription { get; set; }

    public string CvvCode { get; set; }
    public string CvvMessage { get; set; }

    public string AvsCode { get; set; }
    public string AvsMessage { get; set; }


    public string Processor { get; set; }
    public string ProcessorId { get; set; }
    public string ProcessorResponseCode { get; set; }
    public string ProcessorResponseDescription { get; set; }

    public string ProviderTransactionToken { get; set; }
    public string InternalResponseCode { get; set; }
    public string InternalResponseMessage { get; set; }
    public string InternalResponseGroup { get; set; }

    public bool NetworkTokenUsed { get; set; }
    public string Bin { get; set; }
    public string Last4 { get; set; }

    public bool AccountUpdaterUsed { get; set; }
}