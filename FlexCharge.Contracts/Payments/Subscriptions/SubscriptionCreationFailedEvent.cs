using FlexCharge.Contracts.Commands;

namespace FlexCharge.Contracts;

public record SubscriptionCreationFailedEvent : IdempotentEvent
{
    public Guid SubscriptionId { get; set; }
    public Guid OrderId { get; set; }
    public Guid Mid { get; set; }
    public Guid? ProviderId { get; set; }
    public string Provider { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime? CancelAt { get; set; }
    public int Amount { get; set; }
    public bool NetworkTokenUsed { get; set; }
}