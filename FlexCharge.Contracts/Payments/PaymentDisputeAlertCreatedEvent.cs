using FlexCharge.Contracts.Commands;

namespace FlexCharge.Contracts;

/// <summary>
/// Create new dispute alert event on a payment 
/// </summary>
public record PaymentDisputeAlertCreatedEvent : IdempotentEvent
{
    public Guid Mid { get; set; }
    public Guid OrderId { get; set; }

    public Guid TransactionId { get; set; }

    public Guid DisputeId { get; set; }

    public string? Description { get; set; }
    public int Amount { get; set; }
    public int DiscountAmount { get; set; }
    public DateTime Date { get; set; }

    public string Type { get; set; }
    public string Bin { get; set; }
    public string Last4 { get; set; }
}