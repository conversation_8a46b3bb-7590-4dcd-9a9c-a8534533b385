using FlexCharge.Contracts.Commands;

namespace FlexCharge.Contracts;

/// <summary>
/// When a RDR/Ethoca is notifiying us about an existing dispute/chargeback
/// There is no automatic refund so this event is fired after we have manually refunded the order
/// </summary>
public record PaymentPreDisputeCreditedEvent : IdempotentEvent
{
    public Guid Mid { get; set; }
    public Guid Pid { get; set; }
    public Guid OrderId { get; set; }
    public Guid DisputeId { get; set; }

    public Guid TransactionId { get; set; }
    public string Type { get; set; }

    public string Description { get; set; }
    public int Amount { get; set; }
    public int DiscountAmount { get; set; }
    public DateTime RequestDate { get; set; }
    public DateTime PaymentDate { get; set; }
    public DateTime? CreatedDateTime { get; set; }
    public bool EarlyFraudWarning { get; set; }
    public string Reason { get; set; }
    public Guid InitialTransactionReference { get; set; }
    public string Currency { get; set; }
}