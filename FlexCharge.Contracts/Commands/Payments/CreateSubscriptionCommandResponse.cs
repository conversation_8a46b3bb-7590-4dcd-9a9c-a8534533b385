namespace FlexCharge.Contracts.Commands;

public record CreateSubscriptionCommandResponse
{
    public bool Success { get; set; }
    public string ResponseCode { get; set; }
    public string ResponseMessage { get; set; }
    public Guid SubscriptionId { get; set; }
    public bool GatewayFound { get; set; }

    public int GatewayOrder { get; set; }
    public string Provider { get; set; }
    public string ProviderResponseCode { get; set; }

    public int? NextGateway { get; set; }
    public IList<string> Errors { get; set; }
    public IList<KeyValuePair<string, string>> ErrorsWithCodes { get; set; }
}