using FlexCharge.Contracts.Commands.Common;
using FlexCharge.Contracts.Common;
using FlexCharge.Contracts.Vault;

namespace FlexCharge.Contracts.Commands;

public record CreateSubscriptionCommand : IdempotentCommand
{
    public Guid Mid { get; set; }
    public Guid OrderId { get; set; }
    public Guid? PayerId { get; set; }
    public string PayerEmail { get; set; }
    public string PaymentInstrumentToken { get; set; }
    public DateTime StartDate { get; set; }

    /// <summary>
    /// Caution: using this property can cause a proration of the subscription amount
    /// </summary>
    public DateTime? CancelAt { get; set; }


    public IntervalUnits IntervalUnit { get; set; }
    public int IntervalCount { get; set; }

    /// <summary>
    /// Used when payment is authenticated with 3DS
    /// </summary>
    public string? ScaAuthenticationToken { get; set; }

    public int Amount { get; set; }
    public string Currency { get; set; }
    public bool UseDynamicDescriptor { get; set; }

    /// <summary>
    /// Not in use (double check)
    /// </summary>
    public string? DescriptorOverride { get; set; }

    /// <summary>
    /// Not in use (double check)
    /// </summary>
    public string? DescriptorCityOverride { get; set; }

    public int? GatewayOrder { get; set; }
    public List<string> BlocklistedProviders { get; set; }

    public bool PerformCascadingPayment { get; set; } = false;

    public string ProcessorId { get; set; }
    public bool? IsRebilling { get; set; }

    /// <summary>
    /// Overrides payment processor's duplicate order check time span.
    /// Value is in seconds.
    /// </summary>
    /// <remarks>Processors can deny orders with the same card and amount within this time interval.
    /// NMI returns error 300 in this case</remarks>
    public int? OverrideDuplicateOrderCheckTimespan { get; set; }

    public BillingAddress BillingAddress { get; set; }

    public bool UseBillingAsShipping { get; set; }
    public ShippingAddress? ShippingAddress { get; set; }

    public Descriptor Descriptor { get; set; }

    //public DeviceDetails Device { get; set; }

    public ThreeDS? ThreeDs { get; set; }

    public AccountUpdaterSettings AccountUpdaterSettings { get; set; }

    public bool IsCit { get; set; }

    public DeviceInfo? DeviceInformation { get; set; }

    public bool TryUseAccountUpdater { get; set; }


    public enum IntervalUnits
    {
        Day,
        Week,
        Month,
        Year
    }
}