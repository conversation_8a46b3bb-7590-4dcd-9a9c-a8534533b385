namespace FlexCharge.Contracts.Commands;

public record VerifyAmountPaymentCommandResponse
{
    public bool Success { get; set; }
    public string ResponseCode { get; set; }
    public string ResponseMessage { get; set; }
    public string BinNumber { get; set; }
    public Guid TransactionId { get; set; }
    public bool GatewayFound { get; set; }
    public string Provider { get; set; }
    public string ProviderResponseCode { get; set; }

    public string InternalResponseCode { get; set; }
    public string InternalResponseMessage { get; set; }
    public string InternalResponseGroup { get; set; }

    public string CvvCode { get; set; }
    public string AvsCode { get; set; }
    public bool NetworkTokenUsed { get; set; }
}