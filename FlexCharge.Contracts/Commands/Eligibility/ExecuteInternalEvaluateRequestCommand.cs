namespace FlexCharge.Contracts.Commands;

public record ExecuteInternalEvaluateRequestCommand : IdempotentCommand
{
    public Guid RequestId { get; init; }

    public Guid MerchantId { get; init; }
    public Guid? PartnerId { get; init; }
    public string Request { get; init; }

    public bool PartnerIdUnknown { get; init; } = false;

    /// <summary>
    /// If order is processing on another system, it's account id in this system.
    /// For example, when recycling Stripe MIT orders
    /// </summary>
    public string? ExternalAccountId { get; init; }

    public string? ExternalSubscriptionService { get; init; }
    public SubscriptionDefinition? SubscriptionDefinition { get; set; }

    public Guid? ForwardedPaymentInstrumentToCreateId { get; set; }
}