using System.ComponentModel.DataAnnotations;
using FlexCharge.Contracts.Common;

namespace FlexCharge.Contracts;

public record TransmitRecordEvent
{
    public MerchantDT0? Merchant { get; set; }

    public BillingInformationDTO? BillingInformation { get; set; }
    public PaymentMethodLast4DigitsDTO PaymentMethod { get; set; }

    [Required] public bool? IsDeclined { get; set; }


    public Guid? Mid { get; set; }

    [Required(ErrorMessage = "Merchant's orderId is required")]
    public string OrderId { get; set; } //External order Id

    public string? SiteUrl { get; set; }

    public string? OrderSource { get; set; }
    [Required] public TransactionDTO Transaction { get; set; }
    [Required] public PayerDTO Payer { get; set; }
    public List<OrderItemDTO>? OrderItems { get; set; }
    public ShippingInformationDTO? ShippingInformation { get; set; }

    /// <summary>
    /// Is Merchant-Initiated Transaction?
    /// </summary>
    public bool? IsMIT { get; set; }

    //[RequiredIf(nameof(IsMIT), true)]
    public bool? IsRecurring { get; set; }

    //[RequiredIf(nameof(IsRecurring), true)]
    public SubscriptionDTO Subscription { get; set; }

    public List<AdditionalFieldDTO> AdditionalFields { get; set; }


    public class MerchantDT0
    {
        public string Name { get; set; }

        public string Id { get; set; }
        public string Mcc { get; set; }

        public string Country { get; set; }
    }

    public class BillingInformationDTO
    {
        public string FirstName { get; set; }
        public string LastName { get; set; }

        public string Phone { get; set; }
        public string Country { get; set; }
        public string CountryCode { get; set; }
        public string AddressLine1 { get; set; }
        public string AddressLine2 { get; set; }
        public string State { get; set; }
        public string City { get; set; }
        public string Zipcode { get; set; }
    }

    public class PaymentMethodLast4DigitsDTO
    {
        [Required] public string CardBinNumber { get; set; }
        [Required] public string CardLast4Digits { get; set; }

        [Required] public string HolderName { get; set; }

        [Required] public string CardType { get; set; } //Credit/Debit/Prepaid Card
        [Required] public string CardBrand { get; set; } //VISA, Mastercard, AMEX, etc.

        /// <summary>
        /// Country Code
        /// </summary>
        /// <remarks>ISO 3166-1 alpha-2 country code (2-letter)</remarks>
        [Required]
        public string CardCountry { get; set; }

        public string? CardIssuer { get; set; }
        public string? CardLevel { get; set; }
        public string? CardFingerprint { get; set; }

        [Required] [Range(1, 12)] public int ExpirationMonth { get; set; }

        [Required]
        [Range(1, 2100, ErrorMessage = "Expiration year is incorrect")]
        public int ExpirationYear { get; set; }
    }

    public class TransactionDTO
    {
        public string ParentId { get; set; }
        public string DynamicDescriptor { get; set; }

        public string Id { get; set; }
        public string ExternalReference { get; set; }

        public DateTime TimestampUtc { get; set; }
        public int TimezoneUtcOffset { get; set; }

        public string TransactionType { get; set; }

        public int Amount { get; set; }
        public string Currency { get; set; }

        public string ResponseCode { get; set; }
        string ResponseDescription { get; set; }

        public string ResponseStatus { get; set; }
        public string ResponseSubStatus { get; set; }

        //public string Status { get; set; }

        /// <summary>
        /// This is the source of the code from the original transaction ( Example: "Paypal" )
        /// in Case this value cannot be provided just leave this as empty string
        /// </summary>
        public string ResponseCodeSource { get; set; }

        public string ProcessorName { get; set; }

        public string AvsResultCode { get; set; }
        public string CvvResultCode { get; set; }

        public string CavvResultCode { get; set; }
        //public bool IsRecurring { get; set; }

        public bool CardNotPresent { get; set; }
    }

    public class PayerDTO
    {
        public string Id { get; set; }

        public string ExternalId { get; set; }

        //public string FirstName { get; set; }
        //public string LastName { get; set; }
        //public string MiddleName { get; set; }
        public DateTime Birthdate { get; set; }
        public string Email { get; set; }
        public string Phone { get; set; }
    }

    public class OrderItemDTO
    {
        public string Sku { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public int Amount { get; set; }
        public int DiscountAmount { get; set; }
        public int Tax { get; set; }
        public string DiscountType { get; set; }
        public int Quantity { get; set; }
    }

    public class ShippingInformationDTO
    {
        public string FirstName { get; set; }
        public string LastName { get; set; }

        public string Phone { get; set; }

        public string Country { get; set; }
        public string CountryCode { get; set; }

        public string AddressLine1 { get; set; }
        public string AddressLine2 { get; set; }
        public string State { get; set; }
        public string City { get; set; }

        public string Zipcode { get; set; }
    }

    public class SubscriptionDTO
    {
        public string SubscriptionId { get; set; }
        public string Interval { get; set; }
        public string Price { get; set; }
        public string Currency { get; set; }
        public string PaymentNumber { get; set; }
        public string TotalPayments { get; set; }
    }

    public class AdditionalFieldDTO
    {
        public string Key { get; set; }
        public string Value { get; set; }
    }
}