using FlexCharge.Contracts.Commands;

namespace FlexCharge.Contracts;

public record ExternalProviderCancelSubscriptionCommand : IdempotentCommand, ICorrelatedMessage
{
    Guid? ICorrelatedMessage.MessageCorrelationId => OrderId;
    Guid? ICorrelatedMessage.MessageTenantId => Mid;

    public Guid OrderId { get; set; }
    public Guid Mid { get; set; }

    public bool CanCancelActiveSubscription { get; set; } = false;

    public string ExternalAccountId { get; set; }
}