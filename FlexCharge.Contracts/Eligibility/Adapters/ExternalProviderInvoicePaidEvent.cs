namespace FlexCharge.Contracts;

/// <summary>
/// 
/// </summary>
/// <param name="PaidOutOfBand"></param>
/// <param name="Mid"></param>
/// <param name="OrderId"></param>
/// <param name="InvoiceId"></param>
/// <param name="Amount"></param>
/// <param name="RescuedByFlex">True, if paid on external payment provider without external token charge from our side</param>
public record ExternalProviderInvoicePaidEvent(
    bool PaidOutOfBand,
    Guid Mid,
    Guid OrderId,
    string InvoiceId,
    int Amount,
    bool RescuedByFlex
)
    : IdempotentEvent, ICorrelatedMessage
{
    Guid? ICorrelatedMessage.MessageCorrelationId => OrderId;
    Guid? ICorrelatedMessage.MessageTenantId => Mid;
}