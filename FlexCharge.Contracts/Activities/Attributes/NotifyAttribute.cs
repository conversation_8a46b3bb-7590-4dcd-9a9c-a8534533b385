namespace FlexCharge.Common.Activities.Attributes;

public enum NotificationLevel
{
    Information,
    Warning,
    Error,
    Fatal
}

public enum NotificationRecipients
{
    None,
    RnD,
    Operations,
    Analytics,
}

public enum NotificationChannel
{
    Log
}

[AttributeUsage(AttributeTargets.Field, Inherited = false, AllowMultiple = true)]
public class NotifyAttribute : Attribute
{
    public NotificationLevel Level { get; }

    public NotificationRecipients[] Recipients { get; }

    public NotificationChannel Channel { get; set; } = NotificationChannel.Log;

    public string[] Tags { get; set; }

    public NotifyAttribute(NotificationLevel level, params NotificationRecipients[] recipients)
    {
        Level = level;
        Recipients = recipients;
    }
}