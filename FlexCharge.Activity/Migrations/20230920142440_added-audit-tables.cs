using Microsoft.EntityFrameworkCore.Migrations;
using System;

#nullable disable

namespace FlexCharge.Activity.Migrations
{
    /// <inheritdoc />
    public partial class addedaudittables : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "ParentId",
                table: "AggregatedActivities",
                type: "uuid",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "AuditedCorrelationErrors",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CorrelationId = table.Column<Guid>(type: "uuid", nullable: false),
                    Mid = table.Column<Guid>(type: "uuid", nullable: false),
                    Severity = table.Column<int>(type: "integer", nullable: false),
                    Message = table.Column<string>(type: "text", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    ModifiedBy = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AuditedCorrelationErrors", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "EmailsSent",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    SentAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    FirstErrorTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    LastErrorTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    ModifiedBy = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EmailsSent", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "PendingAudits",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    RuleSet = table.Column<string>(type: "text", nullable: true),
                    CorrelationId = table.Column<Guid>(type: "uuid", nullable: true),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: true),
                    IsRollingTime = table.Column<bool>(type: "boolean", nullable: false),
                    NextAuditTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    NextAuditTimeOrLastSeen = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    LastAuditStartTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CheckPoint = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    ModifiedBy = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PendingAudits", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_AuditedCorrelationErrors_CorrelationId_Message",
                table: "AuditedCorrelationErrors",
                columns: new[] { "CorrelationId", "Message" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_EmailsSent_SentAt",
                table: "EmailsSent",
                column: "SentAt");

            migrationBuilder.CreateIndex(
                name: "IX_PendingAudits_RuleSet_CorrelationId_TenantId_IsRollingTime",
                table: "PendingAudits",
                columns: new[] { "RuleSet", "CorrelationId", "TenantId", "IsRollingTime" },
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AuditedCorrelationErrors");

            migrationBuilder.DropTable(
                name: "EmailsSent");

            migrationBuilder.DropTable(
                name: "PendingAudits");

            migrationBuilder.DropColumn(
                name: "ParentId",
                table: "AggregatedActivities");
        }
    }
}
