using FlexCharge.Common;
using FlexCharge.Common.Authentication;
using FlexCharge.Common.PostgreSql;
using FlexCharge.Common.Swagger;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using FlexCharge.Activity.Services.AggregatedActivities;
using FlexCharge.Activity.Services.InternalReporting;
using FlexCharge.Activity.Services.InternalReporting.Reports;
using FlexCharge.Activity.Services.Partitions;
using FlexCharge.Common.Activities;
using FlexCharge.Common.Athena;
using FlexCharge.Common.BackgroundJobs;
using FlexCharge.Common.Cache;
using FlexCharge.Common.DataBase;
using FlexCharge.Common.DataStreaming;
using FlexCharge.Common.Dependencies;
using FlexCharge.Common.DistributedLock.Implementations.RedLock;
using FlexCharge.Common.Emails;
using FlexCharge.Common.Telemetry;
using FlexCharge.Common.Telemetry.PerformanceCounters;
using FlexCharge.Common.MassTransit;
using FlexCharge.Common.Partitions;
using NRules.Integration.AspNetCore;

namespace FlexCharge.Activity
{
    public class Startup
    {
        public static EventWaitHandle StartupCompleted { get; } = new(false, EventResetMode.ManualReset);

        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            services.AddTelemetry();
            services.AddCloudWatchPerformanceCountersTelemetry<Startup>();

            services.AddNRules(new[] {typeof(Startup).Assembly});

            services.AddTransient<IHttpContextAccessor, HttpContextAccessor>();
            services.AddTransient(typeof(IOptions<>), typeof(OptionsManager<>));
            services.AddTransient<IAggregatedActivitiesService, AggregatedActivitiesService>();

            services.Configure<AppOptions>(Configuration.GetSection("app"));
            services.AddOptions();

            services.Configure<TablePartitionOptions>(Configuration.GetSection(TablePartitionOptions.SectionName));

            services.AddScoped<ITablePartitionService, ActivityTablePartitionService>();


            string connectionString =
                $@"Host={Environment.GetEnvironmentVariable("DB_HOST")};Port={Environment.GetEnvironmentVariable("DB_PORT")};Database={Environment.GetEnvironmentVariable("DB_DATABASE")};Username={Environment.GetEnvironmentVariable("DB_USERNAME")};Password='{Environment.GetEnvironmentVariable("DB_PASSWORD")}';";

#if DEBUG
            connectionString = "Host=localhost;Database=fc.activity;Username=activity-service-staging;Password=*****";
#endif

            services.AddEntityFrameworkNpgsql()
                .AddNpgsqlDbContext<PostgreSQLDbContext>(connectionString)
                .AddNpgsqlDbContext<ReadOnlyPostgreSQLDbContext>(
                    ReadOnlyConnectionString.MakeReadOnly(connectionString));
            //.AddNpgsqlDbContext<PostgreSQLDbContext>("Host=localhost;Database=fc.merchants;Username=merchant-service-staging;Password=*****");

            #region Authorization Configuration

            services.AddJwt();

            services.AddAuthorization(options =>
            {
                options.AddPolicy(MyPolicies.SUPER_ADMINS_ONLY,
                    policy => policy.RequireClaim(MyClaimTypes.COGNITO_GROUP, SuperAdminGroups.SUPER_ADMIN));
                options.AddPolicy(MyPolicies.ADMINS_AND_PARTNER_ADMINS_AND_MERCHANT_ADMINS,
                    policy =>
                    {
                        policy.RequireClaim(MyClaimTypes.COGNITO_GROUP,
                            SuperAdminGroups.SUPER_ADMIN,
                            SuperAdminGroups.MERCHANT_ADMIN,
                            SuperAdminGroups.INTEGRATION_PARTNER_ADMIN,
                            SuperAdminGroups.PARTNER_ADMIN);
                    });
                options.AddPolicy(MyPolicies.ADMINS_AND_ALL_MERCHANTS_AND_PARTNER_ADMINS,
                    policy =>
                    {
                        policy.RequireClaim(MyClaimTypes.COGNITO_GROUP,
                            SuperAdminGroups.SUPER_ADMIN,
                            SuperAdminGroups.PARTNER_ADMIN,
                            SuperAdminGroups.INTEGRATION_PARTNER_ADMIN,
                            MerchantGroups.MERCHANT_ADMIN,
                            MerchantGroups.MERCHANT_SUPPORT,
                            MerchantGroups.MERCHANT_SUPPORT_ADMIN,
                            MerchantGroups.MERCHANT_FINANCE,
                            MerchantGroups.MERCHANT_DEVELOPER);
                    });
            });

            #endregion

            services.AddAutoMapper(typeof(Startup));
            services.AddControllers();
            services.AddSwaggerDocs();

            services.AddTransient<IEmailSender, SendGridEmailSender>();
            services.AddTransient<IActivityService, ActivityService>();
            services.AddTransient<IInternalReportingService, InternalReportingService>();

            services.AddMassTransit<Startup>();

            services.AddCors(options =>
            {
                options.AddPolicy("CorsPolicy", cors =>
                    cors.AllowAnyMethod()
                        .AllowAnyOrigin()
                        .AllowAnyHeader());
            });

            services.AddEmailClient();
            services.AddDataStreamingClient();

            services.AddRedisCache();
            services.AddBigPayloadSupport();
            services.AddRedLockDistributedLock();

            services.AddBackgroundWorkerService(Configuration);
            services.AddEmailClient();

            services.AddAthenaDB();
            services.AddAthenaDBOptions<AggregatedActivitiesAthenaDBOptions>("AggregatedActivitiesAthenaDB");


            #region Internal Reports

            services.PopulateAndAddTransients<IInternalReport>();
            services.AddTransient<ServiceCollectionExtensions.InternalReportsResolver>(serviceProvider => reportId =>
            {
                var internalReports = serviceProvider.GetServices<IInternalReport>();

                var internalReport = internalReports.FirstOrDefault(x => x.ReportId == reportId);
                if (internalReport is null) throw new KeyNotFoundException($"Internal report {reportId} not found");

                return internalReport;
            });

            #endregion
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IHostApplicationLifetime applicationLifetime,
            IWebHostEnvironment env)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
                // Enable middleware to serve generated Swagger as a JSON endpoint.
            }

            app.UseCors("CorsPolicy");

            app.UseNRules();

            //app.UseHttpsRedirection();
            app.UseSwaggerDocs();

            app.UseRouting();
            app.UseAuthorization();
            app.UseAutoMigrations<PostgreSQLDbContext>();

            app.UseEndpoints(endpoints => { endpoints.MapControllers(); });

            app.UseMassTransit();

            StartupCompleted.Set();
        }
    }
}