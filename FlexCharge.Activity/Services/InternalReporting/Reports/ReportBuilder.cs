using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace FlexCharge.Activity.Services.InternalReporting.Reports;

public class ReportBuilder
{
    #region RowBuilder

    public class RowBuilder : IDisposable
    {
        private ReportBuilder _reportBuilder;
        private readonly int _rowIndex;

        public RowBuilder(ReportBuilder reportBuilder, int rowIndex)
        {
            _reportBuilder = reportBuilder;
            _rowIndex = rowIndex;
        }


        public void Dispose()
        {
            _reportBuilder = null;
        }

        public string this[int i]
        {
            get => _reportBuilder.GetCellValue(_rowIndex, i);
            set => _reportBuilder.SetCellValue(_rowIndex, i, value);
        }
    }

    #endregion

    public IList<string> Columns { get; }

    public ReportBuilder(IList<string> columns)
    {
        Columns = columns;
    }

    public override string ToString()
    {
        StringBuilder report = new();
        bool firstColumn = true;
        foreach (var column in Columns)
        {
            if (!firstColumn) report.Append(',');
            report.Append(EscapeCsvString(column));

            firstColumn = false;
        }

        report.AppendLine();

        foreach (var row in Rows)
        {
            firstColumn = true;
            int columnsInRow = 0;
            foreach (var column in row)
            {
                firstColumn = columnsInRow == 0;
                if (!firstColumn) report.Append(',');
                report.Append(EscapeCsvString(column));

                columnsInRow++;
            }

            while (columnsInRow < Columns.Count())
            {
                firstColumn = columnsInRow == 0;
                if (!firstColumn) report.Append(',');
                columnsInRow++;
            }

            report.AppendLine();
        }

        return report.ToString();
    }

    private string EscapeCsvString(string text)
    {
        if (string.IsNullOrEmpty(text)) return "";

        bool mustQuote = (text.Contains(",") || text.Contains("\"") || text.Contains("\r") || text.Contains("\n"));
        if (mustQuote)
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("\"");
            foreach (char nextChar in text)
            {
                sb.Append(nextChar);
                if (nextChar == '"')
                    sb.Append("\"");
            }

            sb.Append("\"");
            return sb.ToString();
        }
        else return text;
    }


    private List<List<string>> Rows { get; } = new();

    private void SetCellValue(int rowIndex, int columnIndex, string value)
    {
        EnsureRowExists(rowIndex);
        

        var columns = Rows[rowIndex];
        EnsureColumnsExist(columns);

        if (columns is null || columnIndex >= columns.Count)
            throw new ArgumentOutOfRangeException(nameof(columnIndex));

        columns[columnIndex] = value;
    }

    private void EnsureRowExists(int rowIndex)
    {
        while (rowIndex >= Rows.Count)
        {
            var row = new List<string>();
            Rows.Add(row);
            EnsureColumnsExist(row);
        }
    }

    private void EnsureColumnsExist(List<string> columns)
    {
        while (columns.Count < this.Columns.Count) columns.Add("");
    }


    private string GetCellValue(int rowIndex, int columnIndex)
    {
        if (rowIndex >= Rows.Count) throw new ArgumentOutOfRangeException(nameof(rowIndex));

        var columns = Rows[rowIndex];

        if (columns is null || columnIndex >= columns.Count)
            throw new ArgumentOutOfRangeException(nameof(columnIndex));

        return columns[columnIndex];
    }

    public RowBuilder StartRow()
    {
        return new RowBuilder(this, Rows.Count);
    }
}