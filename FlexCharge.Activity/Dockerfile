#See https://aka.ms/containerfastmode to understand how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

# Install the agent
RUN apt-get update && apt-get install -y wget ca-certificates gnupg \
&& echo 'deb http://apt.newrelic.com/debian/ newrelic non-free' | tee /etc/apt/sources.list.d/newrelic.list \
&& wget https://download.newrelic.com/548C16BF.gpg \
&& apt-key add 548C16BF.gpg \
&& apt-get update \
&& apt-get install -y newrelic-dotnet-agent

# Enable the agent
ENV CORECLR_ENABLE_PROFILING=1 \
CORECLR_PROFILER={36032161-FFC0-4B61-B559-F6C5D41BAE5A} \
CORECLR_NEWRELIC_HOME=/usr/local/newrelic-dotnet-agent \
CORECLR_PROFILER_PATH=/usr/local/newrelic-dotnet-agent/libNewRelicProfiler.so \
NEW_RELIC_LICENSE_KEY=a33a42f4833f5b37fe734e29e57f3fadFFFFNRAL \
NEW_RELIC_APPLICATION_LOGGING_ENABLED=false \
NEW_RELIC_APPLICATION_LOGGING_FORWARDING_ENABLED=false
# OpenTelemetry https://github.com/newrelic/newrelic-opentelemetry-examples/tree/main/getting-started-guides/dotnet
ENV OTEL_EXPORTER_OTLP_ENDPOINT=https://otlp.nr-data.net \
OTEL_EXPORTER_OTLP_HEADERS=api-key=a33a42f4833f5b37fe734e29e57f3fadFFFFNRAL \
OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT=4095 \
OTEL_EXPORTER_OTLP_COMPRESSION=gzip \
OTEL_EXPORTER_OTLP_PROTOCOL=http/protobuf \
OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE=delta

# .NET default is 8080
ENV ASPNETCORE_HTTP_PORTS=80

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src
COPY ["FlexCharge.Activity/FlexCharge.Activity.csproj", "FlexCharge.Activity/"]
COPY ["FlexCharge.Common/FlexCharge.Common.csproj", "FlexCharge.Common/"]
COPY ["FlexCharge.Contracts/FlexCharge.Contracts.csproj", "FlexCharge.Contracts/"]
RUN dotnet restore "FlexCharge.Activity/FlexCharge.Activity.csproj"
COPY . .
WORKDIR "/src/FlexCharge.Activity"
RUN dotnet build "FlexCharge.Activity.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "FlexCharge.Activity.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "FlexCharge.Activity.dll"]