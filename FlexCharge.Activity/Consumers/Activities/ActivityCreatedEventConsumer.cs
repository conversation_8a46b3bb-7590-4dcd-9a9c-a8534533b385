//#define MEASURE_PERFORMANCE

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Amazon.DynamoDBv2.Model;
using Amazon.SQS;
using FlexCharge.Common.DataStreaming;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Activity.Services.AggregatedActivities;
using FlexCharge.Common.MassTransit;
using MassTransit;
using Microsoft.Extensions.DependencyInjection;
using Polly;
using IActivity = FlexCharge.Contracts.Activities.IActivity;

namespace FlexCharge.Activity.Consumers;

public class ActivityCreatedEventConsumerDefinition : BatchConsumerDefinitionBase<ActivityCreatedEventConsumer>
{
    // !!! TEST CHANGES BEFORE PRODUCTION USE!!!!
    // // MassTransit configuration is tricky and can block message processing if not configured properly.

    protected override int MaxBatchMessageLimit => 100;
    protected override int ConcurrentBatchesLimit => Environment.ProcessorCount / 2;

    // !!! TEST CHANGES BEFORE PRODUCTION USE!!!!
}

public class ActivityCreatedEventConsumer : BatchConsumerBase<ActivityCreatedEvent>
{
    //Don't want to log enter and exit, because it's too much noise in logs
    protected override bool LogEnterAndExit => false;

    private readonly IAggregatedActivitiesService _aggregatedActivitiesService;


    public ActivityCreatedEventConsumer(
        IServiceScopeFactory serviceScopeFactory,
        IAggregatedActivitiesService aggregatedActivitiesService) : base(serviceScopeFactory)
    {
        _aggregatedActivitiesService = aggregatedActivitiesService;
    }

    protected override async Task ConsumeMessage(Batch<ActivityCreatedEvent> messageBatch,
        CancellationToken cancellationToken)
    {
        using var workspan = Workspan.Start<ActivityCreatedEventConsumer>();

#if MEASURE_PERFORMANCE
        workspan.Log.Information("Parallel batch consumers: {Increment}",
            Interlocked.Increment(ref _parallelBatchesProcessing));
#endif

        try
        {
            var activitiesToSave = messageBatch
                //.Where(m => m.Message.Activity.Name != "Audit_NoActivitiesFound_Error") //TEMPORARY SKIPPING Audit_NoActivitiesFound_Error ACTIVITY. 2023.11.03
                .Select(m => m.Message.Activity)
                .ToList();

            var sendToKinesisTask = SendToKinesis(activitiesToSave);
            var storeToDatabaseTask = StoreToDatabase(activitiesToSave);

            // Running in parallel to save precious time
            await Task.WhenAll(sendToKinesisTask, storeToDatabaseTask);
        }
        finally
        {
#if MEASURE_PERFORMANCE
            Interlocked.Decrement(ref _parallelBatchesProcessing);
#endif
        }
    }


    private async Task SendToKinesis(List<IActivity> activitiesToSave)
    {
        using var workspan = Workspan.Start<ActivityCreatedEventConsumer>();

#if MEASURE_PERFORMANCE
        var processingTimeStopwatch = Stopwatch.StartNew();
#endif

        try
        {
            await _aggregatedActivitiesService.SendToKinesisAsync(activitiesToSave);
        }
        catch (Exception e)
        {
            workspan.Log.Fatal(e, "Failed recording activities to data stream");
        }

        #region Commented

        // foreach (var activity in activitiesToSave)
        // {
        //     //var activity = message.Message.Activity;
        //
        //     // Don't want to optimize this, because we want to send events to Kinesis from source microservices 
        //     try
        //     {
        //         await _dataStreaming.PutRecordAsync(activity, "activity-stream", "orders-ms");
        //     }
        //     catch (Exception e)
        //     {
        //         workspan.RecordException(e, "Failed recording activity to data stream");
        //     }
        // }

        #endregion

#if MEASURE_PERFORMANCE
        processingTimeStopwatch.Stop();
        workspan.Log.Information("Kinesis processing time: {KinesisTime}",
            processingTimeStopwatch.ElapsedMilliseconds.ToString());
#endif
    }


    private async Task StoreToDatabase(List<IActivity> activitiesToSave)
    {
        using var workspan = Workspan.Start<ActivityCreatedEventConsumer>();

#if MEASURE_PERFORMANCE
        Stopwatch processingTimeStopwatch = Stopwatch.StartNew();
#endif

        try
        {
            await _aggregatedActivitiesService.StoreActivitiesToDatabaseAsync(activitiesToSave);
        }
        catch (Exception e)
        {
            workspan.Log.Fatal(e, "Failed persisting activity batch to database");
        }

#if MEASURE_PERFORMANCE
        processingTimeStopwatch.Stop();
        workspan.Log.Information("RDS processing time: {RdsTime}",
            processingTimeStopwatch.ElapsedMilliseconds.ToString());
#endif
    }
}