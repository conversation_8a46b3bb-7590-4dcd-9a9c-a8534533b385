using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Payments.BinChecker.Migrations
{
    /// <inheritdoc />
    public partial class BinRangesnewTree : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "HighAccount",
                table: "BinRanges");

            migrationBuilder.DropColumn(
                name: "LowAccount",
                table: "BinRanges"
                );
            
            migrationBuilder.AddColumn<string>(
                name: "DataProvider",
                table: "BinRanges",
                type: "text",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "DataProvider",
                table: "BinRanges",
                newName: "LowAccount");

            migrationBuilder.AddColumn<string>(
                name: "HighAccount",
                table: "BinRanges",
                type: "text",
                nullable: true);
        }
    }
}
