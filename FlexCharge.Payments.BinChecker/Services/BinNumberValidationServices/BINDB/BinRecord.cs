using CsvHelper.Configuration;
using CsvHelper.Configuration.Attributes;

namespace FlexCharge.Payments.BinChecker.Services.BinNumberValidationServices.BINDB;

public class BinRecord
{
    [Index(0)] public int BinID { get; set; }
    [Index(1)] public string CardIssuerBin { get; set; }
    [Index(2)] public string CardIssueType { get; set; }
    [Index(3)] public string CardBrand { get; set; }
    [Index(4)] public string CardClass { get; set; }
    [Index(5)] public string CardSubType { get; set; }
    [Index(6)] public string CardIssuingCountry { get; set; }
    [Index(7)] public string CardIssuerRegulated { get; set; }
    [Index(8)] public string CardIssuer { get; set; }
    [Index(9)] public string CardIssueDetail { get; set; }
    [Index(10)] public string CardReloadable { get; set; }
    [Index(11)] public string LowAccount { get; set; }
    [Index(12)] public string HighAccount { get; set; }
    [Index(13)] public string CardEBTState { get; set; }
    [Index(14)] public string Accel { get; set; }
    [Index(15)] public string AFFM { get; set; }
    [Index(16)] public string ATH { get; set; }
    [Index(17)] public string CU24 { get; set; }
    [Index(18)] public string EBT { get; set; }
    [Index(19)] public string Interac { get; set; }
    [Index(20)] public string InterLink { get; set; }
    [Index(21)] public string Jeanie { get; set; }
    [Index(22)] public string Maestro { get; set; }
    [Index(23)] public string NYCE { get; set; }
    [Index(24)] public string Pulse { get; set; }
    [Index(25)] public string Shazam { get; set; }
    [Index(26)] public string Star { get; set; }
    [Index(27)] public string Visa { get; set; }
    [Index(28)] public int Active { get; set; }
    [Index(29)] public int BinLength { get; set; }
    [Index(30)] public int PanLength { get; set; }
    [Index(31)] public int Year { get; set; }
    [Index(32)] public int Month { get; set; }
    [Index(33)] public int Day { get; set; }
    [Index(34)] public string FSAIndicator { get; set; }
    [Index(35)] public string PrepaidIndicator { get; set; }
    [Index(36)] public string CardProductSubType { get; set; }
    [Index(37)] public string LargeTicketIndicator { get; set; }
    [Index(38)] public string CardProcessingIndicator { get; set; }
    [Index(39)] public string CardFundSource { get; set; }
    [Index(40)] public int PanMinimumLength { get; set; }
    [Index(41)] public int PanMaximumLength { get; set; }
    [Index(42)] public string TokenBinIndicator { get; set; }
    [Index(43)] public string CardB2BProgram { get; set; }
    [Index(44)] public string CardDebitNetworkParticipant { get; set; }
    [Index(45)] public string CardBillingCurrency { get; set; }
    [Index(46)] public string CardMoneySendIndicator { get; set; }
    [Index(47)] public string CardMoneyTransferIndicator { get; set; }
    [Index(48)] public string CardOnlineGamblingIndicator { get; set; }
    [Index(49)] public string CardFastFunds { get; set; }

    [Index(50)] public string CardOriginalCreditIndicator { get; set; }
    // [Index(51)]
    // public string CardIssuerPhone { get; set; }
    // [Index(52)]
    // public string CardIssuerWebsite { get; set; }
}

#region Commented
// public class BinRecordMap : ClassMap<BinRecord>
// {
//     public BinRecordMap()
//     {
//         Map(m => m.BinID).Name("Bin ID");
//         Map(m => m.CardIssuerBin).Name("Card Issuer Bin");
//         Map(m => m.CardIssueType).Name("Card Issue Type");
//         Map(m => m.CardBrand).Name("Card Brand");
//         Map(m => m.CardClass).Name("Card Class");
//         Map(m => m.CardSubType).Name("Card Sub Type");
//         Map(m => m.CardIssuingCountry).Name("Card Issuing Country");
//         Map(m => m.CardIssuerRegulated).Name("Card Issuer Regulated");
//         Map(m => m.CardIssuer).Name("Card Issuer");
//         Map(m => m.CardIssueDetail).Name("Card Issue Detail");
//         Map(m => m.CardReloadable).Name("Card Reloadable");
//         Map(m => m.LowAccount).Name("Low Account");
//         Map(m => m.HighAccount).Name("High Account");
//         Map(m => m.CardEBTState).Name("Card EBT State");
//         Map(m => m.Accel).Name("Accel");
//         Map(m => m.AFFM).Name("AFFM");
//         Map(m => m.ATH).Name("ATH");
//         Map(m => m.CU24).Name("CU24");
//         Map(m => m.EBT).Name("EBT");
//         Map(m => m.Interac).Name("Interac");
//         Map(m => m.InterLink).Name("InterLink");
//         Map(m => m.Jeanie).Name("Jeanie");
//         Map(m => m.Maestro).Name("Maestro");
//         Map(m => m.NYCE).Name("NYCE");
//         Map(m => m.Pulse).Name("Pulse");
//         Map(m => m.Shazam).Name("Shazam");
//         Map(m => m.Star).Name("Star");
//         Map(m => m.Visa).Name("Visa");
//         Map(m => m.Active).Name("Active");
//         Map(m => m.BinLength).Name("Bin Length");
//         Map(m => m.PanLength).Name("Pan Length");
//         Map(m => m.Year).Name("Year");
//         Map(m => m.Month).Name("Month");
//         Map(m => m.Day).Name("Day");
//         Map(m => m.FSAIndicator).Name("FSA Indicator");
//         Map(m => m.PrepaidIndicator).Name("Prepaid Indicator");
//         Map(m => m.CardProductSubType).Name("Card Product SubType");
//         Map(m => m.LargeTicketIndicator).Name("Large Ticket Indicator");
//         Map(m => m.CardProcessingIndicator).Name("Card Processing Indicator");
//         Map(m => m.CardFundSource).Name("Card Fund Source");
//         Map(m => m.PanMinimumLength).Name("Pan Minimum Length");
//         Map(m => m.PanMaximumLength).Name("Pan Maximum Length");
//         Map(m => m.TokenBinIndicator).Name("Token Bin Indicator");
//         Map(m => m.CardB2BProgram).Name("Card B2B Program");
//         Map(m => m.CardDebitNetworkParticipant).Name("Card Debit Network Participant");
//         Map(m => m.CardBillingCurrency).Name("Card Billing Currency");
//         Map(m => m.CardMoneySendIndicator).Name("Card Money Send Indicator");
//         Map(m => m.CardMoneyTransferIndicator).Name("Card Money Transfer Indicator");
//         Map(m => m.CardOnlineGamblingIndicator).Name("Card Online Gambling Indicator");
//         Map(m => m.CardFastFunds).Name("Card Fast Funds");
//         Map(m => m.CardOriginalCreditIndicator).Name("Card Original Credit Indicator");
//     }
// }
#endregion