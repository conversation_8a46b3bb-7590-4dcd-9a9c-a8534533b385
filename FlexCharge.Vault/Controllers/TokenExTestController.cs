// #if DEBUG
// using System;
// using System.Threading;
// using System.Threading.Tasks;
// using FlexCharge.Common;
// using FlexCharge.Common.Encryption;
// using FlexCharge.Vault.DTO;
// using FlexCharge.Vault.Services;
// using FlexCharge.Vault.Services.NetworkTokenizationervice;
// using FlexCharge.Vault.Services.TokenExNetworkTokenization;
// using FlexCharge.Vault.Services.TokenExNetworkTokenization.Models;
// using FlexCharge.Vault.Services.TokenExNetworkTokenization.Models.Tokenize.Request;
// using Microsoft.AspNetCore.Mvc;
// using Microsoft.Extensions.Logging;
// using Microsoft.Extensions.Options;
//
// namespace FlexCharge.Vault.Controllers;
//
// [Route("[controller]")]
// [ApiController]
// public class TokenExController : ControllerBase
// {
//     private readonly ILogger _logger;
//     private readonly IVaultService _vaultService;
//     private readonly IAccountUpdaterService _accountUpdaterService;
//     private readonly IPgpEncryptionService _pgpService;
//     private readonly PostgreSQLDbContext _context;
//     private readonly AppOptions _globalData;
//     private readonly NetworkTokenizationTokenexService _networkTokenizationService;
//     private readonly TokenExHttpClient _tokenExHttpClient;
//     
//     
//     public TokenExController(ILogger<TestController> logger, IOptions<AppOptions> globalData,
//         IVaultService vaultService, PostgreSQLDbContext context, IAccountUpdaterService accountUpdaterService,
//         IPgpEncryptionService pgpService, NetworkTokenizationTokenexService networkTokenizationService)
//     {
//         _logger = logger;
//         _vaultService = vaultService;
//         _context = context;
//         _accountUpdaterService = accountUpdaterService;
//         _pgpService = pgpService;
//         _networkTokenizationService = networkTokenizationService;
//         _globalData = globalData.Value;
//     }
//
//     //PxDvSIDkOXQylBME15Wuv7rKuSfXqx4MHmqAwSVx
//     //API Key ID: dIsxwF7tM89m
//
//
//     [HttpPost("tokenize")]
//     [ProducesResponseType(200)]
//     public async Task<IActionResult> Post(CreditCardDTO card, CancellationToken token)
//     {
//         _logger.LogInformation(
//             $"ENTERED: {_globalData.Name} => GET {HttpContext.Request.Path + HttpContext.Request.QueryString}");
//
//         var tokenizedCard = await _networkTokenizationService.Tokenize(new TokenizationRequest
//         {
//             PanSource = PanSource.KEYENTERED,
//             ConsumerId = Guid.NewGuid().ToString(),
//             PresentationMode = new() { PresentationMode.ECOM },
//             AccountType = AccountType.CREDIT_CARD,
//             ExpirationDate = $"{card.ExpirationYear}{card.ExpirationMonth}",
//             Data = card.Number,
//             DeviceData = new DeviceData()
//             {
//                 Locale = "en-US",
//             }
//         }, token);
//         
//         _logger.LogInformation($"Tokenized Card: {tokenizedCard}");
//
//         return Ok(tokenizedCard);
//     }
//     
//     [HttpPost("getCardMetadata")]
//     [ProducesResponseType(200)]
//     public async Task<IActionResult> Post(string cardToken, CancellationToken token)
//     {
//         _logger.LogInformation(
//             $"ENTERED: {_globalData.Name} => GET {HttpContext.Request.Path + HttpContext.Request.QueryString}");
//
//
//         var getToken = await _networkTokenizationService.GetCardMetadata(new GetCardMetadataRequest
//         {
//             Token = cardToken
//         }, token);
//
//         return Ok(getToken);
//     }
//     
//     [HttpPost("getPaymentBundle")]
//     [ProducesResponseType(200)]
//     public async Task<IActionResult> Post(PaymentBundleRequest payload, CancellationToken token)
//     {
//         _logger.LogInformation(
//             $"ENTERED: {_globalData.Name} => GET {HttpContext.Request.Path + HttpContext.Request.QueryString}");
//         
//         var getToken = await _networkTokenizationService.GetPaymentBundle(payload, token);
//         return Ok(getToken);
//     }
//     
//     [HttpPost("getAsset")]
//     [ProducesResponseType(200)]
//     public async Task<IActionResult> Post(AssetRequest payload, CancellationToken token)
//     {
//         _logger.LogInformation(
//             $"ENTERED: {_globalData.Name} => GET {HttpContext.Request.Path + HttpContext.Request.QueryString}");
//
//
//         var getToken = await _networkTokenizationService.GetAsset(payload, token);
//         return Ok(getToken);
//     }
// }
// #endif