{"containerDefinitions": [{"name": "core-vault", "image": "************.dkr.ecr.us-east-1.amazonaws.com/fc-core-server-vault:7da9b252999a69d0cc1ab1ed52f769f29f960e1d", "cpu": 0, "portMappings": [{"containerPort": 80, "hostPort": 80, "protocol": "tcp"}], "essential": true, "environment": [{"name": "DB_USERNAME", "value": "vault_service_prod"}, {"name": "DB_DATABASE", "value": "fc_vault"}, {"name": "DB_HOST", "value": "flexcharge-prod-pci.ctwfhnhdjewu.us-east-1.rds.amazonaws.com"}, {"name": "DB_PORT", "value": "5432"}, {"name": "ASPNETCORE_ENVIRONMENT", "value": "Production"}, {"name": "SNS_IAM_REGION", "value": "us-east-1"}, {"name": "TOKENEX_SFTP_HOST", "value": "batch.tokenex.com"}, {"name": "TOKENEX_SFTP_PORT", "value": "22"}, {"name": "TOKENEX_SFTP_USERNAME", "value": "****************"}, {"name": "TOKENEX_ID", "value": "****************"}, {"name": "TOKENEX_ACCOUNT_UPDATER_BUCKET", "value": "cc-account-updater"}, {"name": "NEW_RELIC_APP_NAME", "value": "Vault-production"}, {"name": "OTEL_SERVICE_NAME", "value": "Vault-production"}], "mountPoints": [], "volumesFrom": [], "secrets": [{"name": "DB_PASSWORD", "valueFrom": "arn:aws:secretsmanager:us-east-1:************:secret:PROD_DB_VAULT_PASSWORD-8T0rCU"}, {"name": "SNS_IAM_ACCESS_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:************:secret:PROD_SNS_IAM_ACCESS_KEY-lbCS4b"}, {"name": "SNS_IAM_SECRET_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:************:secret:PROD_SNS_IAM_SECRET_KEY-a0ce7k"}, {"name": "TOKENEX_SFTP_PASSWORD", "valueFrom": "arn:aws:secretsmanager:us-east-1:************:secret:PRODUCTION-TOKENEX-SFTP-PASSWORD-YBNdgv"}, {"name": "FLEXCHARGE_PGP_ACCOUNT_UPDATER_PUBLIC_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:************:secret:SANDBOX-PGP-FLEXCHARGE-ACCOUNT-UPDATER-PUBLIC-KEY-uwb7BZ"}, {"name": "FLEXCHARGE_PGP_ACCOUNT_UPDATER_PRIVATE_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:************:secret:SANDBOX-PGP-FLEXCHARGE-ACCOUNT-UPDATER-PRIVATE-KEY-79ATjq"}, {"name": "FLEXCHARGE_PGP_ACCOUNT_UPDATER_PRIVATE_KEY_PASSPHRASE", "valueFrom": "arn:aws:secretsmanager:us-east-1:************:secret:SANDBOX-TOKENEX-ACCOUNT-UPDATER-SIGNING-KEY-PASSPHRASE-pRH1Ep"}, {"name": "TOKENEX_PGP_ACCOUNT_UPDATER_PUBLIC_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:************:secret:PRODUCTION-TOKENEX-PGP-ACCOUNT-UPDATER-PUBLIC-KEY-DLBLNe"}, {"name": "TOKENEX_NETWORK_TOKENIZATION_API_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:************:secret:PRODUCTION-TOKENEX-API-KEY-mPZ8vU"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/fc-core-vault-server-prod", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs", "awslogs-create-group": "true"}}}], "family": "fc-core-vault-server-prod", "taskRoleArn": "arn:aws:iam::************:role/ecsTaskExecutionWithSecretAccess-Production-Role", "executionRoleArn": "arn:aws:iam::************:role/ecsTaskExecutionWithSecretAccess-Production-Role", "networkMode": "awsvpc", "volumes": [], "placementConstraints": [], "requiresCompatibilities": ["FARGATE"], "cpu": "2048", "memory": "6144"}