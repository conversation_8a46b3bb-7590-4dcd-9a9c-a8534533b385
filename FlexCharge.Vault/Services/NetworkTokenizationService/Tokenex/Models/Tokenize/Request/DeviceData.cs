using System.Text.Json.Serialization;

namespace FlexCharge.Vault.Services.TokenExNetworkTokenization.Models.Tokenize.Request;

public class DeviceData
{
    
    
    
    public string DeviceName { get; set; }
   
    public string DeviceLocation { get; set; }
   
    public string DeviceType { get; set; }
    /// <summary>
    /// Unique ID for the device, such as a hardware ID.
    /// </summary>
    public string DeviceId { get; set; }
    
    /// <summary>
    /// 	OS of the device.
    /// </summary>
    public string DevicePlatform { get; set; }
    /// <summary>
    /// he telephone number or the last few digits of the telephone number. Required for American Express cards.
    /// </summary>
    
    public string DevicePhoneNumber { get; set; }
    /// <summary>
    /// The IPv4 address of the device. Required for American Express if PanSource is not "ONFILE".
    /// </summary>
   
    public string DeviceIPv4 { get; set; }
    
    
    public string DeviceTimeZone { get; set; }
    
    /// <summary>
    /// he version of the OS of the device.
    /// </summary
    public string OsVersion { get; set; }
    
    /// <summary>
    /// The language that the application is using to communicate with the cardholder.
    /// </summary>
    public string Language { get; set; }
    /// <summary>
    /// he language that the application communicates with the cardholder.
    /// It is based on BCP 47 standard. The language must be lowercase, and the country must be uppercase.
    /// The language and country should be separated using a hyphen (-). *Required for Visa.
    /// </summary>

    public string Locale { get; set; }
    /// <summary>
    /// The unique ID assigned to a wallet installation for a given user and device.
    /// </summary>
  
    public string WalletId { get; set; }
    
    public string Brand { get; set; }
   
    public string Manufacturer { get; set; }
   
    public string Model { get; set; }
    
    public string NetworkType { get; set; }
  
    public string ImeiNumber { get; set; }
    
    public string SerialNumber { get; set; }
    
    public string WalletAccountEmailAddress { get; set; }
}

