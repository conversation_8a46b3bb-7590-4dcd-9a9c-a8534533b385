using MassTransit.Futures.Contracts;

namespace FlexCharge.Vault.Services.TokenExNetworkTokenization.Models;

public class TokenStatusRequest
{
    public string Token { get; set; }
}

public class TokenStatusResponse : IResponse
{
    public bool Success { get; set; }

    /// <summary>
    /// TokenEx reference number for the transaction.
    /// </summary>
    public string ReferenceNumber { get; set; }

    /// <summary>
    /// TokenEx Error Code and human readable description.
    /// </summary>
    public string Error { get; set; }

    public string Message { get; set; }

    public TokenStatusResponseNetworkResponse NetworkResponse { get; set; }


    public class TokenStatusResponseNetworkResponse : INetworkResponse
    {
        public TokenState TokenState { get; set; }

        /// <summary>
        /// Unique message identifier (GUID format) of this command.
        /// </summary>
        public string MessageId { get; set; }

        /// <summary>
        /// Message identifier assigned for the entire conversation (GUID format).
        /// Typically, it is generated by the initiator of the flow.
        /// </summary>
        public string ConversationId { get; set; }

        /// <summary>
        /// The four-digit status code.
        /// </summary>
        public string StatusCode { get; set; }

        /// <summary>
        /// The four-digit status code.
        /// </summary>
        public string StatusMessage { get; set; }

        public string TokenReferenceId { get; set; }
    }

    // public enum TokenState
    // {
    //     INACTIVE,
    //     ACTIVE,
    //     SUSPENDED,
    //     DELETED
    // }
}