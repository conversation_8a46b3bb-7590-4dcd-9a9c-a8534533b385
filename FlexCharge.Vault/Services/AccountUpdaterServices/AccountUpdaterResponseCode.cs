namespace FlexCharge.Contracts.Vault;

public enum AccountUpdaterResponseCode
{
    Unknown = 0,
    NewCard = 1,
    ValidAccountNoUpdate = 2,
    AccountExpirationDateUpdated = 3,
    AccountNumberUpdated = 4,
    AccountIsClosed = 5,
    ContactCardholder = 6,
    ErrorMerchantNotRegistered = 7,
    NoMatch = 8,
    BlockedMerchantOrReportedFraud = 9,
    InactiveCard = 10,
    InactiveOrCanceledSeller = 11,
    InvalidExpirationDate = 12,
    InvalidAccountNumber = 13,
    InvalidCardType = 14,
    RemoveSuccessful = 15,
    InvalidOrBlankSellerId = 16,
    CardRemovedPreviously = 17,
    CardRecordNotFound = 18
}