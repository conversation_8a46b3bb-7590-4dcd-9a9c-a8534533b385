using System.Collections.Generic;
using static FlexCharge.Vault.Services.AccountUpdaterServices.AccountUpdaterResponse;


namespace FlexCharge.Vault.Services.AccountUpdaterServices;

//TODO - move the logic to RuleEngine
public static class CardAccountUpdaterHelper
{
    public static List<string> MessagesToStopWaitingForAccountUpdateOn = new()
    {
        AccountNumberUpdated,
        AccountExpirationDateUpdated,
        ContactCardholder,
        AccountIsClosed,
        RemoveSuccessful,
        NewCard
    };

    public static List<string> MessagesToDeactivateCardOn = new()
    {
        // CardRecordNotFound,
        // AccountIsClosed, 
        // InvalidAccountNumber,
        // InvalidExpirationDate,
        // InvalidCardType,
        // InvalidOrBlankSellerId
    };

    public static List<string> MessagesToDisableAccountUpdater = new()
    {
        // ContactCardholder
    };
}