using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Vault.Migrations
{
    /// <inheritdoc />
    public partial class addixcvvcreatedon : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "CreatedOn_CvvNotNull_Idx1",
                table: "Vaults",
                column: "CreatedOn",
                filter: "\"Cvv\" is not null")
                .Annotation("Npgsql:CreatedConcurrently", true);
            
            migrationBuilder.CreateIndex(
                name: "CreatedOn_CvvNotNull_Idx",
                table: "PaymentInstruments",
                column: "CreatedOn",
                filter: "\"Cvv\" is not null")
                .Annotation("Npgsql:CreatedConcurrently", true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "CreatedOn_CvvNotNull_Idx1",
                table: "Vaults");

            migrationBuilder.DropIndex(
                name: "CreatedOn_CvvNotNull_Idx",
                table: "PaymentInstruments");
        }
    }
}
