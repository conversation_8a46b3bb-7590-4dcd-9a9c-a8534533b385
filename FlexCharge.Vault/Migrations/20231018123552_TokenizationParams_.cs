using FlexCharge.Contracts.Common;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Vault.Migrations
{
    /// <inheritdoc />
    public partial class TokenizationParams : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Address>(
                name: "BillingAddress",
                table: "PaymentInstruments",
                type: "jsonb",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Email",
                table: "PaymentInstruments",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Phone",
                table: "PaymentInstruments",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SenseKey",
                table: "PaymentInstruments",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<Address>(
                name: "ShippingAddress",
                table: "PaymentInstruments",
                type: "jsonb",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "BillingAddress",
                table: "PaymentInstruments");

            migrationBuilder.DropColumn(
                name: "Email",
                table: "PaymentInstruments");

            migrationBuilder.DropColumn(
                name: "Phone",
                table: "PaymentInstruments");

            migrationBuilder.DropColumn(
                name: "SenseKey",
                table: "PaymentInstruments");

            migrationBuilder.DropColumn(
                name: "ShippingAddress",
                table: "PaymentInstruments");
        }
    }
}
