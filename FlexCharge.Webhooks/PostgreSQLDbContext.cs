using FlexCharge.Webhooks.Entities;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;
using MassTransit.Futures.Contracts;

namespace FlexCharge.Webhooks
{
    public class PostgreSQLDbContext : DbContext
    {
        private readonly IHttpContextAccessor _httpContextAccessor;

        public PostgreSQLDbContext(DbContextOptions<PostgreSQLDbContext> options,
            IHttpContextAccessor httpContextAccessor)
            : base(options)
        {
            _httpContextAccessor = httpContextAccessor;
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            //If you would like to utilize schema name change
            //modelBuilder.Entity<{Entity name here}>().ToTable("{Table name here}", schemaName);
            base.OnModelCreating(modelBuilder);

            modelBuilder.Entity<Webhook>().Property<bool>("IsDeleted");
            modelBuilder.Entity<Webhook>().HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);

            modelBuilder.Entity<Event>().Property<bool>("IsDeleted");
            modelBuilder.Entity<Event>().HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);

            modelBuilder.Entity<Subscriber>().Property<bool>("IsDeleted");
            modelBuilder.Entity<Subscriber>().HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);

            modelBuilder.Entity<WebhookLogEntry>().Property<bool>("IsDeleted");
            modelBuilder.Entity<WebhookLogEntry>().HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);
        }

        public DbSet<Webhook> Webhooks { get; set; }
        public DbSet<Event> Events { get; set; }
        public DbSet<Subscriber> Subscribers { get; set; }
        public DbSet<WebhookLogEntry> WebhooksLog { get; set; }
        //public DbSet<Activity> Activities { get; set; }

        public override int SaveChanges()
        {
            UpdateSoftDeleteStatuses();
            return base.SaveChanges();
        }

        public override async Task<int> SaveChangesAsync(bool acceptAllChangesOnSuccess,
            CancellationToken cancellationToken = default(CancellationToken))
        {
            UpdateSoftDeleteStatuses();
            return await base.SaveChangesAsync(acceptAllChangesOnSuccess, cancellationToken);
        }

        private void UpdateSoftDeleteStatuses()
        {
            var user = _httpContextAccessor?.HttpContext?.User?.Claims.SingleOrDefault(x =>
                x.Type == ClaimTypes.NameIdentifier);
            foreach (var entry in ChangeTracker.Entries())
            {
                switch (entry.State)
                {
                    case EntityState.Modified:
                    {
                        if (user != null)
                        {
                            if (entry.Properties.Any(o => o.Metadata.Name == "ModifiedBy"))
                                entry.Property("ModifiedBy").CurrentValue = user.Value;
                        }


                        if (entry.Properties.Any(o => o.Metadata.Name == "ModifiedOn"))
                            entry.Property("ModifiedOn").CurrentValue = DateTime.Now.ToUniversalTime();
                        entry.CurrentValues["IsDeleted"] = false;
                        break;
                    }
                    case EntityState.Added:
                    {
                        if (user != null)
                        {
                            if (entry.Properties.Any(o => o.Metadata.Name == "CreatedBy"))
                                entry.Property("CreatedBy").CurrentValue = user.Value;
                            if (entry.Properties.Any(o => o.Metadata.Name == "ModifiedBy"))
                                entry.Property("ModifiedBy").CurrentValue = user.Value;
                        }

                        if (entry.Properties.Any(o => o.Metadata.Name == "CreatedOn"))
                            entry.Property("CreatedOn").CurrentValue = DateTime.Now.ToUniversalTime();
                        if (entry.Properties.Any(o => o.Metadata.Name == "ModifiedOn"))

                            entry.Property("ModifiedOn").CurrentValue = DateTime.Now.ToUniversalTime();
                        entry.CurrentValues["IsDeleted"] = false;
                        break;
                    }

                    case EntityState.Deleted:
                        entry.State = EntityState.Modified;
                        entry.CurrentValues["IsDeleted"] = true;
                        break;
                }
            }
        }
    }
}