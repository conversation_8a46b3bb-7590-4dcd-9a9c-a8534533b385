using Stripe;

namespace FlexCharge.Eligibility.Adapters.Stripe.Services.StripeService;

public interface IStripeProviderService
{
    Task MarkInvoiceAsync(string accountId, string invoiceId, Dictionary<string, string> metaData);
    Task<Charge> GetChargeAsync(string accountId, string chargeId);

    Task RequestForwardApi(string accountId, string apiKey, string paymentMethod, string forwardApiEndpointUrl);
}