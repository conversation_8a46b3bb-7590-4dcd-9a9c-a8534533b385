using FlexCharge.WorkflowEngine.Common.Workflows.Providers;

namespace FlexCharge.Eligibility.Adapters.Stripe;

public partial class ProviderDescription : IProviderDescription
{
    public static Guid ProviderId = new Guid("bf98adb6-4bf3-4837-ba48-1ad907df37ec");

    public static string ProviderName => "Stripe";

    public static ProviderType ProviderType => ProviderType.PaymentProvider;
}

/// <summary>
/// Do not change this class. It is used to ensure all required data is specified for provider
/// </summary>
public partial class ProviderDescription
{
    public Guid Id => ProviderId;
    public string Name => ProviderName;
    public ProviderType Type => ProviderType;
}