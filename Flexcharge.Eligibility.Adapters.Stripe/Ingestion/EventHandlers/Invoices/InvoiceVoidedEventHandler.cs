using FlexCharge.Common.Shared.Adapters;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Contracts.Common;
using Flexcharge.Eligibility.StripeAdapter.Ingestion;
using MassTransit;
using Stripe;

namespace FlexCharge.Eligibility.Adapters.Stripe.Ingestion.EventHandlers.Invoices;

class InvoiceVoidedEventHandler : StripeWebhookEventHandlerBase<Invoice>
{
    public override string EventType => EventTypes.InvoiceVoided;

    protected override async Task HandleEventInternalAsync()
    {
        using var workspan = Workspan.Start<InvoiceVoidedEventHandler>();

        var invoice = EventData;

        var merchantConfiguration = await GetMerchantConfigurationAsync(OAuthProvider.Stripe, Context.StripeAccount);

        workspan
            .Baggage("Mid", merchantConfiguration.Mid)
            .Baggage("InvoiceId", invoice.Id);

        if (!invoice.Metadata.TryGetValue(FlexFactorMetadata.FlexFactorOrderId, out var orderIdString))
        {
            workspan.Log.Information("No OrderId found in Stripe metadata");
            return; //!!!
        }

        workspan
            .Baggage("OrderId", orderIdString);

        var orderId = Guid.Parse(orderIdString);

        var publisher = GetRequiredService<IPublishEndpoint>();

        await PublishInvoiceVoidedEvent(publisher, merchantConfiguration.Mid, orderId, invoice.Id);
    }

    private static async Task PublishInvoiceVoidedEvent(IPublishEndpoint publisher, Guid mid, Guid orderId,
        string invoiceId)
    {
        await publisher.Publish(new ExternalProviderInvoiceVoidedEvent(mid, orderId, invoiceId));
    }
}