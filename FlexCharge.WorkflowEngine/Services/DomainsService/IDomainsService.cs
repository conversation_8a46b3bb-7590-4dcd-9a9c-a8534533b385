using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FlexCharge.WorkflowEngine.DTO;

namespace FlexCharge.WorkflowEngine.Services.DomainsService;

public interface IDomainsService
{
    Task<List<DomainDTO>> GetDomainsAsync(String query);
    Task<DomainDTO> GetDomainByIdAsync(Guid id);
    Task DeleteDomainAsync(Guid id);
    Task CreateDomainAsync(CreateDomainDTO payload);
}