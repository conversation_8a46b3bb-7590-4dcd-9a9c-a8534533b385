{"app": {"name": "workflow-engine-service", "version": "0.0.1"}, "jwt": {"Provider": "Cognito", "secretKey": "JLBMU2VbJZmt42sUwByUpJJF6Y5mG2gPNU9sQFUpJFcGFJdyKxskR3bxh527kax2UcXHvB", "expiryMinutes": 30, "issuer": "identity-service", "validateLifetime": true, "ValidateAudience": true, "ValidAudience": "6jl6laoa1jtk2hgqdu79bu3m7d", "Region": "us-east-1", "UserPoolId": "us-east-1_7Snu9L5Rt", "AppClientId": "6jl6laoa1jtk2hgqdu79bu3m7d"}, "cache": {"connectionString": "redis-cache-sandbox.wkhxcg.ng.0001.use1.cache.amazonaws.com:6379", "instance": "", "schemaName": "dbo", "tableName": "<PERSON><PERSON>", "BigPayloadCacheConnectionString": "redis-cache-sandbox.wkhxcg.ng.0001.use1.cache.amazonaws.com:6379", "IdempotencyCacheConnectionString": "redis-cache-sandbox.wkhxcg.ng.0001.use1.cache.amazonaws.com:6379", "ExternalRequestsCacheConnectionString": "redis-cache-sandbox.wkhxcg.ng.0001.use1.cache.amazonaws.com:6379"}, "serilog": {"consoleEnabled": true, "level": "Information", "path": "../logs/workflow-engine-{0}.txt"}, "dataStream": {"provider": "kinesis"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "email": {"provider": "sendgrid", "key": "*********************************************************************", "supportEmail": "<EMAIL>", "senderEmail": "<EMAIL>", "senderName": "FlexFactor"}, "swagger": {"enabled": true, "commentsEnabled": true, "reDocEnabled": false, "name": "v1", "title": "workflow-engine", "version": "v1", "routePrefix": "", "includeSecurity": true}, "jaeger": {"agentHost": "**************", "agentPort": 6831}, "backgroundWorkerService": {"executionInterval": 500}, "AllowedHosts": "*"}