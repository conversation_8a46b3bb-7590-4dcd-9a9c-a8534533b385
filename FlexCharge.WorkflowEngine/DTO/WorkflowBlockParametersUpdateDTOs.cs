using System;
using System.Collections.Generic;
using FlexCharge.Common.Response;

namespace FlexCharge.WorkflowEngine.DTO;

public class WorkflowNodeParametersUpdateDTO
{
    public object Node { get; set; }
    public IDictionary<Guid, object> Answers { get; set; }
}

public class WorkflowNodeParametersUpdateResponse : BaseResponse
{
    public object Data { get; set; }
    public IList<ParameterValidationError> ValidationErrors { get; set; }
}

public record ParameterValidationError(
    Guid ParameterId,
    string ErrorMessage
);