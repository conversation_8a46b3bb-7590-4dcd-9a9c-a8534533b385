using System.Collections.Generic;
using FlexCharge.Audit.DTO;
using FlexCharge.Common.Shared.Activities;

namespace FlexCharge.Audit.Rules.Facts.ActivityFacts.Implementations.Eligibility.Processing;

[ActivityFact(ActivityCategories.Orders_ConsumerNotification, "ConsumerNotification_NotificationDisabled")]
public class ConsumerNotification_NotificationDisabled_Fact : ActivityWithMetadataFactBase
{
    public ConsumerNotification_NotificationDisabled_Fact(ActivityToAuditDTO data)
        : base(data)
    {
    }

    public override void SetFactProperties(Dictionary<string, string> meta)
    {
    }
}

[ActivityFact(ActivityCategories.Orders_ConsumerNotification, "ConsumerNotification_NotificationFailed")]
public class ConsumerNotification_NotificationFailed_Fact : ActivityWithMetadataFactBase
{
    public ConsumerNotification_NotificationFailed_Fact(ActivityToAuditDTO data)
        : base(data)
    {
    }

    public override void SetFactProperties(Dictionary<string, string> meta)
    {
    }
}