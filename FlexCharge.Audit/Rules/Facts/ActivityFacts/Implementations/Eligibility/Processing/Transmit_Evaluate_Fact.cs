using System.Collections.Generic;
using FlexCharge.Audit.DTO;
using FlexCharge.Common.Shared.Activities;

namespace FlexCharge.Audit.Rules.Facts.ActivityFacts.Implementations.Eligibility;

[ActivityFact(ActivityCategories.Eligibility_Transmit, "Transmit_Evaluate")]
public class Transmit_Evaluate_Fact : ActivityWithMetadataFactBase
{
    public int Amount { get; set; }
    public string ExternalOrderId { get; set; }

    public Transmit_Evaluate_Fact(ActivityToAuditDTO data)
        : base(data)
    {
    }

    public override void SetFactProperties(Dictionary<string, string> meta)
    {
        Amount = int.Parse(meta["Amount"]);
        ExternalOrderId = meta["ExternalOrderId"];
    }
}