using FlexCharge.Audit.DTO;
using FlexCharge.Common.Shared.Activities;

namespace FlexCharge.Audit.Rules.Facts.ActivityFacts.Implementations.Payments.Transactions;

[ActivityFact(ActivityCategories.Payments_Processing, "Payments_SalePayment_Succeeded")]
public class Payments_SalePayment_Succeeded_Fact : PaymentTransactionFactBase
{
    public Payments_SalePayment_Succeeded_Fact(ActivityToAuditDTO data) : base(data)
    {
    }
}