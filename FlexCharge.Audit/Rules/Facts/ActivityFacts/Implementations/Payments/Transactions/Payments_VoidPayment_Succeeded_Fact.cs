using FlexCharge.Audit.DTO;
using FlexCharge.Common.Shared.Activities;

namespace FlexCharge.Audit.Rules.Facts.ActivityFacts.Implementations.Payments.Transactions;

[ActivityFact(ActivityCategories.Payments_Processing, "Payments_VoidPayment_Succeeded")]
public class Payments_VoidPayment_Succeeded_Fact : PaymentTransactionFactBase
{
    public Payments_VoidPayment_Succeeded_Fact(ActivityToAuditDTO data) : base(data)
    {
    }
}