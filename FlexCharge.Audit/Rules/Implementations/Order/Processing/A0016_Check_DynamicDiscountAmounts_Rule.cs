using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Audit.Rules.Facts.ActivityFacts.Implementations.Eligibility;
using FlexCharge.Audit.Rules.Facts.ActivityFacts.Implementations.Payments.Transactions;
using FlexCharge.Common.Shared.Activities;
using NUnit.Framework;

namespace FlexCharge.Audit.Rules.Implementations.Order.Processing;

public class A0016_Check_DynamicDiscountAmounts_Rule : TestableRuleBase
{
    private const int MAX_DYNAMIC_DISCOUNT_AMOUNT = 10;

    IEnumerable<Payments_AuthorizePayment_Succeeded_Fact> _authorizations = null;
    IEnumerable<Payments_CapturePayment_Succeeded_Fact> _captures = null;
    IEnumerable<Payments_SalePayment_Succeeded_Fact> _sales = null;

    public override void Define()
    {
        When()
            .Collect(() => _captures)
            .Collect(() => _sales)
            .Collect(() => _authorizations)
            .Having(() =>
                _authorizations.Any(x => x.DiscountAmount > MAX_DYNAMIC_DISCOUNT_AMOUNT) ||
                _captures.Any(x => x.DiscountAmount > MAX_DYNAMIC_DISCOUNT_AMOUNT) ||
                _sales.Any(x => x.DiscountAmount > MAX_DYNAMIC_DISCOUNT_AMOUNT)
            )
            ;

        Then()
            .CriticalError(() => $"Dynamic discount amount is greater than {MAX_DYNAMIC_DISCOUNT_AMOUNT}");
    }

    #region Unit Testing

    [Test]
    [ExcludeFromCodeCoverage]
    public override async Task Test_Positive()
    {
        //Arrange
        await CreateActivityFromFactAsync<Payments_AuthorizePayment_Succeeded_Fact>(
            set: set => set.Meta(meta => meta
                .SetValue("Amount", 2000)
                .SetValue("DiscountAmount", 100)
            ));


        //Act
        Session.Fire();

        //Assert
        AssertFiredOnce();
    }

    [Test]
    [ExcludeFromCodeCoverage]
    public override async Task Test_Negative()
    {
        //Arrange
        await CreateActivityFromFactAsync<Payments_AuthorizePayment_Succeeded_Fact>(
            set: set => set.Meta(meta => meta
                .SetValue("Amount", 2000)
                .SetValue("DiscountAmount", 6)
            ));

        //Act
        Session.Fire();

        //Assert
        AssertDidNotFire();
    }

    [Test]
    [ExcludeFromCodeCoverage]
    public async Task Test_Positive_WithCapture()
    {
        //Arrange
        await CreateActivityFromFactAsync<Payments_CapturePayment_Succeeded_Fact>(
            set: set => set.Meta(meta => meta
                .SetValue("Amount", 2000)
                .SetValue("DiscountAmount", 100)
            ));


        //Act
        Session.Fire();

        //Assert
        AssertFiredOnce();
    }

    [Test]
    [ExcludeFromCodeCoverage]
    public async Task Test_Negative_WithCapture()
    {
        //Arrange
        await CreateActivityFromFactAsync<Payments_CapturePayment_Succeeded_Fact>(
            set: set => set.Meta(meta => meta
                .SetValue("Amount", 2000)
                .SetValue("DiscountAmount", 6)
            ));

        //Act
        Session.Fire();

        //Assert
        AssertDidNotFire();
    }

    [Test]
    [ExcludeFromCodeCoverage]
    public async Task Test_Positive_WithSale()
    {
        //Arrange
        await CreateActivityFromFactAsync<Payments_SalePayment_Succeeded_Fact>(
            set: set => set.Meta(meta => meta
                .SetValue("Amount", 2000)
                .SetValue("DiscountAmount", 100)
            ));


        //Act
        Session.Fire();

        //Assert
        AssertFiredOnce();
    }

    [Test]
    [ExcludeFromCodeCoverage]
    public async Task Test_Negative_WithSale()
    {
        //Arrange
        await CreateActivityFromFactAsync<Payments_SalePayment_Succeeded_Fact>(
            set: set => set.Meta(meta => meta
                .SetValue("Amount", 2000)
                .SetValue("DiscountAmount", 6)
            ));

        //Act
        Session.Fire();

        //Assert
        AssertDidNotFire();
    }

    #endregion
}