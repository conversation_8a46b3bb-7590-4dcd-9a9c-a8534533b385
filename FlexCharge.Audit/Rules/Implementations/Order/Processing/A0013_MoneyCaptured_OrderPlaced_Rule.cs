using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Audit.Rules.Facts.ActivityFacts.Implementations.Eligibility;
using FlexCharge.Audit.Rules.Facts.ActivityFacts.Implementations.Payments.Transactions;
using FlexCharge.Common.Shared.Activities;
using NUnit.Framework;

namespace FlexCharge.Audit.Rules.Implementations.Order.Processing;

public class A0013_MoneyCaptured_OrderPlaced_Rule : TestableRuleBase
{
    IEnumerable<Payments_CapturePayment_Succeeded_Fact> _captures = null;
    IEnumerable<Payments_SalePayment_Succeeded_Fact> _sales = null;
    IEnumerable<Order_Placed_Fact> _orderPlacedFacts = null;
    private int _capturedAmount = 0;


    public override void Define()
    {
        When()
            .Collect(() => _captures)
            .Collect(() => _sales)
            .Collect(() => _orderPlacedFacts)
            .Let(() => _capturedAmount, () =>
                _captures.Sum(c => c.Amount) +
                _sales.Sum(s => s.Amount))
            .Having(() =>
                _capturedAmount > 0 &&
                _orderPlacedFacts.Count() == 0)
            ;

        Then()
            .CriticalError(() => "Funds captured, but order not placed");
    }

    #region Unit Testing

    [Test]
    [ExcludeFromCodeCoverage]
    public override async Task Test_Positive()
    {
        //Arrange
        await CreateActivityFromFactAsync<Payments_CapturePayment_Succeeded_Fact>(
            set: set => set.Meta(meta => meta
                .SetValue("Amount", 2000)
                .SetValue("DiscountAmount", 0)
            ));

        //Act
        Session.Fire();

        //Assert
        AssertFiredOnce();
    }

    [Test]
    [ExcludeFromCodeCoverage]
    public override async Task Test_Negative()
    {
        //Arrange
        await CreateActivityFromFactAsync<Payments_CapturePayment_Succeeded_Fact>(
            set: set => set.Meta(meta => meta
                .SetValue("Amount", 2000)
                .SetValue("DiscountAmount", 0)
            ));

        await CreateActivityFromFactAsync<Order_Placed_Fact>(
            set: set => set.Meta(meta => meta
                .SetValue("Amount", 2000)
                .SetValue("Fee", 2000)
            ));

        //Act
        Session.Fire();

        //Assert
        AssertDidNotFire();
    }

    #endregion
}