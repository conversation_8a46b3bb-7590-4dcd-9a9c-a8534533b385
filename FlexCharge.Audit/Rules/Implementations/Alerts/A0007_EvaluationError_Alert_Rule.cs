using System.Diagnostics.CodeAnalysis;
using System.Threading.Tasks;
using FlexCharge.Audit.Rules.Facts.ActivityFacts.Implementations.Eligibility.Evaluation;
using FlexCharge.Common.Shared.Activities;
using NUnit.Framework;

namespace FlexCharge.Audit.Rules.Implementations.Order.Alerts;

public class A0007_EvaluationError_Alert_Rule : TestableRuleBase
{
    public override void Define()
    {
        // Matching both EligibilityError_Fact and EligibilityError_TriggerFact
        // just to make sure that the rule is fired when the EligibilityError_Fact is created
        // event if the EligibilityError_TriggerFact is not created somehow
        When()
            // MATCH FACTS
            //.Or(x => x
            .Match<EligibilityError_Fact>()
            //    .Match<EligibilityError_TriggerFact>()
            //)
            ;

        Then()
            .CriticalError(() => "Evaluation error")
            ;
    }

    #region Unit Testing

    [Test]
    [ExcludeFromCodeCoverage]
    public override async Task Test_Positive()
    {
        //Arrange
        await CreateActivityFromFactAsync<EligibilityError_Fact>();

        //Act
        Session.Fire();

        //Assert
        AssertFiredOnce();
    }

    [Test]
    [ExcludeFromCodeCoverage]
    public override async Task Test_Negative()
    {
        //Arrange

        //Act
        Session.Fire();

        //Assert
        AssertDidNotFire();
    }

    #endregion
}