namespace FlexCharge.Audit.Rules;

public abstract class TriggerredRuleBase<TTriggerFact> : TestableRuleBase
    where TTriggerFact : TriggerFactBase, new()
{
    public sealed override void Define()
    {
        When()
            // MATCH TRIGGER FACTS
            .Match<TTriggerFact>()
            ;

        DefineProblemCondition();

        DefineThenAction();
    }

    public abstract void DefineProblemCondition();

    public abstract void DefineThenAction();
}