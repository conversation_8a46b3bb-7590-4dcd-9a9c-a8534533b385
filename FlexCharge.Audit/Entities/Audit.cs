using System;
using System.Collections.Generic;

namespace FlexCharge.Audit.Entities
{
    public class Audit : AuditableEntity
    {
        public Guid? CorrelationId { get; set; }
        public Guid? TenantId { get; set; }

        public string Status { get; set; }
        
        public string AuditTimeRange { get; set; }
        public DateTime? AuditRangeStart { get; set; }
        public DateTime? AuditRangeEnd { get; set; }
        
        public string? ProblemsSeverity { get; set; }
        public ICollection<AuditedProblem> AuditedProblems { get; set; }
    }
}