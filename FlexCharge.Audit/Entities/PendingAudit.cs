using Microsoft.EntityFrameworkCore;
using System;

namespace FlexCharge.Audit.Entities
{
    [Index(nameof(RuleSet), nameof(CorrelationId), nameof(AuditTimeRange), /*nameof(TenantId),*/ nameof(IsRollingTime),
        IsUnique = true)]
    [Index(nameof(NextAuditTime))]
    [Index(nameof(RuleSet))]
    [Index(nameof(CorrelationId))]
    public class PendingAudit : AuditableEntity
    {
        public string RuleSet { get; set; }
        public Guid? CorrelationId { get; set; }
        public Guid? TenantId { get; set; }

        public bool IsRollingTime { get; set; }

        public DateTime? NextAuditTime { get; set; }
        public DateTime LastSeen { get; set; }

        public DateTime? LastAuditStartTime { get; set; }
        public DateTime? CheckPoint { get; set; }
        public string? AuditTimeRange { get; set; }

        public DateTime? AuditRangeStart { get; set; }
        public DateTime? AuditRangeEnd { get; set; }
    }
}