using System;
using FlexCharge.Audit.Services.AuditRules;
using FlexCharge.Common.MassTransit;
using FlexCharge.Contracts.Commands;
using Microsoft.Extensions.DependencyInjection;
using System.Threading;
using System.Threading.Tasks;

namespace FlexCharge.Audit.Consumers
{
    public class AuditActivitiesCommandConsumer
        : IdempotentCommandConsumer<AuditActivitiesCommand>
    {
        private readonly IAuditorService _auditService;

        public AuditActivitiesCommandConsumer(
            IServiceScopeFactory serviceScopeFactory,
            IAuditorService auditService) : base(serviceScopeFactory)
        {
            _auditService = auditService;
        }

        protected override async Task ConsumeCommand(AuditActivitiesCommand command,
            CancellationToken cancellationToken)
        {
            try
            {
                await _auditService.PerformAuditAsync(command.PendingAuditId, command.RuleSet, command.CorrelationId);
            }
            catch (Exception e)
            {
                Workspan.RecordException(e);
            }
        }
    }
}