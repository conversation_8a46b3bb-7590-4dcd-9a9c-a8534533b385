using FlexCharge.WorkflowEngine.Entities;
using FlexCharge.WorkflowEngine.Workflows;
using Microsoft.EntityFrameworkCore;

namespace FlexCharge.WorkflowEngine.Common.Services.WorkflowService;

public interface IWorkflowService
{
    /// <summary>
    /// Important!!! Use scoped workflowDbContext for each call to avoid problems
    /// </summary>
    /// <param name="workflow"></param>
    /// <param name="workflowDescription"></param>
    /// <param name="workflowsContext">Use scoped context for each call!!!</param>
    /// <param name="workflows"></param>
    /// <param name="updateLastRunTime"></param>
    /// <returns></returns>
    Task SerializeWorkflow(IWorkflow workflow, WorkflowDescription workflowDescription, DbContext workflowsContext,
        DbSet<Workflow> workflows, bool updateLastRunTime);
}