using FlexCharge.WorkflowEngine.Workflows;

namespace FlexCharge.WorkflowEngine.Common.Workflows.Designer.Model;

public class BlockTypeToNodeTypeConverter
{
    private static Dictionary<BlockType, NodeTypes> _blockTypeToNodeTypeMap = new()
    {
        {BlockType.Trigger, NodeTypes.Input},
        {BlockType.Group, NodeTypes.Group},
        {BlockType.Condition, NodeTypes.IfNode},
        {BlockType.ParallelExecution, NodeTypes.ParallelGroup},
        {BlockType.End, NodeTypes.FinalEndNode},
        {BlockType.GotoEnd, NodeTypes.EndNode},
    };

    private static Dictionary<NodeTypes, BlockType> _nodeTypeToBlockTypeMap { get; } =
        _blockTypeToNodeTypeMap.ToDictionary(x => x.Value, x => x.Key);

    internal static NodeTypes GetNodeType(BlockType blockType)
    {
        NodeTypes nodeType;
        if (!_blockTypeToNodeTypeMap.TryGetValue(blockType, out nodeType))
        {
            nodeType = NodeTypes.Default;
        }

        return nodeType;
    }

    internal static BlockType GetBlockType(NodeTypes nodeType)
    {
        BlockType blockType;
        if (!_nodeTypeToBlockTypeMap.TryGetValue(nodeType, out blockType))
        {
            blockType = BlockType.Default;
        }

        return blockType;
    }

    internal static BlockType GetBlockType(string nodeType)
    {
        return GetBlockType(Enum.Parse<NodeTypes>(nodeType, ignoreCase: true));
    }
}