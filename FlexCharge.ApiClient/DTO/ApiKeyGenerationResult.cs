using FlexCharge.Common.Response;
using FlexCharge.Common.SensitiveData.Obfuscation;

namespace FlexCharge.ApiClient.DTO;

public class ApiKeyGenerationResult
{
    public string Name { get; set; }
    public string UniqueName { get; set; }
    public string Description { get; set; }
    public string ClientId { get; set; }

    [SensitiveData(ObfuscationType.MaskAllChars)]
    public string Secret { get; set; }
}