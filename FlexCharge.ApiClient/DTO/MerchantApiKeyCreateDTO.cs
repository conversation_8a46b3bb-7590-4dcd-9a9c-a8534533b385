using System.ComponentModel.DataAnnotations;

namespace FlexCharge.ApiClient.DTO
{
    public class MerchantApiKeyCreateDTO
    {
        /// <summary>
        /// Description of the API key
        /// </summary>
        [Required]
        [StringLength(255, MinimumLength = 1)]
        public string Description { get; set; }

        /// <summary>
        /// Expiration time in minutes. 0 means no expiration.
        /// </summary>
        [Range(0, int.MaxValue)]
        public int Expiration { get; set; }

        /// <summary>
        /// Optional note for the API key
        /// </summary>
        [StringLength(500)]
        public string? Note { get; set; }

        /// <summary>
        /// Type of the API key (e.g., "production", "sandbox")
        /// </summary>
        [StringLength(50)]
        public string? Type { get; set; }

        /// <summary>
        /// List of scope IDs to assign to the API key
        /// </summary>
        public List<Guid>? Scopes { get; set; }
    }
}
