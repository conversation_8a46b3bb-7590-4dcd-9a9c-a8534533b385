using FlexCharge.Common.Response;

namespace FlexCharge.ApiClient.DTO;

public class StripeTokenResponseDTO : BaseResponse
{
    public string AccessToken { get; set; }
    public string RefreshToken { get; set; }
    public long? Expires { get; set; }

    public string Id { get; set; }

    //public string[] Role { get; set; }
    //public List<Claim> Claims { get; set; }
    public string? Session { get; set; }
    public int? DaysToEnforceMFA { get; set; }
    public bool? SkipAvailable { get; set; }
}