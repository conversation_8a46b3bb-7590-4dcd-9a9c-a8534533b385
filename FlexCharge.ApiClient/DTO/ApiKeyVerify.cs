using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Common.SensitiveData.Obfuscation;

namespace FlexCharge.ApiClient.DTO
{
    public class ApiKeyVerify
    {
        public string AppKey { get; set; }

        [SensitiveData(ObfuscationType.MaskAllChars)]
        public string AppSecret { get; set; }
    }
}