using FlexCharge.ApiClient.DTO;
using FlexCharge.ApiClient.Entities;
using FlexCharge.Common.Authentication;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.PostgreSql;
using FlexCharge.Common.Response;
using FlexCharge.Common.Telemetry;
using Microsoft.EntityFrameworkCore;

namespace FlexCharge.ApiClient.Services;

public class MerchantApiKeysService : IMerchantApiKeysService
{
    private readonly PostgreSQLDbContext _dbContext;
    private readonly IApiKeyService _apiKeyService;

    public MerchantApiKeysService(
        PostgreSQLDbContext dbContext,
        IApiKeyService apiKeyService)
    {
        _dbContext = dbContext;
        _apiKeyService = apiKeyService;
    }

    public async Task<MerchantApiKeysResponseDTO> GetMerchantApiKeys(
        Guid merchantId,
        int pageSize,
        int pageNumber,
        string? type = null,
        bool? revoked = null)
    {
        using var workspan = Workspan.Start<MerchantApiKeysService>()
            .Baggage("MerchantId", merchantId)
            .Baggage("PageSize", pageSize)
            .Baggage("PageNumber", pageNumber)
            .Baggage("Type", type)
            .Baggage("Revoked", revoked)
            .LogEnterAndExit();

        try
        {
            if (merchantId == Guid.Empty)
                throw new FlexChargeException("Invalid merchant ID", "api-keys");

            var query = _dbContext.ApiClientSecrets
                .Include(x => x.Client)
                .Where(x => x.Client.Mid == merchantId && !x.IsDeleted)
                .OrderByDescending(x => x.CreatedOn)
                .AsQueryable();

            // Apply filters
            if (!string.IsNullOrEmpty(type))
                query = query.Where(x => x.Type == type);

            if (revoked.HasValue)
                query = revoked.Value 
                    ? query.Where(x => x.RevokedAt.HasValue)
                    : query.Where(x => !x.RevokedAt.HasValue);

            var totalCount = await query.CountAsync();
            var skip = (pageNumber - 1) * pageSize;
            
            var apiKeys = await query
                .Skip(skip)
                .Take(pageSize)
                .GroupJoin(_dbContext.ApiClientClaims, 
                    x => x.ClientId, 
                    y => y.ApiClientId,
                    (secret, claims) => new { secret, claims })
                .Select(x => new MerchantApiKeyItemDTO
                {
                    Id = x.secret.Id,
                    Description = x.secret.Description,
                    Key = x.secret.Key,
                    Expiration = x.secret.Expiration,
                    RevokedAt = x.secret.RevokedAt,
                    Type = x.secret.Type,
                    ClientId = x.secret.ClientId,
                    LastUsed = x.secret.LastUsed,
                    Note = x.secret.Note,
                    Scopes = x.claims
                        .Where(c => c.ApiScopeId != null)
                        .Select(c => c.ApiScopeId.Value)
                        .ToList(),
                    CreatedOn = x.secret.CreatedOn,
                    ModifiedOn = x.secret.ModifiedOn
                })
                .ToListAsync();

            return new MerchantApiKeysResponseDTO
            {
                ApiKeys = new MerchantApiKeysPagedResult
                {
                    Items = apiKeys,
                    TotalCount = totalCount,
                    PageNumber = pageNumber,
                    PageSize = pageSize
                }
            };
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            throw;
        }
    }

    public async Task<ApiKeyGenerationResult> CreateMerchantApiKey(
        Guid merchantId,
        Guid userId,
        MerchantApiKeyCreateDTO payload)
    {
        using var workspan = Workspan.Start<MerchantApiKeysService>()
            .Baggage("MerchantId", merchantId)
            .Baggage("UserId", userId)
            .Baggage("Description", payload.Description)
            .Baggage("Expiration", payload.Expiration)
            .LogEnterAndExit();

        try
        {
            if (merchantId == Guid.Empty)
                throw new FlexChargeException("Invalid merchant ID", "api-keys");

            // Check if there's already an API client for this merchant
            var existingClient = await _dbContext.ApiClients
                .FirstOrDefaultAsync(x => x.Mid == merchantId && !x.IsDeleted);

            Guid clientId;
            if (existingClient != null)
            {
                clientId = existingClient.Id;
            }
            else
            {
                // Create a new API client for the merchant
                var newClient = new Entities.ApiClient
                {
                    Name = payload.Description,
                    UniqueName = payload.Description,
                    Description = payload.Description,
                    Mid = merchantId,
                    UserId = userId,
                    Uri = new Uri("https://www.flexfactor.io")
                };

                await _dbContext.AddAsync(newClient);

                // Add merchant ID claim
                _dbContext.ApiClientClaims.Add(new ApiClientClaim
                {
                    Value = merchantId.ToString(),
                    Type = MyClaimTypes.MERCHANT_ID,
                    ApiClientId = newClient.Id
                });

                await _dbContext.SaveChangesAsync();
                clientId = newClient.Id;
            }

            // Generate the API key
            var expiration = payload.Expiration == 0 ? TimeSpan.Zero : TimeSpan.FromMinutes(payload.Expiration);
            var result = await _apiKeyService.Generate(clientId, payload.Description, expiration, payload.Note);

            if (string.IsNullOrEmpty(result.ApiClientSecret.Key) ||
                string.IsNullOrEmpty(result.ApiClientSecret.Value))
            {
                workspan.Log.Error("Failed to generate API key for merchant");
                throw new FlexChargeException("Failed to generate API key");
            }

            return new ApiKeyGenerationResult
            {
                Name = payload.Description,
                UniqueName = payload.Description,
                Description = payload.Description,
                ClientId = result.ApiClientSecret.Key,
                Secret = result.ApiClientSecret.Value
            };
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            throw;
        }
    }

    public async Task UpdateMerchantApiKey(
        Guid merchantId,
        Guid keyId,
        MerchantApiKeyUpdateDTO payload)
    {
        using var workspan = Workspan.Start<MerchantApiKeysService>()
            .Baggage("MerchantId", merchantId)
            .Baggage("KeyId", keyId)
            .Payload(payload)
            .LogEnterAndExit();

        try
        {
            if (merchantId == Guid.Empty)
                throw new FlexChargeException("Invalid merchant ID", "api-keys");

            var apiKey = await _dbContext.ApiClientSecrets
                .Include(x => x.Client)
                .FirstOrDefaultAsync(x => x.Id == keyId && x.Client.Mid == merchantId && !x.IsDeleted);

            if (apiKey == null)
                throw new FlexNotFoundException("API key not found or access denied");

            // Update fields if provided
            if (!string.IsNullOrEmpty(payload.Description))
                apiKey.Description = payload.Description;

            if (payload.Note != null)
                apiKey.Note = payload.Note;

            if (!string.IsNullOrEmpty(payload.Type))
                apiKey.Type = payload.Type;

            apiKey.ModifiedOn = DateTime.UtcNow;

            // Handle scopes update if provided
            if (payload.Scopes != null)
            {
                // Remove existing scope claims
                var existingScopes = await _dbContext.ApiClientClaims
                    .Where(x => x.ApiClientId == apiKey.ClientId && x.ApiScopeId != null)
                    .ToListAsync();

                _dbContext.ApiClientClaims.RemoveRange(existingScopes);

                // Add new scope claims
                foreach (var scopeId in payload.Scopes)
                {
                    _dbContext.ApiClientClaims.Add(new ApiClientClaim
                    {
                        ApiClientId = apiKey.ClientId,
                        ApiScopeId = scopeId,
                        Type = MyClaimTypes.SCOPE,
                        Value = scopeId.ToString()
                    });
                }
            }

            await _dbContext.SaveChangesAsync();
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            throw;
        }
    }

    public async Task RevokeMerchantApiKey(
        Guid merchantId,
        Guid keyId)
    {
        using var workspan = Workspan.Start<MerchantApiKeysService>()
            .Baggage("MerchantId", merchantId)
            .Baggage("KeyId", keyId)
            .LogEnterAndExit();

        try
        {
            if (merchantId == Guid.Empty)
                throw new FlexChargeException("Invalid merchant ID", "api-keys");

            var apiKey = await _dbContext.ApiClientSecrets
                .Include(x => x.Client)
                .FirstOrDefaultAsync(x => x.Id == keyId && x.Client.Mid == merchantId && !x.IsDeleted);

            if (apiKey == null)
                throw new FlexNotFoundException("API key not found or access denied");

            if (apiKey.RevokedAt.HasValue)
                throw new FlexChargeException("API key is already revoked", "api-keys");

            await _apiKeyService.RevokeAsync(apiKey.Key, apiKey.ClientId);
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            throw;
        }
    }
}
