using FlexCharge.ApiClient.DTO;

namespace FlexCharge.ApiClient.Services;

public interface IPartnerMerchantApiKeysService
{
    /// <summary>
    /// Validate that a partner has access to a specific merchant
    /// </summary>
    /// <param name="partnerId">Partner ID</param>
    /// <param name="merchantId">Merchant ID</param>
    /// <returns>True if partner is the integration partner for the merchant</returns>
    Task<bool> ValidatePartnerMerchantAccess(Guid partnerId, Guid merchantId);

    /// <summary>
    /// Get API keys for a specific merchant (used by partner endpoints)
    /// </summary>
    /// <param name="pid">Partner ID</param>
    /// <param name="mid">Merchant ID</param>
    /// <returns>List of API keys for the merchant</returns>
    Task<ApiKeysResult> GetMerchantKeysByMid(Guid pid, Guid mid);

    /// <summary>
    /// Create a new API key for a merchant on behalf of a partner
    /// </summary>
    /// <param name="partnerId">Partner ID</param>
    /// <param name="merchantId">Merchant ID</param>
    /// <param name="userId">User ID creating the key</param>
    /// <param name="payload">API key creation request</param>
    /// <returns>Generated API key with secret (shown only once)</returns>
    Task<ApiKeyGenerationResult> CreateMerchantApiKey(Guid partnerId, Guid merchantId, Guid userId,
        PartnerMerchantApiKeyCreateDTO payload);
}