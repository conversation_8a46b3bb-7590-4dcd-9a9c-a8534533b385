using FlexCharge.ApiClient.DTO;
using FlexCharge.ApiClient.Entities;
using FlexCharge.Common.Authentication;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.PostgreSql;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.Commands.Merchants;
using MassTransit;
using Microsoft.EntityFrameworkCore;

namespace FlexCharge.ApiClient.Services;

public class PartnerMerchantApiKeysService : IPartnerMerchantApiKeysService
{
    private readonly PostgreSQLDbContext _dbContext;
    private readonly IApiKeyService _apiKeyService;
    private readonly IRequestClient<GetMerchantCommand> _getMerchantRequestClient;

    public PartnerMerchantApiKeysService(
        PostgreSQLDbContext dbContext,
        IApiKeyService apiKeyService,
        IRequestClient<GetMerchantCommand> getMerchantRequestClient)
    {
        _dbContext = dbContext;
        _apiKeyService = apiKeyService;
        _getMerchantRequestClient = getMerchantRequestClient;
    }

    public async Task<bool> ValidatePartnerMerchantAccess(Guid partnerId, Guid merchantId)
    {
        using var workspan = Workspan.Start<PartnerMerchantApiKeysService>()
            .Baggage("PartnerId", partnerId)
            .Baggage("MerchantId", merchantId)
            .LogEnterAndExit();

        try
        {
            // Use MassTransit to get merchant information from the Merchants microservice
            var response = await _getMerchantRequestClient.GetResponse<GetMerchantCommandResponse>(
                new GetMerchantCommand {MerchantId = merchantId});

            var merchantInfo = response.Message;

            if (!string.IsNullOrEmpty(merchantInfo.Error))
            {
                workspan.Log.Warning($"Error getting merchant info: {merchantInfo.Error}");
                return false;
            }

            // Check if the partner is the integration partner for this merchant
            return merchantInfo.IntegrationPartnerId == partnerId;
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            throw;
        }
    }

    public async Task<ApiKeysResult> GetMerchantKeysByMid(Guid pid, Guid mid)
    {
        using var workspan = Workspan.Start<PartnerMerchantApiKeysService>()
            .Baggage("Mid", mid)
            .Baggage("Pid", pid)
            .LogEnterAndExit();

        var response = new ApiKeysResult();

        try
        {
            // Check for empty merchant ID first
            if (mid == Guid.Empty)
                throw new FlexChargeException("invalid merchant id", "api-keys");

            var getMerchantResponse = await _getMerchantRequestClient.GetResponse<GetMerchantCommandResponse>(
                new GetMerchantCommand {MerchantId = mid});

            if (getMerchantResponse.Message.Error != null)
                throw new FlexChargeException(getMerchantResponse.Message.Error, "api-keys");

            // Check if the partner has access to this merchant
            if (getMerchantResponse.Message.IntegrationPartnerId != pid)
                throw new UnauthorizedAccessException("Unauthorized access");

            var keys = _dbContext.ApiClientSecrets
                .Include(x => x.Client)
                .Where(x => !x.RevokedAt.HasValue)
                .Where(x => x.Client.Mid == mid)
                .AsQueryable();

            response.Keys = await keys.Select(key => new ApiKeyDTO
            {
                Key = key.Key,
                Secret = key.Value
            }).ToListAsync();

            if (!response.Keys.Any())
                throw new FlexChargeException("api-keys", "No keys found");

            return response;
        }
        catch (FlexChargeException e)
        {
            workspan.RecordException(e);
            throw;
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            throw;
        }
    }

    public async Task<ApiKeyGenerationResult> CreateMerchantApiKey(Guid partnerId, Guid merchantId, Guid userId,
        PartnerMerchantApiKeyCreateDTO payload)
    {
        using var workspan = Workspan.Start<PartnerMerchantApiKeysService>()
            .Baggage("PartnerId", partnerId)
            .Baggage("MerchantId", merchantId)
            .Baggage("UserId", userId)
            .Baggage("Description", payload.Description)
            .Baggage("Expiration", payload.Expiration)
            .LogEnterAndExit();

        var response = new ApiKeyGenerationResult();

        try
        {
            // Validate that the partner has access to this merchant
            var hasAccess = await ValidatePartnerMerchantAccess(partnerId, merchantId);
            if (!hasAccess)
            {
                workspan.Log.Warning(
                    $"Partner {partnerId} attempted to create API key for unauthorized merchant {merchantId}");
                throw new UnauthorizedAccessException(
                    "Access denied: Merchant does not belong to your partner account");
            }

            // Check if there's already an API client for this merchant
            var existingClient = await _dbContext.ApiClients
                .FirstOrDefaultAsync(x => x.Mid == merchantId && !x.IsDeleted);

            Guid clientId;
            if (existingClient != null)
            {
                clientId = existingClient.Id;
            }
            else
            {
                // Create a new API client for the merchant
                var newClient = new Entities.ApiClient
                {
                    Name = payload.Description,
                    UniqueName = payload.Description,
                    Description = payload.Description,
                    Mid = merchantId, // Set merchant ID instead of partner ID
                    Pid = partnerId, // Still track which partner created it
                    UserId = userId,
                    Uri = new Uri("https://www.flexfactor.io")
                };

                await _dbContext.AddAsync(newClient);

                // Add merchant ID claim
                _dbContext.ApiClientClaims.Add(new ApiClientClaim
                {
                    Value = merchantId.ToString(),
                    Type = MyClaimTypes.MERCHANT_ID,
                    ApiClientId = newClient.Id
                });

                // Add partner ID claim to track who created it
                _dbContext.ApiClientClaims.Add(new ApiClientClaim
                {
                    Value = partnerId.ToString(),
                    Type = MyClaimTypes.PARTNER_ID,
                    ApiClientId = newClient.Id
                });

                await _dbContext.SaveChangesAsync();
                clientId = newClient.Id;
            }

            // Generate the API key
            var expiration = payload.Expiration == 0 ? TimeSpan.Zero : TimeSpan.FromMinutes(payload.Expiration);
            var result = await _apiKeyService.Generate(clientId, payload.Description, expiration, payload.Note);

            if (string.IsNullOrEmpty(result.ApiClientSecret.Key) ||
                string.IsNullOrEmpty(result.ApiClientSecret.Value))
            {
                workspan.Log.Error("ERROR Generating API key for merchant");
                throw new FlexChargeException("Failed to generate API key");
            }

            // Update the API key with additional properties if provided
            if (!string.IsNullOrEmpty(payload.Type) || payload.Scopes?.Any() == true)
            {
                var apiKey = await _dbContext.ApiClientSecrets
                    .FirstOrDefaultAsync(x => x.Id == result.ApiClientSecret.Id);

                if (apiKey != null)
                {
                    if (!string.IsNullOrEmpty(payload.Type))
                    {
                        apiKey.Type = payload.Type;
                    }

                    _dbContext.ApiClientSecrets.Update(apiKey);

                    // Add scopes if provided
                    if (payload.Scopes?.Any() == true)
                    {
                        foreach (var scopeId in payload.Scopes)
                        {
                            _dbContext.ApiClientClaims.Add(new ApiClientClaim
                            {
                                Value = scopeId.ToString(),
                                Type = MyClaimTypes.SCOPE,
                                ApiClientId = clientId,
                                ApiScopeId = scopeId
                            });
                        }
                    }

                    await _dbContext.SaveChangesAsync();
                }
            }

            response.ClientId = result.ApiClientSecret.Key;
            response.Secret = result.ApiClientSecret.Value;
            response.Name = result.ApiClient.Name;
            response.UniqueName = result.ApiClient.UniqueName;
            response.Description = result.ApiClient.Description;

            workspan.Log.Information($"Successfully created API key for merchant {merchantId} by partner {partnerId}");
            return response;
        }
        catch (FlexChargeException e)
        {
            workspan.RecordException(e);
            throw;
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            throw;
        }
    }
}