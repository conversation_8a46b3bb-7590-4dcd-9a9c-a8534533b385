using FlexCharge.ApiClient.DTO;

namespace FlexCharge.ApiClient.Services;

public interface IMerchantApiKeysService
{
    /// <summary>
    /// Get API keys for the authenticated merchant
    /// </summary>
    /// <param name="merchantId">Merchant ID</param>
    /// <param name="pageSize">Number of items per page</param>
    /// <param name="pageNumber">Page number</param>
    /// <param name="type">Optional filter by key type</param>
    /// <param name="revoked">Optional filter by revoked status</param>
    /// <returns>Paginated list of API keys for the merchant</returns>
    Task<MerchantApiKeysResponseDTO> GetMerchantApiKeys(
        Guid merchantId,
        int pageSize,
        int pageNumber,
        string? type = null,
        bool? revoked = null);

    /// <summary>
    /// Create a new API key for the authenticated merchant
    /// </summary>
    /// <param name="merchantId">Merchant ID</param>
    /// <param name="userId">User ID creating the key</param>
    /// <param name="payload">API key creation request</param>
    /// <returns>Generated API key with secret (shown only once)</returns>
    Task<ApiKeyGenerationResult> CreateMerchantApiKey(
        Guid merchantId,
        Guid userId,
        MerchantApiKeyCreateDTO payload);

    /// <summary>
    /// Update an existing API key for the authenticated merchant
    /// </summary>
    /// <param name="merchantId">Merchant ID</param>
    /// <param name="keyId">API key ID</param>
    /// <param name="payload">Update request</param>
    /// <returns>Success response</returns>
    Task UpdateMerchantApiKey(
        Guid merchantId,
        Guid keyId,
        MerchantApiKeyUpdateDTO payload);

    /// <summary>
    /// Revoke an API key for the authenticated merchant
    /// </summary>
    /// <param name="merchantId">Merchant ID</param>
    /// <param name="keyId">API key ID to revoke</param>
    /// <returns>Success response</returns>
    Task RevokeMerchantApiKey(
        Guid merchantId,
        Guid keyId);
}
