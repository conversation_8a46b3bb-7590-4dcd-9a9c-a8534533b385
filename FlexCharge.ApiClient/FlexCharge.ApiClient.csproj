<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
        <WarningsAsErrors>CS4014</WarningsAsErrors>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.0"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.0">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Stripe.net" Version="47.3.0"/>
        <PackageReference Include="Swashbuckle.AspNetCore" Version="6.2.3" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\FlexCharge.Common\FlexCharge.Common.csproj" />
      <ProjectReference Include="..\FlexCharge.Contracts\FlexCharge.Contracts.csproj" />
    </ItemGroup>

    <ItemGroup>
        <Folder Include="Enums\"/>
        <Folder Include="OAuth\AuthorizationHandlers\"/>
    </ItemGroup>

</Project>
