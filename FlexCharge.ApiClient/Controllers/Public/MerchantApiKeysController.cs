using FlexCharge.ApiClient.Authorization;
using FlexCharge.Common;
using FlexCharge.Common.Authentication;
using FlexCharge.ApiClient.Services;
using FlexCharge.ApiClient.DTO;
using FlexCharge.Common.Authentication.BasicAuthentication;
using FlexCharge.Common.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using FlexCharge.Common.Telemetry;

namespace FlexCharge.ApiClient.Controllers.Public
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize(AuthenticationSchemes = AuthenticationSchemas.BasicAuthentication)]
    public class MerchantApiKeysController : BaseController
    {
        private readonly IMerchantApiKeysService _merchantApiKeysService;
        private readonly AppOptions _globalOptions;

        public MerchantApiKeysController(
            IOptions<AppOptions> globalOptions,
            IMerchantApiKeysService merchantApiKeysService)
        {
            _globalOptions = globalOptions.Value;
            _merchantApiKeysService = merchantApiKeysService;
        }

        /// <summary>
        /// Get API keys for the authenticated merchant
        /// </summary>
        /// <param name="pageSize">Number of items per page (default: 10, max: 100)</param>
        /// <param name="pageNumber">Page number (default: 1)</param>
        /// <param name="type">Optional filter by key type (e.g., "production", "sandbox")</param>
        /// <param name="revoked">Optional filter by revoked status</param>
        /// <returns>Paginated list of API keys for the merchant</returns>
        [HttpGet]
        [Authorize(ApiClientPolicies.CanRead)]
        [ProducesResponseType(typeof(MerchantApiKeysResponseDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetApiKeys(
            [FromQuery] int pageSize = 10,
            [FromQuery] int pageNumber = 1,
            [FromQuery] string? type = null,
            [FromQuery] bool? revoked = null)
        {
            using var workspan = Workspan.StartEndpoint<MerchantApiKeysController>(this, null, _globalOptions)
                .Baggage("pageSize", pageSize)
                .Baggage("pageNumber", pageNumber)
                .Baggage("type", type)
                .Baggage("revoked", revoked);

            try
            {
                if (!ModelState.IsValid) return ValidationProblem();

                // Validate pagination parameters
                if (pageSize <= 0 || pageSize > 100)
                {
                    return BadRequest("Page size must be between 1 and 100");
                }

                if (pageNumber <= 0)
                {
                    return BadRequest("Page number must be greater than 0");
                }

                var merchantId = GetMID();

                if (merchantId == Guid.Empty)
                {
                    return BadRequest("Merchant ID not found in token claims");
                }

                var apiKeys = await _merchantApiKeysService.GetMerchantApiKeys(
                    merchantId,
                    pageSize,
                    pageNumber,
                    type,
                    revoked);

                if (!apiKeys.Success)
                    return ReturnResponse(apiKeys);

                workspan.Log.Information(
                    $"Merchant {merchantId} accessed API keys list - Page: {pageNumber}, Size: {pageSize}");
                return Ok(apiKeys);
            }
            catch (FlexValidationException vex)
            {
                ModelState.AddModelError(vex.Property, vex.Message);
                return ValidationProblem(ModelState);
            }
            catch (FlexNotFoundException e)
            {
                workspan.RecordException(e);
                return NotFound(e.Message);
            }
            catch (UnauthorizedAccessException e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status401Unauthorized, e.Message);
            }
            catch (FlexChargeException e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, e.Message);
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed fetching records");
            }
        }

        /// <summary>
        /// Create a new API key for the authenticated merchant
        /// </summary>
        /// <param name="payload">API key creation request</param>
        /// <returns>Created API key with the secret value (shown only once)</returns>
        [HttpPost]
        [Authorize(ApiClientPolicies.CanWrite)]
        [ProducesResponseType(typeof(ApiKeyGenerationResult), StatusCodes.Status201Created)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> CreateApiKeys([FromBody] MerchantApiKeyCreateDTO payload)
        {
            using var workspan = Workspan.StartEndpoint<MerchantApiKeysController>(this, payload, _globalOptions);

            try
            {
                if (!ModelState.IsValid) return ValidationProblem();

                var userId = GetUID();
                var merchantId = GetMID();

                workspan
                    .Baggage("UserId", userId)
                    .Baggage("MerchantId", merchantId);

                if (merchantId == Guid.Empty)
                {
                    return BadRequest("Merchant ID not found in token claims");
                }

                var result = await _merchantApiKeysService.CreateMerchantApiKey(
                    merchantId,
                    userId,
                    payload);

                workspan.Log.Information($"Merchant {merchantId} created new API key");
                return CreatedAtAction(nameof(GetApiKeys), new { }, result);
            }
            catch (FlexValidationException vex)
            {
                ModelState.AddModelError(vex.Property, vex.Message);
                return ValidationProblem(ModelState);
            }
            catch (FlexNotFoundException e)
            {
                workspan.RecordException(e);
                return NotFound(e.Message);
            }
            catch (UnauthorizedAccessException e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status401Unauthorized, e.Message);
            }
            catch (FlexChargeException e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, e.Message);
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed creating API key");
            }
        }

        /// <summary>
        /// Update an existing API key (description, note, type)
        /// </summary>
        /// <param name="keyId">API key ID</param>
        /// <param name="payload">Update request</param>
        /// <returns>Success response</returns>
        [HttpPut("{keyId:guid}")]
        [Authorize(ApiClientPolicies.CanManage)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> UpdateApiKey(Guid keyId, [FromBody] MerchantApiKeyUpdateDTO payload)
        {
            using var workspan =
                Workspan.StartEndpoint<MerchantApiKeysController>(this, new {keyId, payload}, _globalOptions);

            try
            {
                if (!ModelState.IsValid) return ValidationProblem();

                var merchantId = GetMID();
                if (merchantId == Guid.Empty)
                {
                    return BadRequest("Merchant ID not found in token claims");
                }

                var result = await _merchantApiKeysService.UpdateMerchantApiKey(merchantId, keyId, payload);

                if (!result.Success)
                    return ReturnResponse(result);

                workspan.Log.Information($"Merchant {merchantId} updated API key {keyId}");
                return Ok(result);
            }
            catch (FlexValidationException vex)
            {
                ModelState.AddModelError(vex.Property, vex.Message);
                return ValidationProblem(ModelState);
            }
            catch (FlexNotFoundException e)
            {
                workspan.RecordException(e);
                return NotFound(e.Message);
            }
            catch (UnauthorizedAccessException e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status401Unauthorized, e.Message);
            }
            catch (FlexChargeException e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, e.Message);
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed updating API key");
            }
        }

        /// <summary>
        /// Revoke an API key
        /// </summary>
        /// <param name="keyId">API key ID to revoke</param>
        /// <returns>Success response</returns>
        [HttpDelete("{keyId:guid}")]
        [Authorize(ApiClientPolicies.CanManage)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> RevokeApiKey(Guid keyId)
        {
            using var workspan = Workspan.StartEndpoint<MerchantApiKeysController>(this, keyId, _globalOptions);

            try
            {
                if (!ModelState.IsValid) return ValidationProblem();

                var merchantId = GetMID();
                if (merchantId == Guid.Empty)
                {
                    return BadRequest("Merchant ID not found in token claims");
                }

                var result = await _merchantApiKeysService.RevokeMerchantApiKey(merchantId, keyId);

                if (!result.Success)
                    return ReturnResponse(result);

                workspan.Log.Information($"Merchant {merchantId} revoked API key {keyId}");
                return Ok(result);
            }
            catch (FlexValidationException vex)
            {
                ModelState.AddModelError(vex.Property, vex.Message);
                return ValidationProblem(ModelState);
            }
            catch (FlexNotFoundException e)
            {
                workspan.RecordException(e);
                return NotFound(e.Message);
            }
            catch (UnauthorizedAccessException e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status401Unauthorized, e.Message);
            }
            catch (FlexChargeException e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, e.Message);
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed revoking API key");
            }
        }
    }
}
