using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.ApiClient.Migrations
{
    /// <inheritdoc />
    public partial class addedRelatedEntitycolumnstoOAuthAccessTokenstable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "RelatedEntityId",
                table: "OAuthAccessTokens",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.AddColumn<string>(
                name: "RelatedEntityType",
                table: "OAuthAccessTokens",
                type: "text",
                nullable: false,
                defaultValue: "");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "RelatedEntityId",
                table: "OAuthAccessTokens");

            migrationBuilder.DropColumn(
                name: "RelatedEntityType",
                table: "OAuthAccessTokens");
        }
    }
}
