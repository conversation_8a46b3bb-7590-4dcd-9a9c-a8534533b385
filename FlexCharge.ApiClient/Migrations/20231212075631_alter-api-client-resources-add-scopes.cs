using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.ApiClient.Migrations
{
    /// <inheritdoc />
    public partial class alterapiclientresourcesaddscopes : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "Description",
                table: "ApiResources",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "FreindlyName",
                table: "ApiResources",
                type: "text",
                nullable: false,
                defaultValue: "");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Description",
                table: "ApiResources");

            migrationBuilder.DropColumn(
                name: "FreindlyName",
                table: "ApiResources");
        }
    }
}
