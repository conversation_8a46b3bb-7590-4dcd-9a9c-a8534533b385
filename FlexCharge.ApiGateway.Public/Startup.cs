using FlexCharge.Common;
using FlexCharge.Common.Authentication;
using FlexCharge.Common.Swagger;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;

namespace FlexCharge.ApiGateway.Public
{
    public class Startup
    {
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            services.AddTransient<IHttpContextAccessor, HttpContextAccessor>();
            services.AddTransient(typeof(IOptions<>), typeof(OptionsManager<>));

            services.Configure<AppOptions>(Configuration.GetSection("app"));
            services.AddOptions();
            
            services.AddJwt();
            
            services.AddAuthorization(options =>
                {
                    options.AddPolicy(MyPolicies.ADMINS_ONLY,
                        policy => policy.RequireClaim(SuperAdminGroups.MERCHANT_ADMIN));
                    options.AddPolicy(MyPolicies.USERS,
                        policy => policy.RequireClaim(SuperAdminGroups.USER));
                }
            );

            services.AddAutoMapper(typeof(Startup));
            services.AddControllers();
            services.AddSwaggerDocs();


            services.AddCors(options =>
            {
                options.AddPolicy("CorsPolicy", cors =>
                    cors.AllowAnyMethod()
                        .AllowAnyOrigin()
                        .AllowAnyHeader());
            });
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IHostApplicationLifetime applicationLifetime,
            IWebHostEnvironment env)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
                // Enable middleware to serve generated Swagger as a JSON endpoint.
            }
            app.UseCors("CorsPolicy");

            //app.UseHttpsRedirection();
            app.UseSwaggerDocs();

            app.UseRouting();
            app.UseAuthorization();

            app.UseEndpoints(endpoints => { endpoints.MapControllers(); });
        }
        
    }
}