// using System;
// using System.ComponentModel.DataAnnotations.Schema;
//
// namespace FlexCharge.Eligibility.Entities;
//
// public class Activity : AuditableEntity
// {
//     public DateTime ActionTimestamp { get; set; }
//     
//     public bool IsRoot { get; set; }
//
//     public Guid PreviousActionId { get; set; }
//
//     public string Source { get; set; }
//
//     public Guid TenantId { get; set; }
//
//     public string Event { get; set; }
//
//     public string Domain { get; set; }
//
//     public Guid CorrelationId { get; set; }
//
//     public string Version { get; set; }
//
//     [Column(TypeName = "jsonb")] public string Meta { get; set; }
//
//     [Column(TypeName = "jsonb")] public string Data { get; set; }
//
//     public string Category { get; set; }
//     public string SubCategory { get; set; }
//     public string Name { get; set; }
//
//     public string Value { get; set; }
//
//     public string ActionResult { get; set; }
//     
//     public string AccessLevel { get; set; }
//     
//     public int InformationLevel { get; set; }
// }

