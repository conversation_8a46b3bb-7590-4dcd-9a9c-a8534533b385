// using System;
// using System.ComponentModel.DataAnnotations.Schema;
//
// namespace FlexCharge.Eligibility.Entities;
//
// [Table("configurations")]
// public class SenseJSMerchantConfiguration
// {
//     [Column("id")]
//     public System.Guid Id { get; set; }
//
//     public Guid MerchantId { get; set; }
//     
//     [Column(TypeName = "jsonb")]
//     public string ScrapingData { get; set; }
//     
//     [Column(TypeName = "jsonb")]
//     public string SubmitData { get; set; }
//     
//     [Column(TypeName = "jsonb")]
//     public string CheckoutUrl { get; set; }
//     
//     public string SuccessUrl { get; set; }
//     
//     public string RejectUrl { get; set; }
//     
//     public string CancelUrl { get; set; }
//     
//     public int ConsentFlowConfiguration { get; set; }
//     
//     public string Logo { get; set; }
//     
//     public string PrimaryColor { get; set; }
//     
//     public string SecondaryColor { get; set; }
//     
//     public string TokenizationPublicKey { get; set; }
//     
//     public string FontFamily { get; set; }
//     
//     public bool IsLogoVisible { get; set; }
//     
//     public string ButtonsCornersRadius { get; set; }
//     
//     public string ModalCornersRadius { get; set; }
//     
//
//     [Column("createdAt")]
//     public DateTime CreatedAt { get; set; }
//     
//     [Column("updatedAt")]
//     public DateTime UpdatedAt { get; set; }
//     
//     [Column("createdBy")]
//     public string CreatedBy { get; set; }
//     
//     [Column("updatedBy")]
//     public string UpdatedBy { get; set; }
// }