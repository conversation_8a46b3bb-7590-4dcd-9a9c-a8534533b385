using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Eligibility.Migrations
{
    /// <inheritdoc />
    public partial class addedExternalRecyclingEnginerelatedcolumnsinOrderstable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsProcessingByExternalRecyclingEngine",
                table: "Orders",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "RecyclingEngine",
                table: "Orders",
                type: "text",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsProcessingByExternalRecyclingEngine",
                table: "Orders");

            migrationBuilder.DropColumn(
                name: "RecyclingEng<PERSON>",
                table: "Orders");
        }
    }
}
