using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Eligibility.Migrations
{
    public partial class Merchant_Add_Dba : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "<PERSON><PERSON>",
                table: "Merchants",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsLocked",
                table: "Merchants",
                type: "boolean",
                nullable: false,
                defaultValue: false);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "<PERSON><PERSON>",
                table: "Merchants");

            migrationBuilder.DropColumn(
                name: "IsLocked",
                table: "Merchants");
        }
    }
}
