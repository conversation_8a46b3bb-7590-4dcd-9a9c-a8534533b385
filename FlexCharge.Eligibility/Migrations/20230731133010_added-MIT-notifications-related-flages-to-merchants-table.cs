using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Eligibility.Migrations
{
    /// <inheritdoc />
    public partial class addedMITnotificationsrelatedflagestomerchantstable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "MITConsumerCuresEnabled",
                table: "Merchants",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "MITConsumerNotificationsEnabled",
                table: "Merchants",
                type: "boolean",
                nullable: false,
                defaultValue: false);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "MITConsumerCuresEnabled",
                table: "Merchants");

            migrationBuilder.DropColumn(
                name: "MITConsumerNotificationsEnabled",
                table: "Merchants");
        }
    }
}
