// <auto-generated />
using System;
using FlexCharge.Eligibility;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace FlexCharge.Eligibility.Migrations
{
    [DbContext(typeof(PostgreSQLDbContext))]
    [Migration("20220720153335_payment-transaction-responses-column-to-orders-table")]
    partial class paymenttransactionresponsescolumntoorderstable
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "6.0.1")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.HasPostgresEnum(modelBuilder, "order_state", new[] { "not_eligible", "not_eligible_threshold_limit", "not_eligible_expired", "order_initiated", "eligibility_stage1_passed", "consent_received", "consent_rejected", "eligibility_stage2_passed", "conditional_consumer_interaction", "conditional_internal", "eligibility_stage3_passed", "order_approved" });
            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("FlexCharge.Eligibility.Entities.Activity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("ActivityId")
                        .HasColumnType("uuid");

                    b.Property<string>("ActivityType")
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Data")
                        .HasColumnType("jsonb");

                    b.Property<string>("Event")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Meta")
                        .HasColumnType("jsonb");

                    b.Property<Guid>("Mid")
                        .HasColumnType("uuid");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("OrderId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("PreviousActionId")
                        .HasColumnType("uuid");

                    b.Property<string>("Source")
                        .HasColumnType("text");

                    b.Property<string>("Version")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("OrderId");

                    b.ToTable("Activities");
                });

            modelBuilder.Entity("FlexCharge.Eligibility.Entities.ExecutedCure", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<Guid>("OrderId")
                        .HasColumnType("uuid");

                    b.Property<string>("Payload")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("OrderId");

                    b.ToTable("ExecutedCures");
                });

            modelBuilder.Entity("FlexCharge.Eligibility.Entities.Merchant", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("IntegrationType")
                        .HasColumnType("integer");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsBureauActiveForProduction")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Mcc")
                        .HasColumnType("text");

                    b.Property<Guid>("Mid")
                        .HasColumnType("uuid");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("Pid")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.ToTable("Merchants");
                });

            modelBuilder.Entity("FlexCharge.Eligibility.Entities.Order", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("Amount")
                        .HasColumnType("integer");

                    b.Property<string>("BureauResults")
                        .HasColumnType("jsonb");

                    b.Property<DateTime?>("CompletedTimestamp")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("ConsentTimestamp")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("CureSequenceCounter")
                        .HasColumnType("integer");

                    b.Property<string>("CurrentCureId")
                        .HasColumnType("text");

                    b.Property<string>("ExternalOrderReference")
                        .HasColumnType("text");

                    b.Property<string>("FraudResults")
                        .HasColumnType("jsonb");

                    b.Property<string>("FullAutohrizationResult")
                        .HasColumnType("jsonb");

                    b.Property<string>("FullBureauResults")
                        .HasColumnType("jsonb");

                    b.Property<string>("FullFraudResults")
                        .HasColumnType("jsonb");

                    b.Property<bool>("IsBureauChecked")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsFraudChecked")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsFullAuthorizationChecked")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsZeroVerificationChecked")
                        .HasColumnType("boolean");

                    b.Property<Guid>("Mid")
                        .HasColumnType("uuid");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Payload")
                        .HasColumnType("text");

                    b.Property<string>("PaymentInstrumentFingerprint")
                        .HasColumnType("text");

                    b.Property<string>("PaymentInstrumentToken")
                        .HasColumnType("text");

                    b.Property<string>("PaymentTransactionsResponses")
                        .HasColumnType("text");

                    b.Property<string>("SenseKey")
                        .HasColumnType("text");

                    b.Property<string>("State")
                        .HasColumnType("text");

                    b.Property<string>("ZeroVerificationResult")
                        .HasColumnType("jsonb");

                    b.HasKey("Id");

                    b.HasIndex("SenseKey");

                    b.ToTable("Orders");
                });

            modelBuilder.Entity("FlexCharge.Eligibility.Entities.ResponseCodes", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Messsage")
                        .HasColumnType("text");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ResponseCode")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("ResponseCodes");
                });

            modelBuilder.Entity("FlexCharge.Eligibility.Entities.Activity", b =>
                {
                    b.HasOne("FlexCharge.Eligibility.Entities.Order", "Order")
                        .WithMany()
                        .HasForeignKey("OrderId");

                    b.Navigation("Order");
                });

            modelBuilder.Entity("FlexCharge.Eligibility.Entities.ExecutedCure", b =>
                {
                    b.HasOne("FlexCharge.Eligibility.Entities.Order", null)
                        .WithMany("ExecutedCures")
                        .HasForeignKey("OrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("FlexCharge.Eligibility.Entities.Order", b =>
                {
                    b.Navigation("ExecutedCures");
                });
#pragma warning restore 612, 618
        }
    }
}
