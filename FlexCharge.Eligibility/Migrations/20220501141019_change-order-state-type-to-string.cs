using FlexCharge.Eligibility.Enums;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Eligibility.Migrations
{
    public partial class changeorderstatetypetostring : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "State",
                table: "Orders",
                type: "text",
                nullable: true,
                oldClrType: typeof(OrderState),
                oldType: "order_state");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<OrderState>(
                name: "State",
                table: "Orders",
                type: "order_state",
                nullable: false,
                defaultValue: OrderState.NOT_ELIGIBLE,
                oldClrType: typeof(string),
                oldType: "text",
                oldNullable: true);
        }
    }
}
