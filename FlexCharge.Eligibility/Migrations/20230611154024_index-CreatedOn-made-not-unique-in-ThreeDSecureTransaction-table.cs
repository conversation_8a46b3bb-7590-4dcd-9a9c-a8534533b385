using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Eligibility.Migrations
{
    /// <inheritdoc />
    public partial class indexCreatedOnmadenotuniqueinThreeDSecureTransactiontable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_ThreeDSecureTransactions_SenseKey",
                table: "ThreeDSecureTransactions");

            migrationBuilder.CreateIndex(
                name: "IX_ThreeDSecureTransactions_SenseKey",
                table: "ThreeDSecureTransactions",
                column: "SenseKey");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_ThreeDSecureTransactions_SenseKey",
                table: "ThreeDSecureTransactions");

            migrationBuilder.CreateIndex(
                name: "IX_ThreeDSecureTransactions_SenseKey",
                table: "ThreeDSecureTransactions",
                column: "SenseK<PERSON>",
                unique: true);
        }
    }
}
