using FlexCharge.Common.Exceptions;

namespace FlexCharge.Eligibility.Exceptions.Eligibility;

public abstract class ConcurrentEvaluationException : FlexChargeException
{
}

public class ConcurrentEvaluationCannotObtainLockException : ConcurrentEvaluationException
{
}

public class ConcurrentEvaluationSenseKeyMismatchException : ConcurrentEvaluationException
{
}

public class ConcurrentEvaluationOrderAlreadyApprovedException : ConcurrentEvaluationException
{
}