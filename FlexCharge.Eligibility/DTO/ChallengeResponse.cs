using System;
using System.Collections.Generic;
using FlexCharge.Common.Response;
using FlexCharge.Common.Shared.UIBuilder.DTO;

namespace FlexCharge.Eligibility.DTO
{
    public class ChallengeResponse : BaseResponse, 
        IOptionalEligibilitySessionIdPair,
        INextPostResponse
    {
        public Guid? OrderSessionKey { get; set; }
        public string? SenseKey { get; set; }

        public bool IsUserCancellable { get; set; }

        public List<ChallengeItemDTO> Challenge { get; set; }
        public Dictionary<string, string> Parameters { get; set; }
    }


    //public class EligibilityPutRequest : AbstractValidator<RoomDTO>
    //{
    //    public EligibilityPutRequest()
    //    {

    //    }
    //}
}