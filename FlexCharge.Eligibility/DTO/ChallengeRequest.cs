using System;
using System.ComponentModel.DataAnnotations;
using FlexCharge.Common.Mvc.Validators;

namespace FlexCharge.Eligibility.DTO
{
    public class ChallengeRequest : IEligibilitySessionIdPair
    {
        [Required] public Guid OrderSessionKey { get; set; }
        [Required] [NoUnicodeNullCharacters] public string SenseKey { get; set; }
        [Required] public Guid Mid { get; set; }

        [NoUnicodeNullCharacters] public string? Language { get; set; }

        //public Guid? IdempotencyKey { get; set; }
    }
}