using System;
using FlexCharge.Common.Response;

namespace FlexCharge.Eligibility.DTO
{
    public class ChallengeReEvaluateResponse : BaseResponse, 
        IOptionalEligibilitySessionIdPair,
        IOptionalExternalOrderReference
    {
        public Guid? OrderSessionKey { get; set; }
        public string? SenseKey { get; set; }
        public string? ExternalOrderReference { get; set; }
        public int? Amount { get; set; }
        public string? CurrencySymbol { get; set; }
    }
}