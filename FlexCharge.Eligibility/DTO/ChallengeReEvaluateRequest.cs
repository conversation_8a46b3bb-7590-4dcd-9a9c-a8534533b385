using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using FlexCharge.Common.Mvc.Validators;

namespace FlexCharge.Eligibility.DTO
{
    public class ChallengeReEvaluateRequest : IEligibilitySessionIdPair
    {
        [Required] public Guid OrderSessionKey { get; set; }
        [Required] [NoUnicodeNullCharacters] public string Sense<PERSON>ey { get; set; }
        [Required] public Guid Mid { get; set; }

        public IDictionary<Guid, string> Answers { get; set; }
        //public Guid? IdempotencyKey { get; set; }
    }

    //public class EligibilityPutRequest : AbstractValidator<DTO>
    //{
    //    public EligibilityPutRequest()
    //    {

    //    }
    //}
}