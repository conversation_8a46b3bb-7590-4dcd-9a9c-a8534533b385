<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
      <TargetFramework>net9.0</TargetFramework>
    <UserSecretsId>99bbd081-7f55-4d77-9823-a712a2c8e7e0</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerComposeProjectPath>..\docker-compose.dcproj</DockerComposeProjectPath>
    <Configurations>Debug;Release;Staging</Configurations>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <WarningsAsErrors>CS4014</WarningsAsErrors>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DocumentationFile>bin\$(Configuration)\$(AssemblyName).xml</DocumentationFile>
    <NoWarn>1701;1702;1591;</NoWarn>
  </PropertyGroup>

  <ItemGroup>
      <PackageReference Include="AWSSDK.DynamoDBv2" Version="3.7.400.2"/>
    <PackageReference Include="AWSSDK.Kinesis" Version="3.7.100.21" />
      <PackageReference Include="EntityFrameworkCore.Exceptions.PostgreSQL" Version="8.1.3"/>
    <PackageReference Include="HtmlAgilityPack" Version="1.11.53" />
    <PackageReference Include="Kount.Net.RisCoreSDK" Version="1.0.0" />
    <PackageReference Include="MediatR" Version="8.0.1" />
    <PackageReference Include="MediatR.Extensions.Microsoft.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="6.0.2" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.0"/>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.10.8" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.ServiceModel.Duplex" Version="4.8.*" />
    <PackageReference Include="System.ServiceModel.Http" Version="4.8.*" />
    <PackageReference Include="System.ServiceModel.NetTcp" Version="4.8.*" />
    <PackageReference Include="System.ServiceModel.Security" Version="4.8.*" />
    
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\FlexCharge.Common\FlexCharge.Common.csproj" />
    <ProjectReference Include="..\FlexCharge.Contracts\FlexCharge.Contracts.csproj" />
      <ProjectReference Include="..\FlexCharge.Eligibility.Adapters.Stripe\FlexCharge.Eligibility.Adapters.Stripe.csproj"/>
    <ProjectReference Include="..\FlexCharge.Utils\FlexCharge.Utils.csproj" />
      <ProjectReference Include="..\FlexCharge.WorkflowEngine.Common\FlexCharge.WorkflowEngine.Common.csproj"/>
  </ItemGroup>

  <ItemGroup>
    <Content Update="appsettings.Production.json"/>
    <Content Remove="EligibilityChecks\Sequence\Checks\**"/>
    <None Remove="Resources\Lists\ListOfBINs.csv"/>
    <None Remove="Resources\Lists\BinLists\Remove_Device_BinToIssuerList.csv"/>
    <Content Include="Resources\Lists\BinLists\Remove_Device_BinToIssuerList.csv">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
      <None Remove="Resources\Lists\BinLists\BINs_to_Groups.csv"/>
      <Content Include="Resources\Lists\BinLists\BinToGroupList.csv">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </Content>
      <None Remove="Resources\Lists\DisputeOptimization\IssuerToDisputeClassMap.csv"/>
      <Content Include="Resources\Lists\DisputeOptimization\IssuerToDisputeClassMap.csv">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </Content>
      <None Remove="Resources\Lists\DisputeOptimization\IssuerPartToIssuerMap.csv"/>
      <Content Include="Resources\Lists\DisputeOptimization\IssuerPartToIssuerMap.csv">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </Content>
      <None Remove="Resources\Lists\DisputeOptimization\CardDetailsToDisputeClassMap.csv"/>
      <Content Include="Resources\Lists\DisputeOptimization\CardDetailsToDisputeClassMap.csv">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </Content>
    <None Remove="Resources\Lists\RetryStrategies\ErrorCodeToRecycleStrategyParametersMap.csv"/>
    <Content Include="Resources\Lists\RetryStrategies\ErrorCodeToRecycleStrategyParametersMap.csv">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
      <None Remove="Resources\Lists\DisputeOptimization\IssuerClassToSubclassMap.csv"/>
      <Content Include="Resources\Lists\DisputeOptimization\IssuerClassToSubclassMap.csv">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </Content>
      <None Remove="Resources\Lists\EligibilityStrategies\NormalizedResponseToActionMap.csv"/>
      <Content Include="Resources\Lists\EligibilityStrategies\NormalizedResponseToActionMap.csv">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </Content>
      <Content Update="Resources\Lists\DisputeOptimization\BinToDisputeClassMap.csv">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </Content>
      <None Remove="Resources\Lists\DisputeOptimization\BinToDisputeClassMap.csv"/>
      <Content Include="Resources\Lists\DisputeOptimization\BinToDisputeClassMap.csv">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </Content>
  </ItemGroup>

  <ItemGroup>
      <Folder Include="Cures\Implementations\Auto\Stripe\"/>
    <Folder Include="Migrations"/>
  </ItemGroup>

  <ItemGroup>
    <_ContentIncludedByDefault Remove="Connected Services\Stage3\ConnectedService.json" />
    <_ContentIncludedByDefault Remove="Connected Services\Step2\ConnectedService.json" />
    <_ContentIncludedByDefault Remove="Connected Services\PcsmDemo\ConnectedService.json" />
    <_ContentIncludedByDefault Remove="Connected Services\ServiceReference1\ConnectedService.json" />
    <_ContentIncludedByDefault Remove="Connected Services\ServiceReference2\ConnectedService.json" />
    <_ContentIncludedByDefault Remove="Connected Services\PCSM_1ststage\ConnectedService.json" />
    <_ContentIncludedByDefault Remove="Connected Services\PCSM_2ndstage\ConnectedService.json" />
    <_ContentIncludedByDefault Remove="Connected Services\PCSM_3rdstage\ConnectedService.json" />
    <_ContentIncludedByDefault Remove="Connected Services\PCSM_4thstage_Repayments\ConnectedService.json" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="EligibilityChecks\Sequence\Checks\**" />
    <Compile Remove="EligibilityChecks\Implementations\RuleEngine\E0012_MapGatewayResponseCode.cs"/>
      <Compile Remove="Migrations\20240511103249_add-BillingInformationOptional-column-to-Merchants-table.cs"/>
      <Compile Remove="Migrations\20240511103249_add-BillingInformationOptional-column-to-Merchants-table.Designer.cs"/>
    <Compile Remove="Migrations\20241006083512_made-Nodes-readonoy-in-Orders-table.cs"/>
    <Compile Remove="Migrations\20241006083512_made-Nodes-readonoy-in-Orders-table.Designer.cs"/>
      <Compile Remove="Migrations\20241029145417_add-Payers-table.cs"/>
      <Compile Remove="Migrations\20241029145417_add-Payers-table.Designer.cs"/>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Remove="EligibilityChecks\Sequence\Checks\**" />
    <None Remove="Properties\launchSettings.json" />
    <EmbeddedResource Include="Properties\launchSettings.json" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="EligibilityChecks\Sequence\Checks\**" />
  </ItemGroup>

    <ItemGroup>
        <None Remove="Resources\Lists\RetryStrategies\ErrorCodeToRetryStrategyParametersMap.csv"/>
  </ItemGroup>  
  
  <Choose>
    <When Condition=" '$(Configuration)'=='Staging' ">
      <ItemGroup>
        <Content Remove="appsettings.Development.json" />

        <!-- Other files you want to update in the scope of Debug -->
        <None Update="other_files">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </None>
      </ItemGroup>
    </When>
    <When Condition=" '$(Configuration)'=='Development' ">
      <ItemGroup>
        <Content Remove="appsettings.Staging.json" />

        <!-- Other files you want to update in the scope of Debug -->
        <None Update="other_files">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </None>
      </ItemGroup>
    </When>
  </Choose>
</Project>
