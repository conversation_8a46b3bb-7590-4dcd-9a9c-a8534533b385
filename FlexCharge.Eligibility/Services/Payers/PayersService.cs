using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using EntityFramework.Exceptions.Common;
using FlexCharge.Common.Logging.LogSuppression;
using FlexCharge.Common.Telemetry;
using FlexCharge.Eligibility.Entities;
using Microsoft.EntityFrameworkCore;

namespace FlexCharge.Eligibility.Services.Payers;

public class PayersService : IPayersService
{
    private readonly PostgreSQLDbContext _dbContext;

    public PayersService(PostgreSQLDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<Guid> GetOrCreatePayerAsync(string externalPayerId, Merchant merchant)
    {
        using var workspan = Workspan.Start<PayersService>()
            .Tag("ExternalPayerId", externalPayerId);

        Payer payer;
        try
        {
            payer = new()
            {
                Mid = merchant.Mid,
            };

            if (merchant.PayerEnabled)
            {
                payer.ExternalPayerId = externalPayerId;
            }

            try
            {
                // We expect UniqueConstraintException to be thrown here -> turning error into warning in the log
                using var _ = DatabaseLogSuppressor
                    .SuppressUniqueConstraintError<PostgreSQLDbContext>("Payers", "IX_Mid_ExternalPayerId");

                _dbContext.Payers.Add(payer);
                await _dbContext.SaveChangesAsync();
            }
            catch (UniqueConstraintException e)
            {
                _dbContext.Entry(payer).State = EntityState.Detached;
                payer =
                    await _dbContext.Payers.SingleAsync(x =>
                        x.Mid == merchant.Mid && x.ExternalPayerId == externalPayerId);
            }

            return payer.Id;
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);
            throw;
        }
    }

    public async Task<List<Payer>> GetLinkedPayersAsync(Guid payerId, Merchant merchant)
    {
        var linkedPayers = await _dbContext.Payers
            .Where(p => p.Id == payerId)
            .ToListAsync();

        return linkedPayers;
    }
}