using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Common.BackgroundJobs;
using FlexCharge.Common.Emails;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Common.Shared.UrlShortener;
using FlexCharge.Common.Sms;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.Commands;
using FlexCharge.Contracts.Commands.Tracking;
using FlexCharge.Eligibility.Activities;
using FlexCharge.Eligibility.Cures;
using FlexCharge.Eligibility.DTO;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.Enums;
using FlexCharge.Eligibility.Services.ActivityService;
using FlexCharge.Eligibility.Services.EligibilityService;
using FlexCharge.Eligibility.Services.MerchantsService;
using FlexCharge.Eligibility.Services.PartnerService;
using FlexCharge.Eligibility.Services.PCSMServices;
using FlexCharge.Utils;
using MassTransit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Merchant = FlexCharge.Eligibility.Entities.Merchant;

namespace FlexCharge.Eligibility.Services.ConsumerNotificationsService;

class ConsumerNotificationsService : IConsumerNotificationsService
{
    private readonly IPublishEndpoint _publisher;
    private readonly PostgreSQLDbContext _dbContext;
    private readonly IActivityService _activityService;
    private readonly IEligibilityService _eligibilityService;
    private readonly IEmailSender _emailSender;
    private readonly ISmsServices _smsServices;
    private readonly IOptions<SendGridOptions> _emailOptions;
    private readonly IBackgroundWorkerCommandQueue _backgroundWorkerCommandQueue;
    private readonly IUrlShortenerService _urlShortenerService;
    private readonly IPartnerService _partnerService;
    private readonly IMerchantsService _merchantsService;

    public ConsumerNotificationsService(
        IPublishEndpoint publisher,
        PostgreSQLDbContext dbContext,
        IActivityService activityService,
        IEligibilityService eligibilityService,
        IEmailSender emailSender,
        ISmsServices smsServices,
        IOptions<SendGridOptions> emailOptions,
        IBackgroundWorkerCommandQueue backgroundWorkerCommandQueue,
        IUrlShortenerService urlShortenerService,
        IPartnerService partnerService,
        IMerchantsService merchantsService)
    {
        _publisher = publisher;
        _dbContext = dbContext;
        _activityService = activityService;
        _eligibilityService = eligibilityService;
        _emailSender = emailSender;
        _smsServices = smsServices;
        _emailOptions = emailOptions;
        _backgroundWorkerCommandQueue = backgroundWorkerCommandQueue;
        _urlShortenerService = urlShortenerService;
        _partnerService = partnerService;
        _merchantsService = merchantsService;
    }

    #region Notification class

    internal class Notification
    {
        public Notification(NotificationChannel channel, string notificationId)
        {
            Channel = channel;
            NotificationId = notificationId;
        }

        public NotificationChannel Channel { get; }
        public string NotificationId { get; }
    }

    #endregion

    public void StartSendingConsumerNotifications()
    {
        using var workspan = Workspan.Start<ConsumerNotificationsService>();

        _backgroundWorkerCommandQueue.Enqueue(new SendConsumerNotificationsCommand());
    }

    public async Task ProcessPendingConsumerNotificationsAsync(CancellationToken cancellationToken)
    {
        using var workspan = Workspan.Start<ConsumerNotificationsService>()
            .LogEnterAndExit();

        var utcNowDate = DateTime.UtcNow.Date;

        try
        {
            var offersWaitingForConsumerInteraction = await _dbContext.Orders.Where(x =>
                        x.IsCIT == false &&
                        x.State == nameof(OrderState.CONDITIONAL_CONSUMER_INTERACTION) &&
                        x.ExpiryDate.Value > utcNowDate &&
                        x.StopRetries !=
                        true && // do not use x.StopRetries == false, because it will not work with null values
                        x.IsGhostMode == false // we don't want to send notifications for ghost mode offers
                )
                .AsNoTracking()
                .Select(x => new {x.Id, x.Mid})
                .ToListAsync();

            workspan.Log.Information("Offers waiting for consumer interaction: {OffersCount}",
                offersWaitingForConsumerInteraction.Count);

            workspan.Log.Information("MIT Consumer notifications are disabled");

            #region Commented

            // foreach (var offerInfo in offersWaitingForConsumerInteraction)
            // {
            //     try
            //     {
            //         try
            //         {
            //             await _publisher.Publish(
            //                 new SendOfferConsumerNotificationsCommand(offerInfo.Mid, offerInfo.Id));
            //         }
            //         catch (Exception e)
            //         {
            //             workspan.RecordException(e);
            //         }
            //     }
            //     catch (Exception e)
            //     {
            //         workspan.RecordException(e);
            //     }
            // }

            #endregion
        }
        catch (TaskCanceledException)
        {
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
        }
    }

    public async Task SendOfferCustomerNotificationAsync(Guid mid, Guid offerId, bool forceSendingDefaultNotifications)
    {
        using var workspan = Workspan.Start<ConsumerNotificationsService>();

        Order offer = null;

        try
        {
            var offerAndMerchant = await _dbContext.Orders
                .Where(x => x.Id == offerId && x.Mid == mid)
                .Join(_dbContext.Merchants, x => x.Mid, x => x.Mid, (o, m) => new {Offer = o, Merchant = m})
                .SingleAsync();

            offer = offerAndMerchant.Offer;

            await ProcessOfferNotificationsAsync(offerAndMerchant.Merchant, offerAndMerchant.Offer,
                forceSendingDefaultNotifications);
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            await _activityService.AddActivityAsync(ConsumerNotificationErrorActivities.ConsumerNotification_Error,
                offer, data: e);
        }
    }

    private async Task ProcessOfferNotificationsAsync(Merchant merchant, Order order,
        bool forceSendingDefaultNotifications)
    {
        using var workspan = Workspan.Start<ConsumerNotificationsService>();

        bool notificationsEnabledForOrder =
            (order.IsCIT && (merchant.CITConsumerNotificationsEnabled || order.IsInTerminalOrKioskMode())) ||
            (order.IsMIT() && merchant.MITConsumerNotificationsEnabled);

        if (notificationsEnabledForOrder)
        {
            var notificationsToSend =
                await GetNotificationsToSendAsync(merchant, order, forceSendingDefaultNotifications);

            if (!notificationsToSend.Any())
                return;

            var merchantInfo = await _eligibilityService.GetMerchantInformationByOrderAsync(order);
            string cureUrl = UserChallengeBase.GenerateSelfHostedCureUrl(order, merchantInfo.SiteId);

            List<ConsumerNotification> notificationsAlreadySent = null;
            if (order.SentNotifications != null)
            {
                notificationsAlreadySent =
                    JsonConvert.DeserializeObject<List<ConsumerNotification>>(order.SentNotifications);
            }

            if (notificationsAlreadySent == null) notificationsAlreadySent = new();

            foreach (var notification in notificationsToSend)
            {
                var notificationId = notification.NotificationId;
                var notificationChannel = notification.Channel.ToString();


                if (notificationsAlreadySent.Any(x =>
                        x.Id == notificationId &&
                        x.Channel == notificationChannel /*&& x.CureId == offer.CurrentCureId*/))
                {
                    #region Skipping duplicate notification

                    workspan.LogEligibility
                        .Information(
                            "Skipping duplicate notification {NotificationId}. Channel: {Channel},  Order: {OrderId}, Current cure: {CurrentCureId}",
                            notificationId, notificationChannel, order.Id, order.CurrentCureId);

                    await _activityService.AddActivityAsync(
                        ConsumerNotificationActivities.ConsumerNotification_DuplicateNotificationSkipped,
                        order,
                        meta: meta => meta
                            .SetValue("NotificationId", notificationId)
                            .SetValue("CureId", order.CurrentCureId)
                            .SetValue("Channel", notificationChannel));

                    #endregion
                }
                else
                {
                    notificationsAlreadySent.Add(new ConsumerNotification
                        {Id = notificationId, CureId = order.CurrentCureId, Channel = notificationChannel});

                    try
                    {
                        await SendNotificationAsync(merchant, order, notification, cureUrl,
                            merchantInfo.MerchantPublicName, merchantInfo.SiteId);
                    }
                    catch (Exception e)
                    {
                        workspan.RecordException(e);

                        await _activityService.AddActivityAsync(
                            ConsumerNotificationErrorActivities.ConsumerNotification_Error, order,
                            data: e,
                            meta: meta => meta
                                .SetValue("NotificationId", notificationId)
                                .SetValue("CureId", order.CurrentCureId)
                                .SetValue("Channel", notificationChannel)
                                .SetValue("CureUrl", cureUrl));
                    }
                }
            }

            order.SentNotifications = JsonConvert.SerializeObject(notificationsAlreadySent);

            await _dbContext.SaveChangesAsync();
        }
        else
        {
            Workspan.Current?.Log.Information(
                "Notifications are disabled for order {OrderId}. Skipping notifications sending.", order.Id);

            await _activityService.AddActivityAsync(
                ConsumerNotificationActivities.ConsumerNotification_NotificationsDisabled,
                order);
        }
    }

    private async Task SendNotificationAsync(Merchant merchant, Order order, Notification notification,
        string cureUrl, string merchantPublicName, Guid merchantSiteId)
    {
        Workspan.Current?.Log.Information(
            "Sending notification {NotificationId}. Channel: {Channel},  Order: {OrderId}, Current cure: {CurrentCureId}",
            notification.NotificationId, notification.Channel, order.Id, order.CurrentCureId);

        var evaluateRequest = JsonConvert.DeserializeObject<EvaluateRequest>(order.Payload);
        bool notificationSent;
        switch (notification.Channel)
        {
            case NotificationChannel.Email:
                notificationSent = await SendEmailAsync(order, merchant, notification, evaluateRequest, cureUrl,
                    merchantPublicName, merchantSiteId);
                break;
            case NotificationChannel.Sms:
                notificationSent = await SendSmsAsync(order, merchant, notification, evaluateRequest, cureUrl,
                    merchantPublicName, merchantSiteId);
                break;
            default:
                throw new ArgumentOutOfRangeException("Unknown notification channel: " + notification.Channel);
        }

        if (notificationSent)
        {
            await _activityService.AddActivityAsync(
                ConsumerNotificationActivities.ConsumerNotification_NotificationSent,
                order,
                meta: meta => meta
                    .SetValue("NotificationId", notification.NotificationId)
                    .SetValue("CureId", order.CurrentCureId)
                    .SetValue("Channel", notification.Channel.ToString())
                    .SetValue("CureUrl", cureUrl));
        }
        else
        {
            await _activityService.AddActivityAsync(
                ConsumerNotificationErrorActivities.ConsumerNotification_Error, order,
                meta: meta => meta
                    .SetValue("NotificationId", notification.NotificationId)
                    .SetValue("CureId", order.CurrentCureId)
                    .SetValue("Channel", notification.Channel.ToString())
                    .SetValue("CureUrl", cureUrl));
        }
    }

    private async Task<bool> SendSmsAsync(Order order, Merchant merchant, Notification notification,
        EvaluateRequest evaluateRequest, string cureUrl, string merchantPublicName, Guid siteId)
    {
        using var workspan = Workspan.Start<ConsumerNotificationsService>();


        #region Getting Partner sending settings

        // Validating only partner settings required to send Email on behalf of a partner
        HashSet<string> requiredFields = new()
        {
            nameof(PartnerSettings.NotificationSenderPhone),
        };

        var partnerSettings = await _partnerService.GetPartnerSettingsAsync(merchant, order, requiredFields);
        if (partnerSettings == null) return false;

        #endregion

        var phone = await GetConsumerPhoneAsync(evaluateRequest, order);

        if (string.IsNullOrWhiteSpace(phone))
        {
            await _activityService.AddActivityAsync(
                ConsumerNotificationActivities.ConsumerNotification_PhoneUnknown,
                order);
        }
        else
        {
            //TODO: USE MERCHANT ORDER EXPIRY SETTINGS AS TTL
            var shortUrl = await _urlShortenerService.ShortenUrlAsync(cureUrl, DateTime.UtcNow.AddHours(24));
            var shortenedCureUrl = _urlShortenerService.GetShortUrlLink(shortUrl);
            // var shortenedCureUrl = cureUrl;

            var response = await _smsServices.SendSmsAsync(new SmsRequest
                {
                    PhoneNumber = phone,
                    Message = await GetSmsTextAsync(notification.NotificationId,
                        order, merchant, shortenedCureUrl, merchantPublicName,
                        evaluateRequest.BillingInformation?.FirstName?.FirstCharToUpperOthersToLower(),
                        evaluateRequest.BillingInformation?.LastName?.FirstCharToUpperOthersToLower(),
                        evaluateRequest.PaymentMethod?.CardLast4Digits,
                        false)
                },
                senderPhoneOverride: partnerSettings.NotificationSenderPhone);

            return response.ErrorMessage == null;
        }

        return false;
    }

    private async Task<bool> SendEmailAsync(Order order, Merchant merchant, Notification notification,
        EvaluateRequest evaluateRequest, string cureUrl, string merchantPublicName, Guid merchantSiteId)
    {
        using var workspan = Workspan.Start<ConsumerNotificationsService>();

        bool notificationSent = false;

        string email = GetConsumerEmail(evaluateRequest);
        if (string.IsNullOrWhiteSpace(email))
        {
            await _activityService.AddActivityAsync(
                ConsumerNotificationActivities.ConsumerNotification_EmailUnknown,
                order);
        }
        else
        {
            var merchantSite = await _dbContext.Sites.SingleAsync(x => x.Id == merchantSiteId);

            #region Getting Partner sending settings

            // Validating only partner settings required to send Email on behalf of a partner
            HashSet<string> requiredFields = new()
            {
                nameof(PartnerSettings.NotificationSenderEmail),
                nameof(PartnerSettings.BccEmail),
                nameof(PartnerSettings.LogoUrl),
                nameof(PartnerSettings.TermsAndConditionsUrl),
                nameof(PartnerSettings.PrivacyPolicyUrl),
                nameof(PartnerSettings.SiteUrl),
            };

            var partnerSettings = await _partnerService.GetPartnerSettingsAsync(merchant, order, requiredFields);
            if (partnerSettings == null) return false;

            #endregion

            string emailSubject = "";
            string emailTemplateId = GetEmailTemplateId(notification.NotificationId);

            string senderEmail = partnerSettings.NotificationSenderEmail; //GetNoReplySenderEmail(merchantPublicName);
            string senderName = merchantPublicName;

            var merchantSiteUrl = !string.IsNullOrWhiteSpace(merchantSite?.CustomerSupportLink)
                ? merchantSite?.CustomerSupportLink
                : merchant.CustomerSupportLink;

            var merchantSupportEmail = !string.IsNullOrWhiteSpace(merchantSite?.CustomerSupportEmail)
                ? merchantSite?.CustomerSupportEmail
                : merchant.CustomerSupportEmail;

            var merchantSupportPhone = !string.IsNullOrWhiteSpace(merchantSite?.CustomerSupportPhone)
                ? merchantSite?.CustomerSupportPhone
                : merchant.CustomerSupportPhone;

            var merchantConfiguration = merchantSite != null
                ? await _merchantsService.GetMerchantSiteConfigurationAsync(merchant.Mid, merchantSite.Id)
                : null;


            l18n.CurrencyTools.TryGetCurrencySymbol(evaluateRequest.Transaction.Currency,
                out var currencySymbol);

            bool sentSuccessfully = await _emailSender.SendEmailAsync(
                email,
                emailSubject, "Content", new
                {
                    payerFirstName = evaluateRequest.BillingInformation.FirstName?.FirstCharToUpper(),
                    payerLastName = evaluateRequest.BillingInformation.LastName,

                    cardBrand = evaluateRequest.PaymentMethod?.CardBrand,
                    card4Digits = evaluateRequest.PaymentMethod?.CardLast4Digits,

                    orderId = order.Id,
                    externalOrderId = order.ExternalOrderReference,
                    orderAmount = Formatters.LongToDecimal(evaluateRequest.Transaction.Amount).ToString(),
                    orderCurrency = currencySymbol,

                    merchantName = merchantPublicName,
                    merchantSupportPhone = merchantSupportPhone,
                    merchantSupportEmail = merchantSupportEmail,
                    merchantSiteUrl = merchantSiteUrl,
                    merchantLogoUrl = merchantConfiguration?.Logo,

                    paymentDescriptor = order.MerchantDescriptor,

                    mainActionUrl = cureUrl,

                    subject = emailSubject,

                    termsAndConditionsUrl = partnerSettings.TermsAndConditionsUrl,
                    privacyPolicyUrl = partnerSettings.PrivacyPolicyUrl,
                    partnerSupportUrl = partnerSettings.SupportLink,
                    partnerName = partnerSettings.Name,
                    logoUrl = partnerSettings.LogoUrl,
                    poweredByUrl = partnerSettings.SiteUrl
                },
                emailTemplateId,
                senderEmailOverride: senderEmail,
                senderNameOverride: senderName,
                replyTo: !string.IsNullOrWhiteSpace(merchantSupportEmail)
                    ? merchantSupportEmail
                    : partnerSettings.ReplyToEmail,
                bcc: partnerSettings.BccEmail);


            notificationSent = sentSuccessfully;
        }

        return notificationSent;
    }

    private string GetNoReplySenderEmail(string merchantPublicName)
    {
        // Email should be: noreply+<merchant_public_name>@<domain>.com
        string domain = _emailOptions.Value.SenderEmail.Split('@')[1];

        string emailAddressSafeMerchantName = GetEmailAddressSafeText(merchantPublicName);

        return $"noreply+{emailAddressSafeMerchantName.ToLowerInvariant()}@{domain}";
    }

    private string GetEmailAddressSafeText(string merchantPublicName)
    {
        StringBuilder text = new StringBuilder();
        foreach (var ch in merchantPublicName)
        {
            if (char.IsLetterOrDigit(ch))
            {
                text.Append(ch);
            }
            else if (ch == ' ')
            {
                text.Append('+');
            }
        }

        return text.ToString();
    }

    private static string GetEmailTemplateId(string notificationNotificationId)
    {
        switch (notificationNotificationId)
        {
            case NotificationCodes.NCE0001: return "d-0bac542325d64a399550b853e64c34a6";
            case NotificationCodes.NCE0002: return "d-e78279a453dc44cd896437025b65f6cc";
            case NotificationCodes.NCE0003: return "d-bd22c3c6f7bb48109ca3a0f1ed75ff09";
            default:
                throw new Exception("Unknown notification id: " + notificationNotificationId);
        }
    }

    //consts for messages
    private static class NotificationCodes
    {
        public const string NCE0001 = "NCE0001";
        public const string NCE0002 = "NCE0002";
        public const string NCE0003 = "NCE0003";

        public const string NCS0001 = "NCS0001";
        public const string NCS0002 = "NCS0002";
        public const string NCS0003 = "NCS0003";
        public const string NCS0004 = "NCS0004";
    }

    private async Task<string> GetSmsTextAsync(string notificationNotificationId, Order offer, Merchant merchant,
        string cureUrl,
        string merchantPublicName,
        string? payerFirstName,
        string? payerLastName,
        string last4,
        bool putMerchantNameOnFirstLine = true)
    {
        var evaluateRequest = JsonConvert.DeserializeObject<EvaluateRequest>(offer.Payload);

        StringBuilder text = new();

        if (putMerchantNameOnFirstLine)
            text.AppendLine(merchantPublicName);

        string amount = Formatters.LongToDecimal(offer.Amount).ToString();
        switch (notificationNotificationId)
        {
            case NotificationCodes.NCS0001:
            {
                text.Append(
                    $"Oh no, your ${amount} payment for your {merchantPublicName} subscription failed. Please update your payment details here: {cureUrl}");
                break;
            }

            case NotificationCodes.NCS0002:
                text.Append(
                    $"We are still unable to process your ${amount} payment from your {evaluateRequest.PaymentMethod.CardBrand} ending in {evaluateRequest.PaymentMethod.CardLast4Digits}. Please update your payment details here: {cureUrl}");
                break;

            case NotificationCodes.NCS0003:
                text.Append(
                    $"Last reminder. In order not to lose your {merchantPublicName} subscription, you need to update your payment details now: {cureUrl}");
                break;

            case NotificationCodes.NCS0004:
                text.Append(
                    $"Hey {payerFirstName}! Your payment transaction with {merchantPublicName} with your card *{last4} failed. Please click on the link below to try to reprocess the payment: {cureUrl}");
                break;

            default:
                throw new Exception("Unknown notification id: " + notificationNotificationId);
        }

        return await Task.FromResult(text.ToString());
    }

    private static string GetConsumerEmail(EvaluateRequest evaluateRequest)
    {
        if (!string.IsNullOrWhiteSpace(evaluateRequest.Payer.Email)) return evaluateRequest.Payer.Email;
        else return null;
    }

    private async Task<string> GetConsumerPhoneAsync(EvaluateRequest evaluateRequest, Order order)
    {
        using var workspan = Workspan.Start<ConsumerNotificationsService>();

        string phone;
        string normalizedPhone = null;


        phone = evaluateRequest.Payer.Phone;
        if (!PhoneNumberNormalizer.TryNormalizePhoneNumber(phone, out normalizedPhone))
        {
            if (!string.IsNullOrWhiteSpace(evaluateRequest.BillingInformation?.Phone))
            {
                phone = evaluateRequest.BillingInformation?.Phone;
                PhoneNumberNormalizer.TryNormalizePhoneNumber(phone, out normalizedPhone);
            }
        }

        if (string.IsNullOrWhiteSpace(normalizedPhone))
        {
            await _activityService.AddActivityAsync(
                ConsumerNotificationActivities.ConsumerNotification_PhoneIsInvalid,
                order);

            workspan.Log.Warning("Phone number is invalid: {PhoneNumber}", phone);

            return null;
        }
        else
        {
            return normalizedPhone;
        }
    }

    private async Task<List<Notification>> GetNotificationsToSendAsync(Merchant merchant, Order order,
        bool forceSendingDefaultNotifications)
    {
        List<Notification> notificationsToSend = new();
        var mitNotificationsResponse = new MitNotificationsResponse();

        if (order.IsInTerminalOrKioskMode())
        {
            // Always send only NCS0004 for Terminal CIT orders!!!
            if (order.IsCIT)
            {
                notificationsToSend.Add(new Notification(NotificationChannel.Sms, NotificationCodes.NCS0004));
            }

            return notificationsToSend;
        }

        var orderCreatedDaysAgo = (int) Math.Floor((DateTime.UtcNow.Date - order.CreatedOn.Date).TotalDays);
        if (orderCreatedDaysAgo < 0)
        {
            await _activityService.AddActivityAsync(
                ConsumerNotificationErrorActivities.ConsumerNotification_Error,
                order, data: "Order created in the future");

            Workspan.Current?.Log.Fatal("Order created in the future cannot send consumer notifications");
            return notificationsToSend;
        }

        var lastPaymentOperationResponse = order.GetLastTransactionResultOrDefault( /*false*/);
        if (lastPaymentOperationResponse == null)
        {
            await _activityService.AddActivityAsync(
                ConsumerNotificationErrorActivities.ConsumerNotification_Error,
                order, data: "No payment operation response");

            Workspan.Current?.Log.Error("No payment operation response cannot send consumer notifications");

            return notificationsToSend;
        }

        // var mitNotificationsResponse = await _pcsmService.InvokeMitNotificationsAsync(new MitNotificationsRequest
        // {
        //     ResponseCodeSource = lastPaymentOperationResponse?.ResponseCodeGateway ?? "",
        //     ResponseCode = lastPaymentOperationResponse?.ResponseCode ?? "",
        //
        //     OfferCreatedDaysAgo = (int) Math.Floor((DateTime.UtcNow.Date - offer.CreatedOn.Date).TotalDays),
        // });

        var slowStrategyCodes = new[] {"47", "51", "61", "44", "21", "45", "25"};
        var fastStrategyCodes = new[] {"51pp", "41", "43", "54", "78", "14", "6", "52", "53", "39", "48", ""};
        var mediumStrategyCodes = new[] {"5", "4", "62", "59", "57", "12", "58", "64", "93", "N7", "82"};

        if (slowStrategyCodes.Contains(lastPaymentOperationResponse.NormalizedResponseCode))
        {
            switch (orderCreatedDaysAgo)
            {
                case 7:
                    mitNotificationsResponse.SendEmail = true;
                    mitNotificationsResponse.EmailNotificationId = NotificationCodes.NCE0001;
                    break;
                case 10:
                    mitNotificationsResponse.SendEmail = true;
                    mitNotificationsResponse.EmailNotificationId = NotificationCodes.NCE0002;
                    break;
                case 13:
                    mitNotificationsResponse.SendEmail = true;
                    mitNotificationsResponse.EmailNotificationId = NotificationCodes.NCE0002;
                    break;
                default:
                    mitNotificationsResponse.SendEmail = false;
                    break;
            }
        }
        else if (mediumStrategyCodes.Contains(lastPaymentOperationResponse.NormalizedResponseCode))
        {
            switch (orderCreatedDaysAgo)
            {
                case 5:
                    mitNotificationsResponse.SendEmail = true;
                    mitNotificationsResponse.EmailNotificationId = NotificationCodes.NCE0001;
                    break;
                case 8:
                    mitNotificationsResponse.SendEmail = true;
                    mitNotificationsResponse.EmailNotificationId = NotificationCodes.NCE0002;
                    break;
                case 13:
                    mitNotificationsResponse.SendEmail = true;
                    mitNotificationsResponse.EmailNotificationId = NotificationCodes.NCE0002;
                    break;
                default:
                    mitNotificationsResponse.SendEmail = false;
                    break;
            }
        }
        else if (fastStrategyCodes.Contains(lastPaymentOperationResponse.NormalizedResponseCode))
        {
            switch (orderCreatedDaysAgo)
            {
                case 1:
                    mitNotificationsResponse.SendEmail = true;
                    mitNotificationsResponse.EmailNotificationId = NotificationCodes.NCE0001;
                    break;
                case 5:
                    mitNotificationsResponse.SendEmail = true;
                    mitNotificationsResponse.EmailNotificationId = NotificationCodes.NCE0002;
                    break;
                case 13:
                    mitNotificationsResponse.SendEmail = true;
                    mitNotificationsResponse.EmailNotificationId = NotificationCodes.NCE0002;
                    break;
                default:
                    mitNotificationsResponse.SendEmail = false;
                    break;
            }
        }
        else
        {
            mitNotificationsResponse.AddError("Unknown contact strategy", "contactStrategy", "UNKNOWN");
        }

        if (mitNotificationsResponse.Success)
        {
            bool sendEmailNotificationsTest = false;

            if (EnvironmentHelper.IsInSandboxOrStagingOrDevelopment)
            {
                var orderPayload = JsonConvert.DeserializeObject<EvaluateRequest>(order.Payload);
                sendEmailNotificationsTest = TEMPORARY_TEST_HELPER.IsMITNotificationsTestOrder(orderPayload);

                if (sendEmailNotificationsTest)
                {
                    if (string.IsNullOrWhiteSpace(mitNotificationsResponse.EmailNotificationId))
                    {
                        mitNotificationsResponse.EmailNotificationId = NotificationCodes.NCE0001;
                    }
                }
            }

            if (sendEmailNotificationsTest)
            {
                if (string.IsNullOrWhiteSpace(mitNotificationsResponse.EmailNotificationId))
                {
                    mitNotificationsResponse.EmailNotificationId = NotificationCodes.NCE0001;
                }
            }


            if (mitNotificationsResponse.SendEmail || sendEmailNotificationsTest)
            {
                notificationsToSend.Add(
                    new(NotificationChannel.Email, mitNotificationsResponse.EmailNotificationId));
            }

            if (mitNotificationsResponse.SendSms)
            {
                notificationsToSend.Add(new(NotificationChannel.Sms, mitNotificationsResponse.SmsNotificationId));
            }
        }
        else
        {
            await _activityService.AddActivityAsync(
                ConsumerNotificationErrorActivities.ConsumerNotification_Error,
                order, data: mitNotificationsResponse.Errors);
        }

        if (forceSendingDefaultNotifications || EnvironmentHelper.IsInStagingOrDevelopment)
        {
            #region Forcing to send notifications to enable testing

            if (mitNotificationsResponse.SendEmail == false)
            {
                notificationsToSend.Add(new(NotificationChannel.Email, NotificationCodes.NCE0001));
            }

            if (mitNotificationsResponse.SendSms == false)
            {
                notificationsToSend.Add(new(NotificationChannel.Sms, NotificationCodes.NCS0001));
            }

            await _activityService.AddActivityAsync(
                ConsumerNotificationActivities.Testing_SendingTestComsumerNotifications,
                order, data: mitNotificationsResponse);

            #endregion
        }

        return notificationsToSend;
    }
}