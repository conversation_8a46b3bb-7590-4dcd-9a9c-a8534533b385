using System;
using System.Collections.Generic;

namespace FlexCharge.Eligibility.Services.CreditBureauServices;

public class CreditReportRequest
{
    // public enum PhoneType
    // {
    //     Mobile,
    //     Landline
    // }

    public class Phone
    {
        //public PhoneType Type { get; set; }
        public string Number { get; set; }
    }

    public class Address
    {
        public string ZipCode { get; set; }
        public string Line1 { get; set; }

        public string? Line2 { get; set; }

        public string? State { get; set; }
        public string? City { get; set; }
    }


    public string FirstName { get; set; }
    public string LastName { get; set; }
    public string? MiddleName { get; set; }

    public Address CurrentAddress { get; set; }
    public List<Address>? PreviousAddresses { get; set; }
    
    public DateTime? DateOfBirth { get; set; }
    public int? YearOfBirth { get; set; }
    public List<Phone>? Phones { get; set; }
    public string? Ssn  { get; set; }

}