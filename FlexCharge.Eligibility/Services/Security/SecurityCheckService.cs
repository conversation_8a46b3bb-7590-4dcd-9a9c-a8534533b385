using System;
using System.Collections.Generic;
using System.Linq;
using System.Security;
using System.Threading.Tasks;
using CsvHelper.Configuration.Attributes;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Common.SensitiveData.Guards;
using FlexCharge.Common.Telemetry;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.Exceptions.Eligibility;
using FlexCharge.Utils;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Eligibility.Services;

public class SecurityCheckService : ISecurityCheckService
{
    private readonly IServiceScopeFactory _serviceScopeFactory;
    private static readonly IMemoryCache _MerchantWhitelistedDomainsCache;


    public void EnsureInProductionOrTestCardIsWhitelisted(string cardNumber)
    {
        SensitiveDataGuard.IsTestCreditCardGuard(cardNumber);

        #region Commented

        // if (!EnvironmentHelper.IsInProduction && cardNumber != null)
        // {
        //     if (!IsPaymentInstrumentToken(cardNumber))
        //     {
        //         //Filtering card name to contain only digits
        //         cardNumber = new string(cardNumber.Where(x => char.IsDigit(x)).ToArray());
        //
        //         Common.SensitiveData.Guards.SensitiveDataGuard.IsTestCreditCardGuard(request.PaymentMethod?.CardNumber);
        //         if (!TestCardNumbersWhitelist.Contains(cardNumber))
        //             throw new SecurityException("In test environments only card numbers from whitelist can be used");
        //     }
        // }

        #endregion
    }

    private bool IsPaymentInstrumentToken(string cardNumberOrToken)
    {
        //Card numbers may contain only digits
        return cardNumberOrToken.Any(x => char.IsLetter(x));
    }

    static SecurityCheckService()
    {
        //creating not shared memory cache to control cache parameters
        //see: https://learn.microsoft.com/en-us/aspnet/core/performance/caching/memory?view=aspnetcore-6.0
        _MerchantWhitelistedDomainsCache = new MemoryCache(
            new MemoryCacheOptions
            {
                SizeLimit = 1024,
            });
    }

    public SecurityCheckService(IServiceScopeFactory serviceScopeFactory)
    {
        _serviceScopeFactory = serviceScopeFactory;
    }

    public void EnsureHasAccessToTransmitAndEvaluate(Guid? requestMid, Guid? partnerId)
    {
        //TODO D: Check merchant has access to this transaction and merchant security
    }

    public void EnsureHasAccessToOffersApi(Guid? requestMid)
    {
        //TODO D: Check merchant has access to offers
    }

    public void EnsureHasAccessToChallengeApi(Guid? requestMid)
    {
        //TODO D: Check merchant has access to challenges
    }

    public void EnsureHasAccessToOutcomeApi(Guid? requestMid)
    {
        //TODO D: Check merchant has access to challenges
    }

    #region Commented

    // public async Task UpdateMerchantDomainWhitelist(Guid? mid, IEnumerable<string> whitelistedDomains)
    // {
    //     using (var serviceScope = _serviceScopeFactory.CreateScope())
    //     using (var dbContext = serviceScope.ServiceProvider.GetService<PostgreSQLDbContext>())
    //     {
    //         foreach (var domain in whitelistedDomains)
    //         {
    //             string onlyHostName = new Uri(domain).Host.Trim().ToLowerInvariant();
    //             dbContext.MerchantDomains.Add(new MerchantDomain()
    //             {
    //                 MerchantId = mid.Value,
    //                 Domain = onlyHostName
    //             });
    //         }
    //         
    //         await dbContext.SaveChangesAsync();
    //     }
    //
    //     _MerchantWhitelistedDomainsCache.Remove(mid.Value);
    // }

    #endregion

    public async Task RefreshMerchantDomainWhitelistAsync(Guid mid)
    {
        // We use read-through cache, so it will be populated on next read
        _MerchantWhitelistedDomainsCache.Remove(mid);
    }

    public async Task EnsureCallMadeFromWhitelistedDomainAsync(Guid mid, string origin)
    {
        if (string.IsNullOrWhiteSpace(origin))
            throw new EligibilityCalledFromWrongHostException(mid, origin);

        //Compare called url with whitelist from Merchant configuration

        var uri = UriHelper.CreateHttpsUri(origin);
        string onlyHostName = uri.IsAbsoluteUri ? uri.Host.Trim().ToLowerInvariant() : uri.OriginalString;

        var whitelistedHosts = await GetWhitelistedDomains(mid);

        bool isWhitelistCheckSkipped = whitelistedHosts.Contains("*");

        if (!isWhitelistCheckSkipped)
        {
            if (!whitelistedHosts.Contains(onlyHostName))
            {
                if (onlyHostName != "flex-charge.com" && !onlyHostName.EndsWith(".flex-charge.com") &&
                    onlyHostName != "flexfactor.io" && !onlyHostName.EndsWith(".flexfactor.io"))
                {
                    throw new EligibilityCalledFromWrongHostException(mid, origin);
                }
            }
        }
    }

    #region Whitelisted Domains Loading and Caching

    private async Task<HashSet<string>> GetWhitelistedDomains(Guid mid)
    {
        HashSet<string> whitelistedHosts;
        if (!_MerchantWhitelistedDomainsCache.TryGetValue(mid, out whitelistedHosts))
        {
            whitelistedHosts = await LoadWhitelistedDomains(mid);

            _MerchantWhitelistedDomainsCache.Set(mid, whitelistedHosts, new MemoryCacheEntryOptions()
            {
                Size = 1
            });
        }

        return whitelistedHosts;
    }

    private async Task<HashSet<string>> LoadWhitelistedDomains(Guid mid)
    {
        HashSet<string> whitelistedHosts = new();
        using (var serviceScope = _serviceScopeFactory.CreateScope())
        using (var dbContext = serviceScope.ServiceProvider.GetService<PostgreSQLDbContext>())
        {
            var whitelistedHostsList = await dbContext.MerchantDomains
                .Where(x => x.MerchantId == mid)
                .Select(x => x.Domain).ToListAsync();

            whitelistedHostsList.ForEach(x => whitelistedHosts.Add(x.Trim().ToLowerInvariant()));

#if DEBUG
            //whitelistedHosts.Add("localhost"); //localhost (only for local debugging)
#endif
        }

        return whitelistedHosts;
    }

    #endregion


    #region Test Cards Support

    public bool IsTerminalTestCard_Decline(string cardBin, string cardLast4)
    {
        // For DECLINE on Terminal testing use the cards outlined below
        if (EnvironmentHelper.IsInProduction) return false;

        (string Bin, string Last4)[] declineTestCards = new[]
        {
            ("230377", "0396"), // 2303779951000396
            ("541333", "0060"), // ****************
            ("476173", "0011") // ****************
        };

        string cardBin6 = cardBin.Length >= 6 ? cardBin?.Substring(0, 6) : cardBin;

        return declineTestCards.Any(x => x.Bin == cardBin6 && x.Last4 == cardLast4);
    }

    public bool Is3DSFrictionlessTestCard(string cardBin, string cardLast4)
    {
        if (EnvironmentHelper.IsInProduction) return false;

        return cardBin?.StartsWith("444433") == true && cardLast4 == "1455";
    }

    public bool Is3DSNotAuthenticatedTestCard(string cardBin, string cardLast4)
    {
        if (EnvironmentHelper.IsInProduction) return false;

        return cardBin?.StartsWith("411111") == true && cardLast4 == "1111";
    }

    public bool Is3DSChallengeTestCard(string cardBin, string cardLast4)
    {
        if (EnvironmentHelper.IsInProduction) return false;
        return cardBin?.StartsWith("555555") == true && cardLast4 == "4444";
    }

    public bool CanBeCardNumber(string tokenOrCardNumber)
    {
        //Card numbers may contain only digits and spaces
        if (tokenOrCardNumber.Any(ch => !(char.IsDigit(ch) || char.IsWhiteSpace(ch))))
            return false;

        //Card numbers must be at least 12 digits long
        if (tokenOrCardNumber.Count(char.IsDigit) < 12)
            return false;

        //TODO: Use Luhn algorithm to validate card number

        return true;
    }

    #endregion
}