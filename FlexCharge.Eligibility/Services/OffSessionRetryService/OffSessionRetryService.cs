using System;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Common.DistributedLock;
using FlexCharge.Common.Telemetry;
using FlexCharge.Eligibility.Activities;
using FlexCharge.Eligibility.DistributedLock;
using FlexCharge.Eligibility.DTO;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.Enums;
using FlexCharge.Eligibility.Services.ActivityService;
using FlexCharge.Eligibility.Services.EligibilityService;
using FlexCharge.Eligibility.Services.RecyclingEngineService;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;

namespace FlexCharge.Eligibility.Services.OffSessionRetryService;

partial class OffSessionRetryService : IOffSessionRetryService
{
    private readonly IOffSessionRetrySchedulerService _offSessionRetrySchedulerService;
    private readonly PostgreSQLDbContext _dbContext;
    private readonly IActivityService _activityService;
    private readonly IEligibilityService _eligibilityService;
    private readonly IDistributedLockService _distributedLockService;

    public OffSessionRetryService(
        PostgreSQLDbContext dbContext,
        IActivityService activityService,
        IEligibilityService eligibilityService,
        IDistributedLockService distributedLockService,
        IOffSessionRetrySchedulerService offSessionRetrySchedulerService)
    {
        _offSessionRetrySchedulerService = offSessionRetrySchedulerService;
        _dbContext = dbContext;
        _activityService = activityService;
        _eligibilityService = eligibilityService;
        _distributedLockService = distributedLockService;
    }


    public async Task RetryOffSessionOrderAsync(Guid orderId,
        bool notScheduledRetry,
        CancellationToken cancellationToken)
    {
        using var workspan = Workspan.Start<OffSessionRetrySchedulerService>()
            .Baggage("OrderId", orderId)
            .LogEnterAndExit();

        Order order = null;
        try
        {
            // Preventing possible parallel order retry scheduling and evaluation or more than one parallel retry scheduling
            await using var orderLock = new OrderEvaluationDistributedLock();
            await orderLock.AcquireOrderLock(orderId, null, _distributedLockService, _activityService);

            var orderMerchantSite = await _dbContext.Orders
                .Where(x => x.Id == orderId)
                .Join(_dbContext.Merchants, x => x.Mid, x => x.Mid,
                    (ord, merchant) => new
                    {
                        Order = ord,
                        Merchant = merchant
                    })
                .GroupJoin(_dbContext.Sites, x => x.Order.SiteId, x => x.Id,
                    (orderAndMerchant, site) => new
                    {
                        Order = orderAndMerchant.Order,
                        Merchant = orderAndMerchant.Merchant,
                        Site = site.SingleOrDefault()
                    })
                .SingleAsync();

            order = orderMerchantSite.Order;
            var site = orderMerchantSite.Site;

            workspan
                .Baggage("Mid", order.Mid)
                .Baggage("OrderId", order.Id);

            await AddActivityAsync(OffSessionRetryActivities.OffSessionRetry_StartedProcessingOffer, order);

            #region Validations

            if (!notScheduledRetry)
            {
                if (order.RetryScheduledTime == null)
                {
                    workspan.Log.Fatal("RetryScheduledTime is null");
                    // We don't want to retry offers that are not scheduled for retry or already processed
                    await AddActivityAsync(
                        OffSessionRetryErrorActivities.OffSessionRetry_OrderNotScheduledOrDuplicateRetryRequest, order);
                    return;
                }

                if (order.RetryScheduledTime >
                    DateTime.UtcNow +
                    TimeSpan.FromMinutes(2)) // Scheduler is not very accurate - we want to give it 2 minutes interval
                {
                    workspan.Log.Fatal("RetryScheduledTime is in the future");
                    // We don't want to retry offers that are not scheduled for retry or already processed
                    await AddActivityAsync(OffSessionRetryErrorActivities.OffSessionRetry_RetryScheduledTimeIsInFuture,
                        order);
                    return;
                }
            }

            if (order.IsMIT() == false)
            {
                workspan.Log.Fatal("Skipping retry because order is not MIT");
                await AddActivityAsync(OffSessionRetryErrorActivities.OffSessionRetry_Error,
                    order, data: "Skipping retry because order is not MIT");
                return;
            }

            if (order.StopRetries == true)
            {
                workspan.Log.Information("Skipping retry because StopRetries is true");
                await AddActivityAsync(OffSessionRetryActivities.OffSessionRetry_SkippingStoppedOrder, order,
                    data: "Skipping retry because StopRetries is true");
                return;
            }

            if (order.State == nameof(OrderState.ORDER_APPROVED))
            {
                // We don't want to retry offers that are already approved
                workspan.Log.Warning("Skipping retry because order is already approved");
                await AddActivityAsync(OffSessionRetryErrorActivities.OffSessionRetry_OrderAlreadyApproved, order,
                    data: "Skipping retry because order is already approved");
                return;
            }

            if (order.IsExpiredMIT())
            {
                // We don't want to retry expired offers
                workspan.Log.Warning("Skipping retry because order is expired");
                await AddActivityAsync(OffSessionRetryErrorActivities.OffSessionRetry_OrderExpired, order,
                    data: "Skipping retry because order is expired");
                return;
            }

            #endregion

            // We already checked that it's time to retry when retry was scheduled
            var retryInstructions = await _offSessionRetrySchedulerService.GetRetryExecutionInstructionsAsync(order);

            if (retryInstructions.StopRetries)
            {
                await _offSessionRetrySchedulerService.StopOrderRetriesAsync(order);
            }
            else if (retryInstructions.ExecuteRetry)
            {
                var evaluateRequest = JsonConvert.DeserializeObject<EvaluateRequest>(order.Payload);

                #region Retry Order Evaluation

                order.RetryScheduledTime = null; // to ensure that we don't retry it again
                await _dbContext.SaveChangesAsync();

                Stopwatch processingTimeStopwatch = Stopwatch.StartNew();
                try
                {
                    // Do not honor cancellation token for async retries
                    await _eligibilityService
                        .EvaluateOrderAsync(orderLock, orderMerchantSite.Merchant, order, site, evaluateRequest, true,
                            default);
                }
                catch (Exception e)
                {
                    workspan.RecordFatalException(e);
                    await AddActivityAsync(EligibilityErrorActivities.Eligibility_Error, order, data: e);
                }
                finally
                {
                    processingTimeStopwatch.Stop();
                    await _activityService.CreateActivityAsync(
                        EligibilityActivities.Evaluation_EvaluationRequestEnded,
                        set => set
                            .TenantId(evaluateRequest.Mid)
                            .CorrelationId(orderId)
                            .Meta(meta => meta
                                .ProcessingTime(processingTimeStopwatch.ElapsedMilliseconds)
                                .SetValue("MIT", evaluateRequest.IsMIT == true)
                            ));
                }

                #endregion
            }
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);

            await _activityService.CreateActivityAsync(
                OffSessionRetryErrorActivities.OffSessionRetry_Error,
                data: e,
                set => set
                    .TenantId(order?.Mid)
                    .CorrelationId(orderId));
        }
    }

    async Task AddActivityAsync<TActivityNameEnum>(TActivityNameEnum activityNameEnum,
        Order offer,
        string eventName = null,
        //object meta = null,
        object data = null,
        string subcategory = null,
        Action<IPayloadMetadataSetter> meta = null
    )
        where TActivityNameEnum : Enum
    {
        await _activityService.AddActivityAsync(activityNameEnum, offer, eventName, data, subcategory, meta);
    }
}