using System;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Common.DistributedLock;
using FlexCharge.Common.Telemetry;
using FlexCharge.Eligibility.Activities;
using FlexCharge.Eligibility.DistributedLock;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.Exceptions.Eligibility;

namespace FlexCharge.Eligibility.Services.EligibilityService;

/// <summary>
/// Used to acquire distributed lock for order evaluation and to release it automatically through 'await using' construct.
/// Usage:
/// <code>
/// await using var orderLock = new OrderEvaluationDistributedLock();
/// ...
/// orderLock.AcquireOrderLock(...);
/// </code>
/// </summary>
public class OrderEvaluationDistributedLock : NullableAsyncDisposable<LockAcquisitionHandle>
{
    readonly TimeSpan DEFAULT_LOCK_DURATION = TimeSpan.FromMinutes(5);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="orderId"></param>
    /// <param name="mid"></param>
    /// <param name="distributedLockService"></param>
    /// <param name="activityService"></param>
    /// <exception cref="ConcurrentEvaluationCannotObtainLockException"></exception>
    /// <remarks>Can be called multiple times in downstream methods. Lock will be acquired only once and released only once</remarks>
    public async Task AcquireOrderLock(Guid orderId, Guid? mid,
        IDistributedLockService distributedLockService, IActivityService activityService)
    {
        await AcquireOrderLock(orderId, mid, distributedLockService, activityService,
            DEFAULT_LOCK_DURATION,
            TimeSpan.FromSeconds(3), TimeSpan.FromMilliseconds(300), 10);
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="orderId"></param>
    /// <param name="mid"></param>
    /// <param name="distributedLockService"></param>
    /// <param name="activityService"></param>
    /// <param name="lockDuration"></param>
    /// <param name="maxRetryDuration"></param>
    /// <param name="retryInterval"></param>
    /// <param name="maxRetryCount"></param>
    /// <exception cref="ConcurrentEvaluationCannotObtainLockException"></exception>
    /// <remarks>Can be called multiple times in downstream methods. Lock will be acquired only once and released only once</remarks>
    public async Task AcquireOrderLock(Guid orderId, Guid? mid,
        IDistributedLockService distributedLockService,
        IActivityService activityService,
        TimeSpan lockDuration,
        TimeSpan maxRetryDuration,
        TimeSpan? retryInterval = null, int? maxRetryCount = null)
    {
        var existingLock = Value;
        if (existingLock != null)
        {
            if (existingLock.IsAcquired)
            {
                Workspan.Current?.Log.Information("Order lock is already acquired for evaluation {LockName}",
                    existingLock.LockName);
                return;
            }
            else
            {
                // We shouldn't get here, but if we do, we log the error and throw exception
                Workspan.Current?.Log.Fatal("SHOULD NOT BE HERE: Cannot obtain order lock for evaluation {LockName}",
                    existingLock.LockName);

                await activityService.CreateActivityAsync(EligibilityActivities.Evaluation_OfferIsAlreadyInProcessing,
                    data: "Cannot obtain order lock",
                    set => set
                        .CorrelationId(orderId)
                        .TenantId(mid));

                throw new ConcurrentEvaluationCannotObtainLockException();
            }
        }
        else
        {
            #region Trying to acquire distributed lock for order

            // Order is locked for evaluation for (maximum lock time is CIT order expiration interval)
            // If order is locked by another machine, we throw ConcurrentEvaluateException

            var orderEvaluateLock = await distributedLockService.TryAcquireLockAsync(
                LockKeyFactory.CreateOrderEvaluationLockKey(orderId),
                lockDuration,
                maxRetryDuration, retryInterval, maxRetryCount
            );

            // Ensures that the lock will be released
            SetDisposableTarget(orderEvaluateLock, async (lockHandle) =>
            {
                try
                {
                    bool lockAcquired = lockHandle.IsAcquired;
                    await lockHandle.DisposeAsync();

                    if (lockAcquired)
                    {
                        Workspan.Current?.Log.Information("Order evaluation lock released {LockName}",
                            lockHandle.LockName);
                    }
                }
                catch (Exception e)
                {
                    Workspan.Current?.RecordFatalException(e, "Order evaluation lock release error {LockName}",
                        lockHandle.LockName);
                }
            });

            if (orderEvaluateLock.IsAcquired != true)
            {
                Workspan.Current?.Log.Information("Order lock not acquired for evaluation {LockName}",
                    orderEvaluateLock.LockName);

                await activityService.CreateActivityAsync(EligibilityActivities.Evaluation_OfferIsAlreadyInProcessing,
                    data: "Cannot obtain order lock",
                    set => set
                        .CorrelationId(orderId)
                        .TenantId(mid));

                // Offer is already in processing on another machine
                throw new ConcurrentEvaluationCannotObtainLockException();
            }
            else
            {
                Workspan.Current?.Log.Information("Order lock acquired for evaluation {LockName}",
                    orderEvaluateLock.LockName);
            }

            #endregion
        }
    }
}