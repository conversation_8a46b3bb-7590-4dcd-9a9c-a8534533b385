using System;
using System.Collections;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Common.Telemetry;
using FlexCharge.Common.GeoServices;
using FlexCharge.Eligibility.Services.DataNormalization;
using Kount.Ris;
using Kount.Enums;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace FlexCharge.Eligibility.Services.FraudServices;

public class KountService : IFraudService
{
    private readonly IGeoServices _geoServices;
    private readonly INormalizeDataService _normalizeDataService;
    public ILogger<KountService> _logger { get; set; }

    public KountService(ILogger<KountService> logger, IGeoServices geoServices,
        INormalizeDataService normalizeDataService)
    {
        _geoServices = geoServices;
        _normalizeDataService = normalizeDataService;
        _logger = logger;
    }

    public async Task<FraudResponse> ValidateAsync(FraudRequest fraudRequest)
    {
        using var workspan = Workspan.Start<KountService>();
        workspan.Log.Information($"ENTERED: KountService > Validate > {JsonConvert.SerializeObject(fraudRequest)}");

        var fraudResponse = new FraudResponse();
        try
        {
            #region Creating Fraud Request

            //eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiIxMDA0ODgiLCJhdWQiOiJLb3VudC4xIiwiaWF0IjoxNjQ2MDM2ODMxLCJzY3AiOnsia2EiOm51bGwsImtjIjpudWxsLCJhcGkiOnRydWUsInJpcyI6dHJ1ZSwidGRzIjpudWxsfX0.zjHs4AZjaC_QZ44LxEJ7HIPiPrD0WAdXy9Du1yUH5FY
            Inquiry inq = new Inquiry(false, new Configuration
            {
                MerchantId = Environment.GetEnvironmentVariable("FRAUD_KOUNT_MERCHANT_ID"),
                URL = Environment.GetEnvironmentVariable("FRAUD_KOUNT_BASE_URL"),
                ApiKey = Environment.GetEnvironmentVariable("FRAUD_KOUNT_API_KEY"),
                // CertificateFile = "",
                // PrivateKeyPassword = "",
                // ConfigKey = "",
                // ConnectTimeout = "",
                // Version = "",
            });

            inq.SetVersion("0720"); // To enable Omniscore

            inq.SetUserDefinedField("MERCHANTID",
                fraudRequest.Mid.ToString()); // Merchant id of our merchants (not Kount's merchant Id).

            inq.SetUserDefinedField("EXTERNALORDERID",
                fraudRequest.ExternalOrderReference); // Order id in our merchant's system

            inq.SetMode(InquiryTypes.ModeQ); // Q: RIS call that does not originate from a call center environment.

            inq.SetSessionId(fraudRequest.SessionId); // Unique Session ID. Must be unique over a 30-day span.


            inq.SetWebsite(fraudRequest.Website ?? "DEFAULT"); // Website identifier of where order originated.

            // Dotted Decimal IPv4 address that the merchant sees coming from the customer
            // (the IPAD field should never be an anonymous IP address (i.e. 10.X.X.X or 192.168.X.X).
            inq.SetIpAddress(fraudRequest.Ip ??
                             "********");

            inq.SetEmail(!string.IsNullOrWhiteSpace(fraudRequest.Payer.Email)
                ? fraudRequest.Payer.Email
                : "<EMAIL>");

            inq.SetOrderNumber(fraudRequest.OrderId.ToString()
                .Replace("-",
                    "")); // Merchant’s Order Number (we are the merchant for Kount -> pass our internal order number)
            inq.SetMack('Y');

            //inq.SetPaymentTokenLast4(fraudRequest.Last4Digits);
            if (!string.IsNullOrWhiteSpace(fraudRequest.PaymentMethod.CardBinNumber) &&
                fraudRequest.PaymentMethod.CardBinNumber.Length >= 6 &&
                !string.IsNullOrWhiteSpace(fraudRequest.PaymentMethod.CardLast4Digits) &&
                fraudRequest.PaymentMethod.CardLast4Digits.Length == 4)
            {
                var maskedCard =
                    fraudRequest.PaymentMethod.CardBinNumber
                        .Substring(0, Math.Min(6, fraudRequest.PaymentMethod.CardBinNumber.Length)).PadRight(12, 'X') +
                    fraudRequest.PaymentMethod.CardLast4Digits;
                inq.SetCardPaymentMasked(maskedCard);
            }
            else
            {
                //var paymentToken = fraudRequest.PaymentMethod.CardNumber.Replace("-", "");
                var paymentToken = fraudRequest.PaymentMethod.CardNumber.Replace("-", "");

                if (paymentToken.Length > 32) throw new Exception($"Incorrect payment token: {paymentToken}");

                // Payment token submitted by merchant for order
                // (credit card, payer ID, routing/transit, MICR, and account number).
                // 32 characters alpha-numeric
                inq.SetPayment(PaymentTypes.Token, paymentToken);
            }


            inq.SetTotal(fraudRequest.Transaction.Amount);
            inq.SetCurrency(fraudRequest.Transaction.Currency.ToUpper());

            inq.SetAuth('A');

            if (!string.IsNullOrWhiteSpace(fraudRequest.Payer.Id))
                inq.SetUnique(fraudRequest.Payer.Id.Replace("-", ""));

            inq.SetName(fraudRequest.PaymentMethod.HolderName);

            string billingPhone = CorrectPhoneForKount(fraudRequest.BillingInformation.Phone);
            if (!string.IsNullOrWhiteSpace(billingPhone))
            {
                inq.SetBillingPhoneNumber(billingPhone);
            }

            string shippingPhone = CorrectPhoneForKount(fraudRequest.ShippingInformation?.Phone);
            if (!string.IsNullOrWhiteSpace(shippingPhone))
            {
                inq.SetShippingPhoneNumber(shippingPhone);
            }


            if (fraudRequest.BillingInformation != null)
            {
                inq.SetBillingAddress(fraudRequest.BillingInformation.AddressLine1,
                    fraudRequest.BillingInformation.AddressLine2,
                    fraudRequest.BillingInformation.City, fraudRequest.BillingInformation.State,
                    fraudRequest.BillingInformation.Zipcode,
                    fraudRequest.BillingInformation.CountryCode);
            }

            if (fraudRequest.ShippingInformation != null)
            {
                inq.SetShippingAddress(fraudRequest.ShippingInformation.AddressLine1,
                    fraudRequest.ShippingInformation.AddressLine2, fraudRequest.ShippingInformation.City,
                    fraudRequest.ShippingInformation.State,
                    fraudRequest.ShippingInformation.Zipcode,
                    fraudRequest.ShippingInformation.CountryCode);
            }

            var cartItems = fraudRequest.CartItems;
            if (cartItems == null || !cartItems.Any())
            {
                // Kount said that it's OK to send dummy cart item if there is no data
                cartItems = new List<CartItem>();
                // cartItems.Add(new CartItem("Electronics", "TV", "Big TV", 1, 
                //     fraudRequest.Transaction.Amount));
                cartItems.Add(new CartItem("General", "Unknown", "Non-specified product", 1, 
                    fraudRequest.Transaction.Amount));
            }

            var cart = new ArrayList();
            foreach (var item in cartItems)
            {
                cart.Add(new Kount.Ris.CartItem(
                    item.ProductType, item.ProductItem, 
                    _normalizeDataService.Normalize(item.ProductDescription, false, 250, 
                        checkXmlTextLength: true), // Kount is serializing to XML and it's not happy with long strings 
                    item.ProductQuantity, item.ProductPrice));
            }

            inq.SetCart(cart);

            char? avsr = fraudRequest.AvsCode?.FirstOrDefault();
            char? cvvr = fraudRequest.CvvCode?.FirstOrDefault();


            // Optional:
            //Address Verification System Zip Code verification response returned to merchant from processor. M: Match N: No Match X: Unsupported or Unavailable
            if (avsr.HasValue)
            {
                var kountAVS = GetKountAVS(avsr.Value);
                if (kountAVS.AVST.HasValue) inq.SetAvst(kountAVS.AVST.Value);
                if (kountAVS.AVSZ.HasValue) inq.SetAvsz(kountAVS.AVSZ.Value);
            }

            //Card Verification Value response returned to merchant from processor. Acceptable values are ’M’ for match, ’N’ for no-match, or ’X’ unsupported or unavailable.
            if (cvvr.HasValue)
            {
                var kountCVVR = GetKountCVVR(cvvr.Value);
                if (kountCVVR.HasValue) inq.SetCvvr(kountCVVR.Value);
            }

            #endregion


            Response response = inq.GetResponse();
            fraudResponse.Response = response.ToString();

            if (response.HasErrors())
            {
                var errors = response.GetErrors();
                foreach (var error in errors)
                {
                    _logger.LogError($"{error}");
                    fraudResponse.AddError(error);
                }

                return fraudResponse;
            }

            workspan.Log.Information(
                $"IN > KountService > Validate > RESPONSE: {response.ToString()}");

            switch (response.GetAuto())
            {
                case "A":
                    fraudResponse.Result = FraudResponseState.APPROVE;
                    break;
                case "D":
                    fraudResponse.Result = FraudResponseState.DECLINE;
                    break;
                case "R":
                    fraudResponse.Result = FraudResponseState.REVIEW;
                    break;
                default:
                    fraudResponse.Result = FraudResponseState.DECLINE;
                    break;
            }
            
            var invariantCulture = CultureInfo.InvariantCulture;
            
            fraudResponse.Scores.Add("PersonaScore", double.Parse(response.GetScore(), invariantCulture));
            fraudResponse.Scores.Add("OmniScore", double.Parse(response.getOmniScore(), invariantCulture));

            return fraudResponse;
        }
        catch (Exception e)
        {
            workspan.RecordException(e,
                $"EXCEPTION: KountService > Validate > {JsonConvert.SerializeObject(fraudRequest)}");
            throw;
        }
    }

    private Dictionary<char, (char? AVST, char? AVSZ)> _AvsToKountCodes;

    (char? AVST, char? AVSZ) GetKountAVS(char avs)
    {
        using var workspan = Workspan.Start<KountService>();

        (char? AVST, char? AVSZ) noResult = (null, null);

        // TODO D:??? Consult with Guy:
        var noMatch = ('N', 'N');
        var unavailableOrNotSupported = ('X', 'X');
        var zipMatchAddressDoesNotMatch = ('N', 'M');
        var zipAndCustomerNameMatchAddressDoesNotMatch = ('N', 'M');
        var addressMatchZipDoesNotMatch = ('M', 'N');
        var addressAndCustomerNameMatchZipDoesNotMatch = ('M', 'N');
        //var noAddressOrZipMatchOnly = ('X', 'M');
        var exactAddressAndZipMatch = ('M', 'M');
        var exactAddressAndZipAndCustomerNameMatch = ('M', 'M');
        var addressUnavailable = ('X', 'X');

        if (_AvsToKountCodes == null)
        {
            #region AVS->Kount AVST+AVSZ matching

            _AvsToKountCodes = new Dictionary<char, (char? AVST, char? AVSZ)>();
            // see: https://secure.nmi.com/merchants/resources/integration/integration_portal.php#dp_appendix_1
            // see: https://merchantservices.chase.com/support/faqs/address-verification-service
            // see: https://stratus.jitbit.com/helpdesk/KB/View/11759718-nmi-response-codes-and-code-details
            // see: https://unipaygateway.info/codes/gateways/_23/28#response-code-gateway-28
            // see: https://www.hostedpci.com/payment-api-guide/
            // see: https://www.checkout.com/docs/resources/codes/avs-codes

            _AvsToKountCodes.Add('X', exactAddressAndZipMatch);
            _AvsToKountCodes.Add('Y', exactAddressAndZipMatch);
            _AvsToKountCodes.Add('D', exactAddressAndZipMatch);
            _AvsToKountCodes.Add('M', exactAddressAndZipMatch);
            _AvsToKountCodes.Add('2', exactAddressAndZipAndCustomerNameMatch);
            _AvsToKountCodes.Add('6', exactAddressAndZipAndCustomerNameMatch);

            _AvsToKountCodes.Add('A', addressMatchZipDoesNotMatch);

            _AvsToKountCodes.Add('3', addressAndCustomerNameMatchZipDoesNotMatch);
            _AvsToKountCodes.Add('7', addressAndCustomerNameMatchZipDoesNotMatch);

            _AvsToKountCodes.Add('W', zipMatchAddressDoesNotMatch);
            _AvsToKountCodes.Add('Z', zipMatchAddressDoesNotMatch);
            _AvsToKountCodes.Add('P', zipMatchAddressDoesNotMatch);
            _AvsToKountCodes.Add('L', zipMatchAddressDoesNotMatch);

            _AvsToKountCodes.Add('1', zipAndCustomerNameMatchAddressDoesNotMatch);
            _AvsToKountCodes.Add('5', zipAndCustomerNameMatchAddressDoesNotMatch);

            _AvsToKountCodes.Add('N', noMatch);
            _AvsToKountCodes.Add('C', noMatch);
            _AvsToKountCodes.Add('4', noMatch);
            _AvsToKountCodes.Add('8', noMatch);

            _AvsToKountCodes.Add('U', addressUnavailable);

            _AvsToKountCodes.Add('G', unavailableOrNotSupported);
            _AvsToKountCodes.Add('I', unavailableOrNotSupported);
            _AvsToKountCodes.Add('R', unavailableOrNotSupported);
            _AvsToKountCodes.Add('E', unavailableOrNotSupported);
            _AvsToKountCodes.Add('S', unavailableOrNotSupported);

            _AvsToKountCodes.Add('0', unavailableOrNotSupported);
            _AvsToKountCodes.Add('O', unavailableOrNotSupported);
            _AvsToKountCodes.Add('B', unavailableOrNotSupported);
            
            _AvsToKountCodes.Add('\0', unavailableOrNotSupported);

            #endregion
        }

        if (_AvsToKountCodes.TryGetValue(char.ToUpper(avs), out var kountCodes))
        {
            return kountCodes;
        }
        else
        {
            workspan.Log.Error("Eligibility > KOUNT: unknown AVS code: {avs}", avs);
            return unavailableOrNotSupported;
        }
    }

    private Dictionary<char, char?> _CvvResultToKountCvvrCodes;

    char? GetKountCVVR(char cvvResult)
    {
        using var workspan = Workspan.Start<KountService>();

        (char? AVST, char? AVSZ) noResult = (null, null);
        var noMatch = 'N';
        var unavailableOrNotSupported = 'X';
        var match = 'M';

        if (_CvvResultToKountCvvrCodes == null)
        {
            #region CVV Result->Kount CVVR matching

            _CvvResultToKountCvvrCodes = new Dictionary<char, char?>();
            // see: https://secure.nmi.com/merchants/resources/integration/integration_portal.php#dp_appendix_2

            _CvvResultToKountCvvrCodes.Add('M', match);

            _CvvResultToKountCvvrCodes.Add('N', noMatch);

            _CvvResultToKountCvvrCodes.Add('P', unavailableOrNotSupported);
            _CvvResultToKountCvvrCodes.Add('S', unavailableOrNotSupported);
            _CvvResultToKountCvvrCodes.Add('U', unavailableOrNotSupported);
            
            _CvvResultToKountCvvrCodes.Add('\0', unavailableOrNotSupported);

            #endregion
        }

        if (_CvvResultToKountCvvrCodes.TryGetValue(char.ToUpper(cvvResult), out var kountCode))
        {
            return kountCode;
        }
        else
        {
            workspan.Log.Error("Eligibility > KOUNT: unknown CVV result code: {cvvResult}", cvvResult);
            return unavailableOrNotSupported;
        }
    }

    private string? CorrectPhoneForKount(string phoneNumber)
    {
        //32 characters alpha-numeric

        if (phoneNumber == null) return null;

        var phone = new string(phoneNumber.Where(ch => char.IsDigit(ch)).ToArray());

        if (phone.Length <= 32)
        {
            return phone;
        }

        return null;
    }
}