using System;
using FlexCharge.Eligibility.DTO;
using FlexCharge.Eligibility.Entities;
using Merchant = FlexCharge.Eligibility.Entities.Merchant;

namespace FlexCharge.Eligibility.Services.RequestRateLimiterService.RequestLimiters;

interface IRequestLimiter
{
    bool ShouldStop(DateTime currentTimeUtc, Merchant merchant, Order order,
        MerchantRequestStatistics merchantRequestStatistics, EvaluateRequest evaluateRequest);
}