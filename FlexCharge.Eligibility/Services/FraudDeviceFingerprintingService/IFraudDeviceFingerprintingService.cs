using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FlexCharge.Eligibility.Services.FraudDeviceFingerprintingService;

public interface IFraudDeviceFingerprintingService
{
    Task<(IDictionary<string, (string sessionId, string Session)> Data, string IpAddress)>
        GetTrackingProvidersConfigurationsAsync(Guid mid, string senseKey, string? externalOrderId);

    public Task<(string BrowserInfo, string IpAddress)> GetDeviceFingerprintInfoAsync(Guid mid, string senseKey,
        string? externalOrderId);

    public Task<(string BrowserInfo, string IpAddress)> GetSpreedlyDeviceFingerprintInfoAsync(Guid mid, string senseKey,
        string? externalOrderId);
}