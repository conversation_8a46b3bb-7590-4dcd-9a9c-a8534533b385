using System;
using System.Threading.Tasks;
using FlexCharge.Eligibility.DTO;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Common.Shared.UIBuilder;

namespace FlexCharge.Eligibility.Cures.Implementations;

public class C0004i_AskCardNumberAgain : AskForTokenizedPaymentInstrumentCureBase
{
    // Ask for the credit card number again (the one provided is wrong) - verify check digit on the fly
    // Returns new payment instrument token

    protected override async Task CreateAsync(EvaluateRequest evaluateRequest)
    {
        AddTitle(Order, evaluateRequest, "The card details you have provided seem incorrect.");

        UI.HorizontalSeparator();

        UI.SubTitle()
            .Text("Please <b>confirm your card number, expiry date and CVV:</b>");

        CreateCardTokenizationUI();
    }
}