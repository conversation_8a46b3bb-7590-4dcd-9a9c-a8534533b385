using System;
using System.Threading.Tasks;
using FlexCharge.Eligibility.DTO;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Common.Shared.UIBuilder;

namespace FlexCharge.Eligibility.Cures.Implementations;

public class C9995_TestDecline : C0007_AskForCVV
{
// Ask for the CVV again (the one provided is wrong)
// Returns decline always

    protected override async Task CreateAsync(EvaluateRequest evaluateRequest)
    {
        //base.Create();

        AddTitle(Order, evaluateRequest, "Enter the CVV:");

        UI.HorizontalSeparator();

        UI.SubTitle()
            .Text("Your CVV was incorrect - please, enter again:");


        UI.FullRowInput(InputType.Text, Guid.NewGuid())
            .Text("CVV")
            .Placeholder("CVV")
            .Attribute("maxLength", "4")
            .Validations(v => v
                .Required("CVV is required")
                .Regex("^[0-9]{3,4}$", "CVV is incorrect"));
    }

    protected override async Task<UserChallengeResult> ExecuteCureAsync(Order order,
        EvaluateRequest evaluateRequest, ChallengeReEvaluateRequest challengeReEvaluateRequest)
    {
        AnotherUserChallenge = nameof(P0004_DeclineAfterCuresPopup);
        return await Task.FromResult(UserChallengeResult.SHOW_ANOTHER_POPUP);
    }
}