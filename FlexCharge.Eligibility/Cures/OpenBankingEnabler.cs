using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.Commands;
using FlexCharge.Eligibility.Entities;
using MassTransit;
using Microsoft.Extensions.DependencyInjection;
using Org.BouncyCastle.Utilities.Collections;

namespace FlexCharge.Eligibility.Cures;

class OpenBankingEnabler
{
    public static async Task<bool> IsOpenBankingEnabledForMerchantAsync(Merchant merchant,
        IServiceScopeFactory serviceScopeFactory)
    {
        //TODO PERFORMANCE: Cache or store IsOpenBanking enabled in Merchants table
        
        using var workspan = Workspan.Start<OpenBankingEnabler>()
            .Tag(nameof(merchant), merchant);

        bool openBankingEnabled = false;

        if (merchant != null)
        {
            using var serviceScope = serviceScopeFactory.CreateScope();
            var request = serviceScope.ServiceProvider
                .GetRequiredService<IRequestClient<GetIsOpenBankingEnabledCommand>>();

            var response = await request.GetResponse<GetIsOpenBankingEnabledCommandResponse>(
                new GetIsOpenBankingEnabledCommand
                {
                    Mid = merchant.Mid
                });

            openBankingEnabled = response.Message.Success == true && response.Message.OpenBankingEnabled;
        }

        if (EnvironmentHelper.IsInStagingOrDevelopment)
        {
            openBankingEnabled = true;
        }

        workspan.Log.Information("IsOpenBankingEnabledForMerchant: {OpenBankingEnabled}", openBankingEnabled);

        return openBankingEnabled;
    }
}