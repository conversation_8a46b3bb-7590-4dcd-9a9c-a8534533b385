using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using CsvHelper.Configuration.Attributes;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Common.Telemetry;
using FlexCharge.Eligibility.DTO;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Common.Shared.UIBuilder;
using FlexCharge.Utils;
using Newtonsoft.Json;

namespace FlexCharge.Eligibility.Cures;

public abstract partial class UserChallengeBase : CureBase, IUserChallengeCure
{
    // Use https://docs.microsoft.com/en-us/aspnet/core/fundamentals/localization?view=aspnetcore-6.0 for Globalization/Localization?

    public override bool IsAutoCure => false;
    public virtual bool IsUserCancellable => true;
    public virtual bool RequiresIFrame => false;

    public bool InIFrame { get; set; }

    public int AlreadyExecutedCuresCount { get; set; }
    public bool IsAutoConsentFlowConfiguration { get; set; }

    /// <summary>
    /// No consent popup was shown to user
    /// </summary>
    public bool IsImplicitConsentFlow { get; set; }

    protected bool IsFirstPopupShownToUser =>
        (IsAutoConsentFlowConfiguration || IsImplicitConsentFlow) && AlreadyExecutedCuresCount == 0;

    protected virtual bool SupportsPreExecution => false;


    public string AnotherUserChallenge { get; set; }


    #region Create & Execute Cure

    protected virtual async Task<UserChallengeCurePreExecutionResult> TryPreExecuteCureAsync(Order order,
        EvaluateRequest evaluateRequest) =>
        UserChallengeCurePreExecutionResult.EXECUTE_USER_CHALLENGE;

    protected abstract Task CreateAsync(EvaluateRequest evaluateRequest);

    protected abstract Task<UserChallengeResult> ExecuteCureAsync(Order order,
        EvaluateRequest evaluateRequest, ChallengeReEvaluateRequest challengeReEvaluateRequest);


    public async Task<UserChallengeCurePreExecutionResult> TryPreExecuteAsync(Order order)
    {
        using var workspan = Workspan.Start<UserChallengeCureBase>();

        try
        {
            var evaluateRequest = JsonConvert.DeserializeObject<EvaluateRequest>(order.Payload);
            //GenerateIFrameLinksForMIT(evaluateRequest);

            if (!SupportsPreExecution) return UserChallengeCurePreExecutionResult.EXECUTE_USER_CHALLENGE;


            Log($"CURE: {CureId} > Pre-execution Started"); //> {JsonConvert.SerializeObject(Payload)}");

            var initialOrderSerialized = JsonConvert.SerializeObject(order);

            var cureResult = await TryPreExecuteCureAsync(order, evaluateRequest);

            DetermineChangedData(evaluateRequest, order.Payload);
            DetermineChangedData(order, initialOrderSerialized);

            order.Payload = JsonConvert.SerializeObject(evaluateRequest); // payload can be updated

            Log(
                $"CURE: {CureId} > Pre-execution exited with result: {cureResult}"); //> {JsonConvert.SerializeObject(Payload)}");

            return cureResult;
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            throw;
        }
    }

    protected virtual async Task<UserChallengeResult?> ProcessPressedCommonButtonsAsync(
        ChallengeReEvaluateRequest challengeReEvaluateRequest)
    {
        if (IsButtonPressed(challengeReEvaluateRequest, _closeButtonId))
        {
            return await Task.FromResult(UserChallengeResult.NOT_ELIGIBLE);
        }
        else return null;
    }

    public async Task<UserChallengeResult> ExecuteAsync(
        ChallengeReEvaluateRequest challengeReEvaluateRequest)
    {
        using var workspan = Workspan.Start<UserChallengeCureBase>();

        try
        {
            Log($"CURE: {CureId} > Started"); //> {JsonConvert.SerializeObject(Payload)}");

            var initialOrderSerialized = JsonConvert.SerializeObject(Order);
            var evaluateRequest = JsonConvert.DeserializeObject<EvaluateRequest>(Order.Payload);

            Order.CurrentCureId = null;

            var commonButtonsProcessingResult = await ProcessPressedCommonButtonsAsync(challengeReEvaluateRequest);

            if (commonButtonsProcessingResult.HasValue)
            {
                return commonButtonsProcessingResult.Value;
            }
            else
            {
                var cureResult = await ExecuteCureAsync(Order, evaluateRequest, challengeReEvaluateRequest);

                DetermineChangedData(evaluateRequest, Order.Payload);
                DetermineChangedData(Order, initialOrderSerialized);

                Order.Payload = JsonConvert.SerializeObject(evaluateRequest); // payload can be updated

                Log(
                    $"CURE: {CureId} > Exited with result: {cureResult}"); //> {JsonConvert.SerializeObject(Payload)}");

                return cureResult;
            }
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            throw;
        }
    }


//     private void GenerateIFrameLinksForMIT(EvaluateRequest evaluateRequest)
//     {
//         using var workspan = Workspan.Start<UserChallengeCureBase>();
//
// #if DEBUG
//         if (evaluateRequest.IsMIT == true)
//         {
//             
//             workspan.Log.Information("MIT cure merchant hosted URL: {Url}", GenerateMerchantHostedCureUrl(Order));
//             workspan.Log.Information("MIT cure self hosted URL: {Url}", GenerateSelfHostedCureUrl(Order));
//         }
// #endif
//     }

    public static string GenerateSelfHostedCureUrl(Order order, Guid siteId)
    {
        StringBuilder url = new();
        string baseUrl;

        #region Determine base URL based on environment

        //NOTE: IF YOU CHANGE THIS, MAKE SURE TO UPDATE CODE IN UI WIDGET IN ACCORDANCE!!!
        // OTHERWISE UI WIDGET CANNOT DETERMINE IF THIS CHECKOUT PAGE OR NOT
        if (EnvironmentHelper.IsInDevelopment)
        {
            baseUrl = "http://localhost:3004";
        }
        else if (EnvironmentHelper.IsInStaging)
        {
            baseUrl = "https://checkout-staging.flexfactor.io";
        }
        else if (EnvironmentHelper.IsInSandbox)
        {
            baseUrl = "https://checkout-sandbox.flexfactor.io";
        }
        else
        {
            baseUrl = "https://checkout.flexfactor.io";
        }

        #endregion


        url.Append(baseUrl);
        url.Append('?');

        url.Append("mid=");
        url.Append(order.Mid);

        url.Append("&");
        url.Append("osk=");
        url.Append(order.Id);

        url.Append("&");
        url.Append("sid=");
        url.Append(siteId);

        return url.ToString();
    }

    public static string GenerateMerchantHostedCureUrl(Order order, Guid siteId)
    {
        StringBuilder url = new();

        if (!EnvironmentHelper.IsInStagingOrDevelopment)
        {
            url.Append("http://localhost:3002/mit");
        }

        url.Append('?');
        url.Append("mid=");
        url.Append(order.Mid);

        url.Append('&');
        url.Append("osk=");
        url.Append(order.Id);

        url.Append("&");
        url.Append("sid=");
        url.Append(siteId);

        return url.ToString();
    }

    #endregion

    #region Answers Support

    protected bool TryGetAnswer(ChallengeReEvaluateRequest challengeReEvaluateRequest, Guid questionId,
        out string answer, bool canBeEmpty = false, bool canBeWhitespace = false)
    {
        bool answerIsProvided = false;

        if (challengeReEvaluateRequest.Answers.TryGetValue(questionId, out answer))
        {
            if (canBeEmpty) answerIsProvided = true;
            else if (canBeWhitespace) answerIsProvided = answer != null;
            else answerIsProvided = !string.IsNullOrWhiteSpace(answer);
        }

        if (answerIsProvided)
        {
            Log($"Question: {questionId}. Answer: {answer}.");
        }
        else
        {
            Log($"Question: {questionId} is not answered.");
        }

        return answerIsProvided;
    }

    protected bool TryGetAnswerAsString(ChallengeReEvaluateRequest challengeReEvaluateRequest,
        Guid questionId,
        out string? answer, bool normalizeString = true, bool canBeEmpty = false, bool canBeWhitespace = false)
    {
        answer = null;

        if (TryGetAnswer(challengeReEvaluateRequest, questionId, out var answerString, canBeWhitespace))
        {
            answer = answerString.Trim().ToUpper();
            return true;
        }
        else return false;
    }

    protected bool TryGetAnswerAsGuid(ChallengeReEvaluateRequest challengeReEvaluateRequest, Guid questionId,
        out Guid answer)
    {
        answer = Guid.Empty;

        if (TryGetAnswer(challengeReEvaluateRequest, questionId, out var answerString))
        {
            if (Guid.TryParse(answerString, out answer))
            {
                return true;
            }
            else
            {
                Log($"Question: {questionId}. Answer cannot be converted to Guid.");
                return false;
            }
        }
        else return false;
    }

    protected bool TryGetAnswerAsBoolean(ChallengeReEvaluateRequest challengeReEvaluateRequest, Guid questionId,
        out bool? answer)
    {
        answer = null;

        if (TryGetAnswer(challengeReEvaluateRequest, questionId, out var answerString))
        {
            if (bool.TryParse(answerString, out var answerValue))
            {
                answer = answerValue;
                return true;
            }
            else
            {
                Log($"Question: {questionId}. Answer cannot be converted to Boolean.");
                return false;
            }
        }
        else return false;
    }

    protected bool IsButtonPressed(ChallengeReEvaluateRequest challengeReEvaluateRequest, Guid buttonId)
    {
        if (TryGetAnswerAsBoolean(challengeReEvaluateRequest, buttonId, out var isButtonPressed))
        {
            if (isButtonPressed == true) return true;
        }

        return false;
    }

    public List<AnswerValidationError> AnswerValidationErrors { get; private set; }

    public virtual bool TryValidateUserAnswers(Order order,
        ChallengeReEvaluateRequest challengeReEvaluateRequest, out IEnumerable<AnswerValidationError> validationErrors)
    {
        using var workspan = Workspan.Start<UserChallengeCureBase>();

        validationErrors = null;

        try
        {
            AnswerValidationErrors = new List<AnswerValidationError>();

            ValidateAnswers(order, challengeReEvaluateRequest);

            validationErrors = AnswerValidationErrors;


            return true;
        }
        catch (Exception ex)
        {
            workspan.RecordException(ex);
        }
        finally
        {
            AnswerValidationErrors = null;
        }

        return false; //General validation error
    }

    public virtual void ValidateAnswers(Order order, ChallengeReEvaluateRequest challengeReEvaluateRequest)
    {
    }


    protected void AddAnswerError(Guid dayQuestionId, string incorrectDaySelected)
    {
        AnswerValidationErrors.Add(new AnswerValidationError(dayQuestionId, incorrectDaySelected));
    }

    #endregion

    #region Parameters Support

    public Dictionary<string, string> ChallengeParameters { get; } = new Dictionary<string, string>();

    protected void AddParameter(string parameterName, string parameterValue)
    {
        ChallengeParameters[parameterName] = parameterValue;
    }

    #endregion

    #region UI Builder Customization for User Challenge Cures

    public virtual bool ShowCloseButton => true;
    public virtual bool ShowLogo => true;

    protected void AddTitle(Order order, EvaluateRequest evaluateRequest, string title)
    {
        string titleFirstLine = "";

        if (IsImplicitConsentFlow)
        {
            titleFirstLine = IsFirstPopupShownToUser
                ? $"Your payment of ${OrderAmount(order)} with the {CardIssuerOrDefaultText(evaluateRequest)} card ending in {CardLast4Digits(evaluateRequest)} has failed." //" but no worries, you can complete your purchase anyway!"
                : $"Your payment of ${OrderAmount(order)} has failed again.";

            titleFirstLine += "<br><br>";
            //titleFirstLine += "<br>";
        }

        UI.Title()
            .Text(titleFirstLine + title);
    }

    protected static decimal OrderAmount(Order order)
    {
        return Utils.Formatters.IntToDecimal(order.Amount);
    }

    protected string CardLast4Digits(EvaluateRequest evaluateRequest)
    {
        return evaluateRequest.PaymentMethod.CardLast4Digits;
    }

    protected string CardIssuerOrDefaultText(EvaluateRequest evaluateRequest, string defaultIssuerText = "")
    {
        var issuer = evaluateRequest.PaymentMethod.CardIssuer;

#if DEBUG
        if (string.IsNullOrWhiteSpace(issuer))
        {
            issuer = "Wells Fargo";
        }
#endif

        if (string.IsNullOrWhiteSpace(issuer)) issuer = defaultIssuerText;

        return issuer;
    }

    protected string PartnerName { get; private set; }

    public void SetPartnerName(string name)
    {
        PartnerName = name;
    }

    protected string LogoUrl { get; private set; }

    public void SetLogoUrl(string url)
    {
        LogoUrl = url;
    }

    protected string OuterLogoUrl { get; private set; }

    public void SetOuterLogoUrl(string url)
    {
        OuterLogoUrl = url;
    }


    protected string PartnerSiteUrl { get; private set; }

    public void SetPartnerSiteUrl(string url)
    {
        PartnerSiteUrl = url;
    }

    protected string TermsAndConditionsUrl { get; private set; }

    public void SetTermsAndConditionsUrl(string url)
    {
        TermsAndConditionsUrl = url;
    }

    protected string PrivacyPolicyUrl { get; private set; }

    public void SetPrivacyPolicyUrl(string url)
    {
        PrivacyPolicyUrl = url;
    }

    #endregion

    protected async Task<UserChallengeResult> ShowAnotherPopupAsync(string popupName)
    {
        AnotherUserChallenge = popupName;
        return await Task.FromResult(UserChallengeResult.SHOW_ANOTHER_POPUP);
    }

    protected async Task<UserChallengeResult> CloseThisPopupAsync()
    {
        return await Task.FromResult(UserChallengeResult.CLOSE_THIS_POPUP);
    }

    public virtual async Task InitializeChallengeAsync(EvaluateRequest evaluateRequest)
    {
        await Task.CompletedTask;
    }
}