{"app": {"name": "eligibility", "version": "0.0.1"}, "grpc": {"PaymentsEndpoint": "http://payments.production:5000", "VaultEndpoint": "http://vault.production:5000", "BinCheckerEndpoint": "http://payments.binchecker.production:5000", "OrdersEndpoint": "http://orders.production:5000", "TrackingEndpoint": "http://tracking.production:5000"}, "jwt": {"Provider": "Cognito", "validateLifetime": true, "ValidateAudience": true, "ValidAudience": "3v4ll30aqfb435trbds7447524", "Region": "us-east-1", "UserPoolId": "us-east-1_xHv2n5DhZ", "AppClientId": "3v4ll30aqfb435trbds7447524"}, "basicOauth": {"expiryMinutes": 60, "issuer": "Api-Client-Service", "SecretKey": "TAKEN FROM SECRETS", "Provider": "Api-Client-Service", "ValidateLifetime": true, "ValidateAudience": true, "ValidateIssuer": true, "ValidAudience": "eligibility"}, "cache": {"connectionString": "cache-redis-production.wkhxcg.clustercfg.use1.cache.amazonaws.com:6379", "instance": "", "schemaName": "dbo", "tableName": "<PERSON><PERSON>", "BigPayloadCacheConnectionString": "cache-redis-production.wkhxcg.clustercfg.use1.cache.amazonaws.com:6379", "IdempotencyCacheConnectionString": "idempotency.wkhxcg.clustercfg.use1.cache.amazonaws.com:6379", "ExternalRequestsCacheConnectionString": "tracking.wkhxcg.clustercfg.use1.cache.amazonaws.com:6379"}, "dataStream": {"provider": "kinesis"}, "serilog": {"consoleEnabled": true, "aWSCloudwatchEnabled": false, "level": "Information", "path": "../logs/eligibility-{0}.txt"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "rabbitMq": {"Hostname": "localhost", "QueueName": "eligibility", "UserName": "guest", "Password": "guest"}, "swagger": {"enabled": true, "baseUrl": "https://api-staging.flex-charge.com/v1/eligibility/", "reDocEnabled": false, "name": "v1", "title": "eligibility", "version": "v1", "routePrefix": "", "includeSecurity": true}, "email": {"provider": "sendgrid", "key": "*********************************************************************", "senderEmail": "<EMAIL>", "senderName": "FlexFactor", "supportEmail": "<EMAIL>", "bccEmail": "<EMAIL>"}, "sms": {"SID": "**********************************", "Token": "3e902332976f1378c0edd4a8889230c2", "TwilioPhone": "+16206340920", "WhatsappPhone": "", "TwilioCompanyName": "", "ServiceSid": "MG70eaa8fa28ab1ebfb685253fb4e672e8", "TwilioAuthyAPIKey": "", "TwilioVoiceSmsStartUrl": "https://api.authy.com/protected/json/phones/verification/start", "TwilioVoiceSmsChecktUrl": ""}, "eligibility": {"CITEvaluationTimeout": 20, "maximumCuresToTry": 5, "maximumCITAuthorizationAttempts": 5, "maximumMITAuthorizationAttempts": 3, "orderSessionMatchingMaxRetries": 10, "orderSessionMatchingRetryInterval": 100, "orderSessionMatchingMaxRetries_OptionalSenseJs": 3, "orderSessionMatchingRetryInterval_OptionalSenseJs": 50, "waitForOrderEvaluationMaxRetries": 60, "waitForOrderEvaluationRetryInterval": 300, "getTrackingDataMaxRetries": 10, "getTrackingDataRetryInterval": 50}, "experian": {"baseUrl": "https://us-api.experian.com"}, "jaeger": {"agentHost": "**************", "agentPort": 6831}, "backgroundWorkerService": {"executionInterval": 100}, "dynamoDb": {"Endpoint": "https://dynamodb.us-east-1.amazonaws.com"}, "urlShortener": {"baseUrl": "https://flfc.io"}, "Kestrel": {"Endpoints": {"HTTP": {"Url": "http://*:80", "Protocols": "Http1AndHttp2"}, "gRPC": {"Url": "http://*:5000", "Protocols": "Http2"}}}, "AllowedHosts": "*"}