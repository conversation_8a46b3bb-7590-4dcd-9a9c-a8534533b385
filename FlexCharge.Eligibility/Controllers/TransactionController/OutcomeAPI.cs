using System;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Telemetry;
using FlexCharge.Common.Telemetry.HttpRequests;
using FlexCharge.Eligibility.Activities;
using FlexCharge.Eligibility.DTO;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;

namespace FlexCharge.Eligibility.Controllers;

public partial class TransactionController
{
    //Can be used on merchant's back-end instead of webhook to know if order is completed)

    [HttpPost("outcome")]
    [ProducesResponseType(typeof(OutcomeResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> OutcomePost(OutcomeRequest request, CancellationToken token)
    {
        using var workspan = Workspan.StartEndpoint<TransactionController>(this, request, _globalData);

        var midFromJwt = GetMID(); //Outcome is always requested by merchant => we can get MID from JWT token

        try
        {
            _httpContextAccessor.AddCorrelation(midFromJwt, request?.OrderSessionKey);

            workspan
                .Baggage("Mid", midFromJwt)
                .Baggage("OrderId", request?.OrderSessionKey);

            #region Checking ModelState Errors

            if (!ModelState.IsValid)
            {
                workspan.Log.Information("Invalid model state {ModelState}", ModelState);
                workspan.RecordEndpointBadRequest(ModelState);

                await _activityService.CreateActivityAsync(ApiErrorActivities.ModelStateError, ModelState,
                    set => set.TenantId(midFromJwt).CorrelationId(request?.OrderSessionKey));


                return ValidationProblem();
            }

            #endregion


            _securityCheckService.EnsureHasAccessToOutcomeApi(request?.OrderSessionKey);

            if (!await CheckModelStateAsync(midFromJwt, request?.OrderSessionKey)) return ValidationProblem();


            var response =
                await _eligibilityService.Outcome(request,
                    midFromJwt);

            return SafeReturnResponse(response);
        }
        catch (Exception e)
        {
            workspan.RecordEndpointCriticalApiError(e);
            await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                set => set
                    .TenantId(midFromJwt)
                    .CorrelationId(request?.OrderSessionKey)
                    .Meta(meta => meta
                        .SetValue("Request", JsonConvert.SerializeObject(request)))
            );

            return StatusCode(StatusCodes.Status500InternalServerError, "Outcome call failed");
        }
    }
}