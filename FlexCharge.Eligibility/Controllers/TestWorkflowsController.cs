// using System;
// using Microsoft.AspNetCore.Mvc;
// using Microsoft.Extensions.Logging;
// using System.Collections.Generic;
// using System.Threading.Tasks;
// using Amazon.DynamoDBv2;
// using FlexCharge.Common;
// using FlexCharge.Common.BackgroundJobs;
// using FlexCharge.Common.Cache;
// using FlexCharge.Common.RuntimeEnvironment;
// using FlexCharge.Common.Sms;
// using FlexCharge.Common.Telemetry;
// using FlexCharge.Contracts.Commands;
// using FlexCharge.Eligibility.Cures;
// using FlexCharge.Eligibility.DTO;
// using FlexCharge.Eligibility.EligibilityChecks;
// using FlexCharge.Eligibility.EligibilityChecks.Implementations.WorkflowTesting;
// using FlexCharge.Eligibility.EligibilityChecks.Workflow;
// using FlexCharge.Eligibility.Entities;
// using FlexCharge.Eligibility.Services.EligibilityService;
// using FlexCharge.Eligibility.Services.RiskManagement;
// using FlexCharge.Eligibility.Workflows;
// using FlexCharge.WorkflowEngine;
// using FlexCharge.WorkflowEngine.Workflows;
// using Microsoft.Extensions.Options;
// using MassTransit;
// using Microsoft.Extensions.DependencyInjection;
// using Newtonsoft.Json;
// using Newtonsoft.Json.Converters;
// using Merchant = FlexCharge.Eligibility.Entities.Merchant;
//
// namespace FlexCharge.Eligibility.Controllers
// {
//     [Route("[controller]")]
//     [ApiController]
//     public class TestWorkflowsController : ControllerBase
//     {
//         private readonly WorkflowPostgreSQLDbContext _workflowDbContext;
//         private readonly IServiceScopeFactory _serviceScopeFactory;
//
//
//         private readonly AppOptions _globalData;
//
//         public TestWorkflowsController(
//             IOptions<AppOptions> globalData,
//             WorkflowPostgreSQLDbContext workflowDbContext,
//             IServiceScopeFactory serviceScopeFactory
//         )
//         {
//             _workflowDbContext = workflowDbContext;
//             _serviceScopeFactory = serviceScopeFactory;
//             _globalData = globalData.Value;
//         }
//
//         [HttpPost("create-test-workflow")]
//         [ProducesResponseType(200)]
//         public async Task<ActionResult> CreateTestWorkflow()
//         {
//             var workspan = Workspan.StartEndpoint<TestWorkflowsController>(this, null, _globalData);
//
//             if (!EnvironmentHelper.IsInSandboxOrStagingOrDevelopment)
//                 throw new Exception("This endpoint is only available in sandbox, staging and development environments");
//
//             var workflowContext = new EligibilityCheckContext(
//                 new Merchant(), new Site(), new Order(), new EvaluateRequest(), new DataChangeFlags(), false,
//                 null, UserExperienceFlowType.Frictionless,
//                 new PaymentInstrumentInformation(false, false, false, new AccountUpdaterStatus("", false)));
//
//             var workflowBuilder = await CreateTestWorkflowAsync();
//
//             ValueStorage valueStorage = new();
//
//             var workflow = await workflowBuilder.Build(workflowContext, valueStorage, _serviceScopeFactory);
//
//             // TEST CODE to create and store workflow definition in DB
//             var workflowDefinition = WorkflowDefinition.CreateWorkflowDefinition(workflow);
//             Guid standardWorkflowId = new("3f3dcb71-fc05-4a78-951d-22d7e80f12bc");
//
//             var workflowDefinitionSerializationSettings = new JsonSerializerSettings
//             {
//                 NullValueHandling = NullValueHandling.Ignore,
//                 Converters = new List<JsonConverter>()
//                 {
//                     // Convert enums values to strings
//                     new StringEnumConverter()
//                 }
//             };
//
//             _workflowDbContext.Workflows.Update(new WorkflowEngine.Entities.Workflow()
//             {
//                 WorkflowId = standardWorkflowId,
//                 Definition =
//                     JsonConvert.SerializeObject(workflowDefinition, workflowDefinitionSerializationSettings),
//             });
//
//             await _workflowDbContext.SaveChangesAsync();
//
//             return Ok();
//         }
//
//         private async Task<WorkflowBuilder<EligibilityCheckContext>> CreateTestWorkflowAsync()
//         {
//             var workflow = new WorkflowBuilder<EligibilityCheckContext>();
//
//             CreatePresentationWorkflow(workflow);
//
//             return workflow;
//         }
//
//         private void CreatePresentationWorkflow(WorkflowBuilder<EligibilityCheckContext> workflow)
//         {
//             workflow.Add(nameof(E9801_TestBlockEditor));
//             workflow.Add(nameof(E9802_TestIfBlockEditor));
//
//             //eligibilitySequence.Add(nameof(E5002_UpdateFingerprints));
//         }
//     }
// }

