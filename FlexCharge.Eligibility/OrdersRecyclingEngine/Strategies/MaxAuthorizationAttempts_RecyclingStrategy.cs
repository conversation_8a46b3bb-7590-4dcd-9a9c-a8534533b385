using System;
using FlexCharge.Eligibility.EligibilityChecks.Workflow;

namespace FlexCharge.Eligibility.OrdersRecyclingEngine.Strategies;

public class MaxAuthorizationAttempts_RecyclingStrategy : RecyclingStrategyWorkflowBase
{
    public override int Version => 3;

    public override Guid WorkflowId => new Guid("b283a547-c9e7-48a4-8a65-f01055189de4");

    public override string Name => "Max Authorization Attempts Recycling Strategy";

    public override string Description => "";

    public override string Domain => "";

    public override bool IsReadonly => true;

    #region Strategy-Specific Parameters

    // We want to schedule retry in the range of 9:00 AM - 9:00 PM PST
    // So Retry window is 9:00 AM - 9:00 PM PST
    // in PST: 9:00 AM - 9:00 PM
    // in UTC: 16:00 - 04:00
    private readonly RecycleProcessingWindow _recycleProcessingWindow = new(
        start: new TimeSpan(16, 0, 0),
        end: new TimeSpan(4, 0, 0));

    #endregion

    #region Workflow

    protected override void RecycleRulesSubFlow(WorkflowBuilder<RecyclingWorkflowContext> workflow)
    {
        workflow

            #region Max authorization attempts are not reached? -> Continue

            .IF("Max authorization attempts is not reached?",
                context => context.Order.MaxAuthorizationAttempts == null ||
                           context.Order
                               .EvaluationCount <= // For now MaxAuthorizationAttempts does not include the first attempt. It's better to change this!!!
                           context.Order.MaxAuthorizationAttempts)
            .FALSE(StopRecyclingAndExit("Max authorization attempts reached"))
            .CONTINUE()

            #endregion

            #region No authorization results yet? -> Retry now

            .IF("Have previous authorization results",
                context => context.LastTransactionResponse != null && context.Order.MaxAuthorizationAttempts != null)
            .FALSE(ScheduleRetry_AndExit("Initial attempt", _recycleProcessingWindow))
            .CONTINUE()

            #endregion

            #region First day? -> skip (should be handled by the previous block if no authorization results yet)

            .IF("Not first day",
                context => TotalDaysSinceIngestion(context.Order, context.CurrentTimeUtc) > 0)
            .FALSE(SkipRetry_AndExit("Already attempted on the first day"))
            .CONTINUE()

            #endregion

            #region Order processing window is 24 hours or less? -> Retry now

            .IF("More than 24 hours remaining?",
                context => context.OrderProcessingWindowInHours > 24)
            .FALSE(lessThan24Hours =>
            {
                lessThan24Hours
                    .Transform("Force retry",
                        context => { context.SetResult(RecyclingInstructions.Schedule(_recycleProcessingWindow)); })
                    .END(); //!!!
            })
            .CONTINUE()

            #endregion

            #region Order processing window is 48 hours or less? -> Retry now

            .IF("More than 48 hours remaining?",
                context => context.OrderProcessingWindowInHours > 48)
            .FALSE(lessThan48Hours =>
            {
                lessThan48Hours
                    .Transform("Force retry",
                        context => { context.SetResult(RecyclingInstructions.Schedule(_recycleProcessingWindow)); }
                    )
                    .END(); //!!!
            })
            .CONTINUE()

            #endregion

            #region Order processing window is short? -> Retry every day

            .IF("More than 72 remaining?",
                context => context.OrderProcessingWindowInHours > 72)
            .FALSE(lessThan72Hours =>
            {
                lessThan72Hours
                    .Transform("Force retry",
                        context => { context.SetResult(RecyclingInstructions.Schedule(_recycleProcessingWindow)); })
                    .END(); //!!!
            })
            .CONTINUE()

            #endregion

            #region Default: Calculate Charge Retry Interval based on MaxAuthorizationAttempts and remaining time to order expiry

            .Transform("Set retry interval according to Order's MaxAuthorizationAttempts", context =>
            {
                int maxAuthorizationAttempts =
                    context.Order.MaxAuthorizationAttempts ?? 1; // For old orders we don't have this value.
                // 1 will force to retry every day and order.MaxAuthorizationAttempts
                // will be set on the next evaluation

                int expiryInDays = context.OrderProcessingWindowInHours / 24;
                if (expiryInDays - 1 >= maxAuthorizationAttempts)
                {
                    // Enough time to do MaxAuthorizationAttempts? Retry every MaxRetries days
                    context.SetResult(RecyclingInstructions.Schedule((expiryInDays - 1) / maxAuthorizationAttempts,
                        _recycleProcessingWindow));
                }
                else
                {
                    // Not enough time to do MaxRetries? Retry every second day
                    context.SetResult(RecyclingInstructions.Schedule(2, _recycleProcessingWindow));
                }
            })

            #endregion

            ;
    }

    #endregion
}