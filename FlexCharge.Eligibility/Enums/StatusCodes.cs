namespace FlexCharge.Eligibility.Enums
{
    public enum EligibilityResultCodes
    {
        Success = 1,
        Failed
    }

    public enum EligibilityStatusCodes
    {
        APPROVED = 1,
        DECLINED,

        //PROCESSING,
        CHALLENGE, // for synchronous evaluate requests when user interaction is required

        WAITING_FOR_APPROVAL,
        CAPTUREREQUIRED,

        SUBMITTED,
        CA<PERSON><PERSON>LED,

        //MIT Only
        EXPIRED,
    }

    public enum ConsentStatusCodes
    {
        APPROVED = 1,
        REJECTED
    }

    public enum OutcomeStatusCodes
    {
        PROCESSING = 0,
        APPROVED = 1,
        CAPTUREREQUIRED = 2,
        DECLINED = -1,
        NOTFOUND = -2,
        EXPIRED = -3
    }
}