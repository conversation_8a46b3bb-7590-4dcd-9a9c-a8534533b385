using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.MassTransit;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Eligibility.Services.RiskManagement;
using MassTransit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Eligibility.Consumers;

public class OrderFullyPaidInEventConsumer : IdempotentEventConsumer<OrderFullyPaidInEvent>
{
    private readonly PostgreSQLDbContext _dbContext;
    private readonly IFingerprintService _fingerprintService;


    public OrderFullyPaidInEventConsumer(PostgreSQLDbContext dbContext,
        IFingerprintService fingerprintService,
        IServiceScopeFactory serviceScopeFactory) : base(serviceScopeFactory)
    {
        _dbContext = dbContext;
        _fingerprintService = fingerprintService;
    }

    protected override async Task ConsumeEvent(OrderFullyPaidInEvent message, CancellationToken cancellationToken)
    {
        try
        {
            var order = await _dbContext.Orders.SingleAsync(x => x.Id == message.OrderId && x.Mid == message.Mid);

            if (!order.IsFullyPaidIn) // to avoid duplicate processing
            {
                order.IsFullyPaidIn = true;
                await _dbContext.SaveChangesAsync();

                await _fingerprintService.ProcessFullyPaidInOrderAsync(order);
            }
        }
        catch (Exception e)
        {
            Workspan.RecordException(e,
                "Failed updating fully paid in order {OrderId}. Mid: {Mid}", message.OrderId, message.Mid);
        }
    }
}