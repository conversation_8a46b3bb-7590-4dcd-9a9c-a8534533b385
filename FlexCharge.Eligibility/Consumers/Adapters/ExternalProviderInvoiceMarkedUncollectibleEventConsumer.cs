// using System;
// using System.Threading;
// using System.Threading.Tasks;
// using FlexCharge.Common.MassTransit;
// using FlexCharge.Contracts;
// using FlexCharge.Eligibility.Services.ExternalProviderDunningService;
// using Microsoft.Extensions.DependencyInjection;
//
// namespace FlexCharge.Eligibility.Consumers.Adapters;
//
// public class
//     ExternalProviderInvoiceMarkedUncollectibleEvent : ConsumerBase<
//     Contracts.ExternalProviderInvoiceMarkedUncollectibleEvent>
// {
//     private readonly IExternalProviderDunningService _externalProviderDunningService;
//
//     public ExternalProviderInvoiceMarkedUncollectibleEvent(IServiceScopeFactory serviceScopeFactory,
//         IExternalProviderDunningService externalProviderDunningService) : base(serviceScopeFactory)
//     {
//         _externalProviderDunningService = externalProviderDunningService;
//     }
//
//     protected override async Task ConsumeMessage(Contracts.ExternalProviderInvoiceMarkedUncollectibleEvent message,
//         CancellationToken cancellationToken)
//     {
//         try
//         {
//             await _externalProviderDunningService.ProcessInvoiceMarkedUncollectibleAsync(
//                 message.Mid, message.OrderId,
//                 message.InvoiceId);
//         }
//         catch (Exception e)
//         {
//             Workspan.RecordFatalException(e);
//         }
//     }
// }

