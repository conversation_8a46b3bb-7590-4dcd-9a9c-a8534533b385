using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.MassTransit;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Eligibility.Enums;
using MassTransit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Eligibility.Consumers;

public class MerchantUpdatedEventConsumer : ConsumerBase<MerchantUpdatedEvent>
{
    private readonly PostgreSQLDbContext _dbContext;


    public MerchantUpdatedEventConsumer(PostgreSQLDbContext dbContext,
        IServiceScopeFactory serviceScopeFactory) : base(serviceScopeFactory)
    {
        _dbContext = dbContext;
    }

    protected override async Task ConsumeMessage(MerchantUpdatedEvent message, CancellationToken cancellationToken)
    {
        try
        {
            var merchant = await _dbContext.Merchants.SingleOrDefaultAsync(x => x.Mid == message.MerchantId);

            var validIntegrationType = Utils.EnumHelpers.TryParseEnum(message.IntegrationType,
                out MerchantIntegrationTypes integrationType);

            if (!validIntegrationType)
            {
                Workspan.Log.Error(
                    "Invalid IntegrationType: {IntegrationType} for Merchant: {MerchantId}",
                    message.IntegrationType, message.MerchantId);
            }

            bool createMerchant = false;
            if (merchant is null)
            {
                merchant = new Entities.Merchant();
                merchant.IntegrationPartnerId = message.IntegrationPartnerId;

                createMerchant = true;
            }

            MerchantUpdateHelper.UpdateMerchant(merchant, message);

            if (!createMerchant)
            {
                _dbContext.Merchants.Update(merchant);
            }
            else
            {
                _dbContext.Merchants.Add(merchant);
            }

            await _dbContext.SaveChangesAsync();

            Workspan.Log.Information("Merchant updated: {Mid}", message.MerchantId);
        }
        catch (Exception e)
        {
            Workspan.RecordException(e,
                "Failed updating merchant {Mid}", message.MerchantId);
        }
    }
}