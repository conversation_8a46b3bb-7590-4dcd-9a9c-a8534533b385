using System.Collections.Concurrent;

namespace FlexCharge.Eligibility.EligibilityChecks.Workflow;

public class ValueStorage
{
    private ConcurrentDictionary<string, string> _valuesDictionary = new();

    // public string GetResult(string name)
    // {
    //     if (_valuesDictionary.TryGetValue(name, out var value))
    //     {
    //         return value;
    //     }
    //     else throw new Exception($"Required value {name} not found ");
    // }
    //
    // public T GetResult<T>(string name)
    // {
    //     string parameterSerializedValue = GetResult(name);
    //     return JsonConvert.DeserializeObject<T>(parameterSerializedValue);
    // }
    //
    // public void StoreValue(string name, string result)
    // {
    //     _valuesDictionary.AddOrUpdate(name, result, (s, s1) => result);
    // }
    //
    // public void StoreValue<T>(string name, T value) => StoreValue(name, JsonConvert.SerializeObject(value));
}