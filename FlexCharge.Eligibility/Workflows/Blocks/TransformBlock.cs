using System;
using System.Threading.Tasks;
using FlexCharge.Eligibility.Activities;
using FlexCharge.Eligibility.Workflows;

namespace FlexCharge.Eligibility.EligibilityChecks.Workflow.Steps;

public class TransformBlock<TContext> : ActionBlockBase<TContext>, ITransformBlock<TContext>
    where TContext : IExecutionContext
{
    public override string Name => "Transform";
    public override string FullName => "Transform Block";

    public Action<TContext> Transformation { get; private set; }

    public void SetTransformation(string transformationName, Action<TContext> transformation)
    {
        SetInstanceName(transformationName);
        Transformation = transformation;
    }

    public override async Task Execute()
    {
        await AddActivityAsync(WorkflowActivities.TransformBlock_Executed,
            metaSetter: meta => meta
                .SetValue("Name", InstanceName));

        Transformation(Context);
    }
}