using System.Collections.Generic;
using FlexCharge.Eligibility.EligibilityChecks.Workflow.Steps;
using FlexCharge.Eligibility.Workflows.Exceptions;
using FlexCharge.WorkflowEngine.Workflows;

namespace FlexCharge.Eligibility.Workflows.Blocks;

/// <summary>
/// Base class for terminal blocks. Each workflow path must end with a terminal block.
/// </summary>
/// <typeparam name="TContext"></typeparam>
public abstract class TerminalBlockBase<TContext> : BlockBase<TContext>
    where TContext : IExecutionContext
{
    public override bool IsTerminalBlock => true;

    public override IEnumerable<IBlockLink> GetNextLinks()
    {
        yield break;
    }

    public override void AttachNext(IBlock<TContext> nextBlock, int connectorIndex)
    {
        throw new WorkflowException("Cannot attach next block to terminal block");
    }
}