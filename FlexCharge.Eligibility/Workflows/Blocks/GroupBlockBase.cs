using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using FlexCharge.Eligibility.Workflows;
using FlexCharge.WorkflowEngine.Workflows;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Eligibility.EligibilityChecks.Workflow.Steps;

public abstract class GroupBlockBase<TContext> : ActionBlockBase<TContext>,
    IGroupBlock<TContext>
    where TContext : IExecutionContext
{
    public override BlockType Type => BlockType.Group;

    public IList<IBlock<TContext>> ChildBlocks { get; } = new List<IBlock<TContext>>();
    public IEnumerable<IBlock> GetChildBlocks() => ChildBlocks;


    public void AddChild(IBlock<TContext> block)
    {
        ChildBlocks.Add(block);
    }

    public override string ToString()
    {
        StringBuilder checkNames = new();
        foreach (var check in ChildBlocks)
        {
            if (checkNames.Length > 0) checkNames.Append(";");
            checkNames.Append(check.ToString());
        }

        return checkNames.ToString();
    }

    public override async Task CreateAsync(TContext context,
        IServiceScopeFactory serviceScopeFactory, ValueStorage resultsStorage)
    {
        foreach (var step in ChildBlocks)
        {
            await step.CreateAsync(context, serviceScopeFactory, resultsStorage);
        }

        // Call the base implementation to create next linked blocks
        await base.CreateAsync(context, serviceScopeFactory, resultsStorage);
    }
}