using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Eligibility.Activities;
using FlexCharge.Eligibility.Entities;
using FlexCharge.Eligibility.Enums;
using FlexCharge.Eligibility.RiskManagement.Fingerprinting;
using FlexCharge.WorkflowEngine.Common.Workflows.ExternalControl;
using FlexCharge.WorkflowEngine.Common.Workflows.ExternalControl.Controls;
using Newtonsoft.Json;

namespace FlexCharge.Eligibility.EligibilityChecks.Implementations.RiskManagement;

public class E2008_FingerprintsCheck : FingerprintsCheckBase
{
    public override bool IsProductionBlock => true;

    private readonly OffSwitch _disable_DuplicateChecks_CIT_OffSwitch;
    private readonly OffSwitch _disable_DuplicateChecks_MIT_OffSwitch;
    private readonly OffSwitch _disable_MerchantLevel_FraudDetection_Checks_CIT_OffSwitch;
    private readonly OffSwitch _disable_MerchantLevel_FraudDetection_Checks_MIT_OffSwitch;
    private readonly OffSwitch _disable_NetworkLevel_FraudDetectionChecks_CIT_OffSwitch;
    private readonly OffSwitch _disable_NetworkLevelFraudDetectionChecks_MIT_OffSwitch;
    private readonly OffSwitch _disable_DeclineDuplicateOrApprovedSubscriptions_MIT_OffSwitch;


    public E2008_FingerprintsCheck()
    {
        _disable_DuplicateChecks_CIT_OffSwitch = AddExternalControl(
            new OffSwitch(new Guid("8637fbd4-fea4-4507-ae02-8b0916927076"),
                "Duplicate Checks (CIT)",
                "Duplicate checks for CIT transactions.",
                ExternalControls.PreAuthGroup.Name,
                ExternalControls.PreAuthGroup.VelocityChecks_CIT.Name,
                ExternalControlOwnerType.Block,
                nameof(E2008_FingerprintsCheck)));

        _disable_DuplicateChecks_MIT_OffSwitch = AddExternalControl(
            new OffSwitch(new Guid("9e43af93-a073-498b-8b21-8fbd11dcb35f"),
                "Duplicate Checks (MIT)",
                "Duplicate checks for MIT transactions.",
                ExternalControls.PreAuthGroup.Name,
                ExternalControls.PreAuthGroup.VelocityChecks_MIT.Name,
                ExternalControlOwnerType.Block,
                nameof(E2008_FingerprintsCheck)));

        _disable_MerchantLevel_FraudDetection_Checks_CIT_OffSwitch = AddExternalControl(
            new OffSwitch(new Guid("08c8adf0-c309-48a3-bb0d-************"),
                "Merchant-Level Fraud Detection Checks (CIT)",
                "Duplicate checks for CIT transactions.",
                ExternalControls.PreAuthGroup.Name,
                ExternalControls.PreAuthGroup.VelocityChecks_CIT.Name,
                ExternalControlOwnerType.Block,
                nameof(E2008_FingerprintsCheck)));

        _disable_MerchantLevel_FraudDetection_Checks_MIT_OffSwitch = AddExternalControl(
            new OffSwitch(new Guid("cd712aeb-f2c3-4d0b-be2b-aa056bbf95bf"),
                "Merchant-Level Fraud Detection Checks (MIT)",
                "Duplicate checks for MIT transactions.",
                ExternalControls.PreAuthGroup.Name,
                ExternalControls.PreAuthGroup.VelocityChecks_MIT.Name,
                ExternalControlOwnerType.Block,
                nameof(E2008_FingerprintsCheck)));

        _disable_DeclineDuplicateOrApprovedSubscriptions_MIT_OffSwitch = AddExternalControl(
            new OffSwitch(new Guid("00a1001c-159e-406c-8e89-e8c4cdfbdf84"),
                "Decline Duplicate or Approved Subscription Transactions (MIT)",
                "Duplicate checks for MIT subscription transactions.",
                ExternalControls.PreAuthGroup.Name,
                ExternalControls.PreAuthGroup.VelocityChecks_MIT.Name,
                ExternalControlOwnerType.Block,
                nameof(E2008_FingerprintsCheck)) {Enabled = false});


        _disable_NetworkLevel_FraudDetectionChecks_CIT_OffSwitch = AddExternalControl(
            new OffSwitch(new Guid("17975197-7c13-4ed2-b889-27d003deaeeb"),
                "Network-Level Fraud Detection Checks (CIT)",
                "Network-level duplicate checks for CIT transactions.",
                ExternalControls.PreAuthGroup.Name,
                ExternalControls.PreAuthGroup.VelocityChecks_CIT.Name,
                ExternalControlOwnerType.Block,
                nameof(E2008_FingerprintsCheck)));

        _disable_NetworkLevelFraudDetectionChecks_MIT_OffSwitch = AddExternalControl(
            new OffSwitch(new Guid("56726c0b-d05e-4f6e-bee1-0650a6a9ca22"),
                "Network-Level Fraud Detection Checks (MIT)",
                "Network-level duplicate checks for MIT transactions.",
                ExternalControls.PreAuthGroup.Name,
                ExternalControls.PreAuthGroup.VelocityChecks_MIT.Name,
                ExternalControlOwnerType.Block,
                nameof(E2008_FingerprintsCheck)));
    }

    protected override async Task<bool> CanBeExecutedAsync()
    {
        if (MerchantRiskProfile == MerchantRiskProfiles.Unfiltered)
            return false;

        return await base.CanBeExecutedAsync();
    }

    protected override async Task ExecuteBlockAsync()
    {
        await CheckFingerprintsAsync();
    }

    private async Task CheckFingerprintsAsync()
    {
        var allFingerprintStatistics = Context.FingerprintStatistics;

        await ProcessDuplicateChecksAsync(allFingerprintStatistics);

        #region !!!This checks are super critical!!! (Elio's rules on Merchant Level)

        // CANNOT BE DISABLED
        await DeclineRepeatActivityAsync(
            activityType: FingerprintActivityType.OrderCreated, // Count approved transactions
            description: x =>
                $"FraudMerchant: Existing {x.Count} orders with same Merchant_PaymentInstrument over the last {x.Interval} days",
            checkCode: "FC-O2D7-M2b",
            fingerprintType: FingerprintType.PaymentInstrumentFingerprint,
            mid: Order.Mid,
            timeInterval: 7, timeIntervalType: TimeIntervalType.Days,
            maxCount: 2
        );

        #region Approved Transactions >= 2 in 7 days

        (string CheckCode, FingerprintType FingerprintType)[] merchantLevelApprovedTransactionsBlockRules = new[]
        {
            ("FC-O2D7-M3a", FingerprintType.Payer_Email),
            ("FC-O2D7-M3b", FingerprintType.Payer_Phone),

            ("FC-O2D7-M4a", FingerprintType.BillingInformation_FullName),
            ("FC-O2D7-M4b", FingerprintType.ShippingInformation_FullName),
        };

        foreach (var blockRule in merchantLevelApprovedTransactionsBlockRules)
        {
            await DeclineRepeatActivityAsync(
                activityType: FingerprintActivityType.OrderCreated, // Count ingested transactions
                description: x =>
                    $"FraudMerchant: Existing {x.Count} transactions with same Merchant_{blockRule.FingerprintType} over the last {x.Interval} days",
                checkCode: blockRule.CheckCode,
                fingerprintType: blockRule.FingerprintType,
                mid: Order.Mid,
                timeInterval: 7, timeIntervalType: TimeIntervalType.Days,
                maxCount: 2
            );
        }

        #endregion

        #endregion

        await ProcessMerchantLevelFraudDetectionChecksAsync();

        if (Order.IsMIT())
        {
            await DeclineDuplicateOrApprovedSubscriptionTransactionsAsync(allFingerprintStatistics);
        }


        #region !!!This checks are super critical!!! (Elio's rules on Network level)

        // CANNOT BE DISABLED
        await DeclineRepeatActivityAsync(
            activityType: FingerprintActivityType.OrderCreated, // Count approved transactions
            description: x =>
                $"FraudMerchant: Existing {x.Count} orders with same PaymentInstrument over the last {x.Interval} days",
            checkCode: "FC-O4D7-2b",
            fingerprintType: FingerprintType.PaymentInstrumentFingerprint,
            timeInterval: 7, timeIntervalType: TimeIntervalType.Days,
            maxCount: 4
        );

        #region Approved Transactions >= 4 in 7 days | Stage 1

        (string CheckCode, FingerprintType FingerprintType)[] networkLevelApprovedTransactionsBlockRules = new[]
        {
            ("FC-O4D7-3a", FingerprintType.Payer_Email),
        };

        foreach (var blockRule in networkLevelApprovedTransactionsBlockRules)
        {
            await DeclineRepeatActivityAsync(
                activityType: FingerprintActivityType.OrderCreated, // Count approved transactions
                description: x =>
                    $"FraudMerchant: Existing {x.Count} transactions with same {blockRule.FingerprintType} over the last {x.Interval} days",
                checkCode: blockRule.CheckCode,
                fingerprintType: blockRule.FingerprintType,
                timeInterval: 7, timeIntervalType: TimeIntervalType.Days,
                maxCount: 4
            );
        }

        #endregion

        #region Approved Transactions >= 4 in 7 days | Stage 2

        (string CheckCode, FingerprintType FingerprintType)[] networkLevelApprovedTransactionsBlockRules2 = new[]
        {
            ("FC-O4D7-3b", FingerprintType.Payer_Phone),
        };

        foreach (var blockRule in networkLevelApprovedTransactionsBlockRules2)
        {
            await DeclineRepeatActivityAsync(
                activityType: FingerprintActivityType.OrderCreated, // Count ingested transactions
                description: x =>
                    $"FraudMerchant: Existing {x.Count} transactions with same {blockRule.FingerprintType} over the last {x.Interval} days",
                checkCode: blockRule.CheckCode,
                fingerprintType: blockRule.FingerprintType,
                timeInterval: 7, timeIntervalType: TimeIntervalType.Days,
                maxCount: 4
            );
        }

        #endregion

        #endregion

        await ProcessNetworkLevelFraudDetectionChecksAsync();


        #region [Commented] Temporary solution to start slowly and reduce fraud

#if DISABLE_PROCESSING_SAME_DAY_REPEAT_TRANSACTIONS_IN_PRODUCTION
        // #region Declining Same Day Repeat Transactions
        //
        // FingerprintType[] fingerprintsToDeclineIfRepeatTransaction =
        // {
        //     FingerprintType.Merchant_Amount_PayerEmail_OrderPaymentInstrument_Date,
        //     FingerprintType.Merchant_Amount_PayerPhone_OrderPaymentInstrument_Date
        // };
        //
        // foreach (var fingerprintType in fingerprintsToDeclineIfRepeatTransaction)
        // {
        //     await DeclineIfRepeatTransactionByFingerprint(allFingerprintStatistics,
        //         fingerprintType);
        // }
        //
        // #endregion

#endif

#if DISABLE_PROCESSING_N_HOURS_REPEAT_TRANSACTIONS_IN_PRODUCTION
        // #region Declining Repeat Transactions during consequtive N hours
        //
        // await DeclineNHourRepeatTransaction(
        //     FingerprintType.Merchant_Payer_Email_Amount,
        //     Order.IsMIT()
        //         ? 24 * 20 // 20 days 
        //         : 31, // 31 hour
        //     Order.IsMIT()
        //         ? null
        //         : Order.PaymentInstrumentFingerprint
        // );
        //
        // #endregion
        //
        // #region Declining More Than 2 Attempts in 24 hours | Payer_Email + Same Merchant
        //
        // await DeclineNHourRepeatTransaction(FingerprintType.Payer_Email, 24, mid: Order.Mid,
        //     maxAttempts: 2);
        //
        // #endregion
        //
        // #region Declining More Than 2 Attempts in 24 hours | Payer_Phone + Same Merchant
        //
        // await DeclineNHourRepeatTransaction(FingerprintType.Payer_Email, 24, mid: Order.Mid,
        //     maxAttempts: 2);
        //
        // #endregion

#endif

#if DISABLE_MORE_THAN_2_COMPLETED_ORDERS_IN_7_DAYS_IN_PRODUCTION
        // #region Declining More Than 2 Same Card + Same Merchant Transactions during consequtive 7 days
        //
        // await DeclineOnMaxCreatedOrdersInNDaysLimit(
        //     FingerprintType.Merchant_OrderPaymentInstrument, 7, 2);
        //
        // #endregion
        //
        // #region Declining More Than 2 Same Email + Same Merchant Transactions during consequtive 7 days
        //
        // await DeclineOnMaxCreatedOrdersInNDaysLimit(
        //     FingerprintType.Payer_Email, 7, 2, null, Order.Mid);
        //
        // #endregion
        //
        // #region Declining More Than 2 Same Phone + Same Merchant Transactions during consequtive 7 days
        //
        // await DeclineOnMaxCreatedOrdersInNDaysLimit(
        //     FingerprintType.Payer_Phone, 7, 2, null, Order.Mid);
        //
        // #endregion

#endif

        #endregion
    }

    private async Task ProcessDuplicateChecksAsync(IDictionary<string, FingerprintStatistics> allFingerprintStatistics)
    {
        if (Order.IsCIT)
        {
            if (await IsOffSwitchEngagedAsync(_disable_DuplicateChecks_CIT_OffSwitch))
                return; //!!!

            await ProcessCitDuplicateChecksAsync();
        }
        else // MIT
        {
            if (await IsOffSwitchEngagedAsync(_disable_DuplicateChecks_MIT_OffSwitch))
                return; //!!!

            await ProcessMitDuplicateChecksAsync(allFingerprintStatistics);
        }
    }

    private async Task ProcessNetworkLevelFraudDetectionChecksAsync()
    {
        // Check if the off-switch is engaged to disable this checks group
        if (Order.IsCIT)
        {
            if (await IsOffSwitchEngagedAsync(_disable_NetworkLevel_FraudDetectionChecks_CIT_OffSwitch))
                return; //!!!
        }
        else // MIT
        {
            if (await IsOffSwitchEngagedAsync(_disable_NetworkLevelFraudDetectionChecks_MIT_OffSwitch))
                return; //!!!
        }


        #region Network-Level Fraud Detection Checks

        await DeclineRepeatActivityAsync(
            activityType: FingerprintActivityType.OfferCreated, // Count ingested transactions
            description: x =>
                $"FraudMerchant: Existing {x.Count} transactions with same PaymentInstrument over the last {x.Interval} hours",
            checkCode: "FC-I3H24-2b",
            fingerprintType: FingerprintType.PaymentInstrumentFingerprint,
            timeInterval: 24, timeIntervalType: TimeIntervalType.Hours,
            maxCount: 4
        );

        #region Ingested Transactions >= 4 in 24 hours | Stage 1

        (string CheckCode, FingerprintType FingerprintType)[] networkLevelIngestedTransactionsBlockRules = new[]
        {
            ("FC-I4H24-3a", FingerprintType.Payer_Email),
        };

        foreach (var blockRule in networkLevelIngestedTransactionsBlockRules)
        {
            await DeclineRepeatActivityAsync(
                activityType: FingerprintActivityType.OfferCreated, // Count ingested transactions
                description: x =>
                    $"FraudMerchant: Existing {x.Count} transactions with same {blockRule.FingerprintType} over the last {x.Interval} hours",
                checkCode: blockRule.CheckCode,
                fingerprintType: blockRule.FingerprintType,
                timeInterval: 24, timeIntervalType: TimeIntervalType.Hours,
                maxCount: 4
            );
        }

        #endregion

        #region Ingested Transactions >= 4 in 24 hours | Stage 2

        (string CheckCode, FingerprintType FingerprintType)[] networkLevelIngestedTransactionsBlockRules2 = new[]
        {
            ("FC-I4H24-3b", FingerprintType.Payer_Phone),
        };

        foreach (var blockRule in networkLevelIngestedTransactionsBlockRules2)
        {
            await DeclineRepeatActivityAsync(
                activityType: FingerprintActivityType.OfferCreated, // Count ingested transactions
                description: x =>
                    $"FraudMerchant: Existing {x.Count} transactions with same {blockRule.FingerprintType} over the last {x.Interval} hours",
                checkCode: blockRule.CheckCode,
                fingerprintType: blockRule.FingerprintType,
                timeInterval: 24, timeIntervalType: TimeIntervalType.Hours,
                maxCount: 4
            );
        }

        #endregion

        #endregion
    }

    private async Task ProcessMerchantLevelFraudDetectionChecksAsync()
    {
        // Check if the off-switch is engaged to disable this checks group
        if (Order.IsCIT)
        {
            if (await IsOffSwitchEngagedAsync(_disable_MerchantLevel_FraudDetection_Checks_CIT_OffSwitch))
                return; //!!!
        }
        else // MIT
        {
            if (await IsOffSwitchEngagedAsync(_disable_MerchantLevel_FraudDetection_Checks_MIT_OffSwitch))
                return; //!!!
        }

        #region Merchant-Level Fraud Detection Checks

        if (MerchantRiskProfile >= MerchantRiskProfiles.VeryLow)
        {
            await DeclineRepeatActivityAsync(
                activityType: FingerprintActivityType.OfferCreated, // Count ingested transactions
                description: x =>
                    $"FraudMerchant: Existing {x.Count} transactions with same Merchant_PaymentInstrument over the last {x.Interval} hours",
                checkCode: "FC-I2H24-M2b",
                fingerprintType: FingerprintType.PaymentInstrumentFingerprint,
                mid: Order.Mid,
                timeInterval: 24, timeIntervalType: TimeIntervalType.Hours,
                maxCount: 2
            );

            #region Ingested Transactions >= 2 in 24 hours

            (string CheckCode, FingerprintType FingerprintType)[] merchantLevelIngestedTransactionsBlockRules = new[]
            {
                ("FC-I2H24-M3a", FingerprintType.Payer_Email),
                ("FC-I2H24-M3b", FingerprintType.Payer_Phone),

                ("FC-I2H24-M4a", FingerprintType.BillingInformation_FullName),
                ("FC-I2H24-M4b", FingerprintType.ShippingInformation_FullName),
            };

            foreach (var blockRule in merchantLevelIngestedTransactionsBlockRules)
            {
                await DeclineRepeatActivityAsync(
                    activityType: FingerprintActivityType.OfferCreated, // Count ingested transactions
                    description: x =>
                        $"FraudMerchant: Existing {x.Count} transactions with same Merchant_{blockRule.FingerprintType} over the last {x.Interval} hours",
                    checkCode: blockRule.CheckCode,
                    fingerprintType: blockRule.FingerprintType,
                    mid: Order.Mid,
                    timeInterval: 24, timeIntervalType: TimeIntervalType.Hours,
                    maxCount: 2
                );
            }
        }

        #endregion

        #endregion
    }

    private async Task DeclineDuplicateOrApprovedSubscriptionTransactionsAsync(
        IDictionary<string, FingerprintStatistics> allFingerprintStatistics)
    {
        if (await IsOffSwitchEngagedAsync(_disable_DeclineDuplicateOrApprovedSubscriptions_MIT_OffSwitch))
            return; //!!!


        #region Decline Duplicate or Approved Subscription Transactions

        if (!MerchantSpecificOverrides
                .Disable_DuplicateSubscriptionTransactionValidation(Merchant))
        {
            (string CheckCode, FingerprintType FingerprintType)[]
                merchantLevelIngestedDuplicateSubscriptionsBlockRules = new[]
                {
                    ("FC-I2D31-M1b", FingerprintType.Merchant_SubscriptionId_PaymentNumber),
                };

            foreach (var blockRule in merchantLevelIngestedDuplicateSubscriptionsBlockRules)
            {
                await DeclineRepeatActivityAsync(
                    activityType: FingerprintActivityType.OfferCreated, // Count ingested transactions
                    description: x =>
                        $"FraudMerchant: Existing {x.Count} transactions with same {blockRule.FingerprintType} over the last {x.Interval} days",
                    checkCode: blockRule.CheckCode,
                    fingerprintType: blockRule.FingerprintType,
                    //mid: Order.Mid,
                    timeInterval: 31, timeIntervalType: TimeIntervalType.Days,
                    maxCount: 1
                );
            }


            (string CheckCode, FingerprintType FingerprintType)[]
                merchantLevelApprovedDuplicateSubscriptionsBlockRules = new[]
                {
                    ("FC-O1-M1b", FingerprintType.Merchant_SubscriptionId_PaymentNumber),
                };


            await DeclineByFingerprintStatistics(allFingerprintStatistics,
                merchantLevelApprovedDuplicateSubscriptionsBlockRules.Select(x =>
                    (x.FingerprintType, x.CheckCode,
                        $"FraudMerchant: Existing order with same {x.FingerprintType}")),
                maxOpenOrders: 1,
                maxPaidInOrders: 1,
                maxWrittenOffOrders: 1
            );
        }

        #endregion
    }

    private async Task ProcessMitDuplicateChecksAsync(
        IDictionary<string, FingerprintStatistics> allFingerprintStatistics)
    {
        #region Duplicate Checks

        #region Open MIT Offers >= 1 (transactions in processing)

        (string CheckCode, FingerprintType FingerprintType)[] mitDuplicateCheckBlockRules = new[]
        {
            ("DC-A1-M1a2b3a", FingerprintType.Merchant_Amount_OrderPaymentInstrument_PayerEmail),
            ("DC-A1-M1a2b3b", FingerprintType.Merchant_Amount_OrderPaymentInstrument_PayerPhone),

            ("DC-A1-M1a2b4a", FingerprintType.Merchant_Amount_OrderPaymentInstrument_BillingFullName),
            ("DC-A1-M1a2b5a", FingerprintType.Merchant_Amount_OrderPaymentInstrument_ShippingFullName),

            ("DC-A1-M1a2b", FingerprintType.Merchant_Amount_OrderPaymentInstrument)
        };

        await DeclineByFingerprintStatistics(allFingerprintStatistics,
            mitDuplicateCheckBlockRules.Select(x =>
                (x.FingerprintType, x.CheckCode, "Duplicate: Existing MIT in-processing status")),
            maxOpenOffers: 1
        );

        #endregion

        #endregion

        if (!MerchantSpecificOverrides.Disable_DuplicateSubscriptionTransactionValidation(Merchant))
        {
            #region Duplicate Subscription Check

            (string CheckCode, FingerprintType FingerprintType)[]
                mitDuplicateSubscriptionCheckBlockRules = new[]
                {
                    ("DC-A1-M1b", FingerprintType.Merchant_SubscriptionId_PaymentNumber),
                };

            await DeclineByFingerprintStatistics(allFingerprintStatistics,
                mitDuplicateSubscriptionCheckBlockRules.Select(x =>
                    (x.FingerprintType, x.CheckCode,
                        "Duplicate: Existing MIT subscription in-processing status")),
                maxOpenOffers: 1
            );

            #endregion
        }
        else
        {
            Log("Duplicate subscription transaction validation is disabled for this merchant.");

            await AddActivityAsync(EligibilityActivities.Overrides_MerchantSpecificOverride_Engaged, meta: meta =>
                meta
                    .SetValue("Mid", Merchant.Mid)
                    .SetValue("Override",
                        nameof(MerchantSpecificOverrides.Disable_DuplicateSubscriptionTransactionValidation))
            );
        }
    }

    private async Task ProcessCitDuplicateChecksAsync()
    {
        #region Duplicate Checks

        #region Ingested CIT Transactions >= 1 in 31 hours

        (string CheckCode, FingerprintType FingerprintType)[] citDuplicateCheckBlockRules = new[]
        {
            ("DC-I1H31-M1a2b3a", FingerprintType.Payer_Email),
            ("DC-I1H31-M1a2b3b", FingerprintType.Payer_Phone),

            ("DC-I1H31-M1a2b4a", FingerprintType.BillingInformation_FullName),
            ("DC-I1H31-M1a2b5a", FingerprintType.ShippingInformation_FullName),

            ("DC-I1H31-M1a2b", FingerprintType.PaymentInstrumentFingerprint),
        };

        foreach (var blockRule in citDuplicateCheckBlockRules)
        {
            await DeclineRepeatActivityAsync(
                activityType: FingerprintActivityType.OfferCreated, // Count ingested transactions
                description: x => $"Duplicate: {x.Interval}-hours duplicate CIT",
                checkCode: blockRule.CheckCode,
                fingerprintType: blockRule.FingerprintType,
                mid: Order.Mid,
                paymentInstrumentFingerprint: Order.PaymentInstrumentFingerprint,
                amount: Order.Amount,
                timeInterval: 31, timeIntervalType: TimeIntervalType.Hours,
                maxCount: 1
            );
        }

        #endregion

        #endregion
    }
}