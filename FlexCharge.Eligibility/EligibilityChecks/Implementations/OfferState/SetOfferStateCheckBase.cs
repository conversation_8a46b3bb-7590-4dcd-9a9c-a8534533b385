using System.Threading.Tasks;
using FlexCharge.Eligibility.Services.Orders;
using FlexCharge.Eligibility.Services.Orders.OrderStates;

namespace FlexCharge.Eligibility.EligibilityChecks.Implementations;

public abstract class SetOfferStateCheckBase : EligibilityCheckBase
{
    protected async Task SetOrderStateAsync(StateOfOrder state, object meta = null)
    {
        var orderStateMachine = GetRequiredService<IOrderStateMachine>();
        await orderStateMachine.SetOrderStateAsync(Order, state, saveChangesToDatabase: false, forceUpdateEvent: false);
    }
}