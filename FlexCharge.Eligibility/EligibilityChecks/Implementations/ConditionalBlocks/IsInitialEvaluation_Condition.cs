// using System;
// using FlexCharge.Eligibility.EligibilityChecks.Workflow.Steps;
// using FlexCharge.Eligibility.Entities;
// using FlexCharge.Eligibility.Workflows;
//
// namespace FlexCharge.Eligibility.EligibilityChecks.Implementations;
//
// public class IsInitialEvaluation_Condition : PredefinedConditionIfBlockBase<EligibilityCheckContext>
// {
//     protected override bool PredefinedCondition()
//     {
//         // CIT or first MIT evaluation? => It's an initial evaluation
//         return Context.Order.IsCIT || 
//                (Context.Order.IsMIT() && Context.Order.EvaluationCount <= 1);
//     }
// }

