using System.Threading.Tasks;

namespace FlexCharge.Eligibility.EligibilityChecks.Implementations.Testing;

public class E9004_ReplacePaymentInstrumentTypeWithCREDIT : EligibilityCheckBase
{
    public override bool IsProductionBlock => false;

    protected override async Task ExecuteBlockAsync()
    {
        if (OrderPayload.PaymentMethod.CardType == null)
        {
            OrderPayload.PaymentMethod.CardType = "CREDIT";
        }
    }
}