using System;
using System.Globalization;
using CsvHelper.Configuration;
using CsvHelper.Configuration.Attributes;

namespace FlexCharge.Orders.DTO.Batches;

public class BatchDTOMap : ClassMap<BatchDTO>
{
    public BatchDTOMap()
    {
        Map(m => m.BatchId).Name("BatchId");
        Map(m => m.BatchType).Name("BatchType");
        Map(m => m.PayoutId).Name("PayoutId");
        Map(m => m.PayoutDate).Name("PayoutDate");
        Map(m => m.ScheduledFor).Name("ScheduledFor");
        Map(m => m.CapturePeriodFrom).Name("CapturePeriodFrom");
        Map(m => m.CapturePeriodTo).Name("CapturePeriodTo");
        Map(m => m.Status).Name("Status");
        Map(m => m.TotalValue).Name("TotalValue");
        Map(m => m.TotalCount).Name("TotalCount");
        Map(m => m.FlexChargeFees).Name("ProcessingFees");
        Map(m => m.FlexChargeFeesCount).Name("ProcessingFeesCount");
        Map(m => m.FlexChargeChargebackFeesAmount).Name("ChargebackFeesAmount");
        Map(m => m.Returns).Name("Returns");
        Map(m => m.ReturnsCount).Name("ReturnsCount");
        Map(m => m.Chargebacks).Name("Chargebacks");
        Map(m => m.ChargebacksCount).Name("ChargebacksCount");
        Map(m => m.PayoutAmount).Name("PayoutAmount");
        Map(m => m.Currency).Name("Currency");
        Map(m => m.CurrencyCode).Name("CurrencyCode");
        Map(m => m.CurrencySymbol).Name("CurrencySymbol");
        Map(m => m.ReserveHold).Name("Reserve");
        Map(m => m.ReserveUtilization).Name("ReserveUtilization");
        Map(m => m.ReserveRelease).Name("ReserveRelease");
        Map(m => m.Adjustment).Name("Adjustment");

        // Flatten Sender/Receiver sub-properties
        Map(m => m.Sender.RelatedEntityDba).Name("SenderDba");
        Map(m => m.Sender.RelatedEntityId).Name("SenderId");
        Map(m => m.Receiver.RelatedEntityDba).Name("ReceiverDba");
        Map(m => m.Receiver.RelatedEntityId).Name("ReceiverId");
    }
}

public class BatchDTO
{
    public Guid BatchId { get; set; }
    public string BatchType { get; set; }
    public Guid PayoutId { get; set; }
    public DateTime PayoutDate { get; set; }
    public DateTime ScheduledFor { get; set; }
    public DateTime CapturePeriodFrom { get; set; }
    public DateTime CapturePeriodTo { get; set; }

    public bool IsPosted { get; set; }
    public bool CanUnpost { get; set; }

    public bool IsFinished { get; set; }
    public bool CanPost { get; set; }
    public string Status { get; set; }
    public Guid Mid { get; set; }
    public string MerchantName { get; set; }
    public FinancialAccountDTO Sender { get; set; }
    public FinancialAccountDTO Receiver { get; set; }
    public decimal TotalValue { get; set; }
    public int TotalCount { get; set; }
    public decimal FlexChargeFees { get; set; }
    public decimal FlexChargeChargebackFeesAmount { get; set; }

    public int FlexChargeFeesCount { get; set; }

    // public decimal Tax { get; set; }
    public decimal Returns { get; set; }
    public int ReturnsCount { get; set; }
    public decimal Chargebacks { get; set; }
    public int ChargebacksCount { get; set; }
    public decimal PayoutAmount { get; set; }
    public string Currency { get; set; }
    public int CurrencyCode { get; set; }
    public string CurrencySymbol { get; set; }
    public decimal ReserveHold { get; set; }
    public decimal ReserveRelease { get; set; }
    public decimal ReserveUtilization { get; set; }

    public decimal Adjustment { get; set; }
    public DateTime? IsShelved { get; set; }
    public string Comment { get; set; }
}

public class FinancialAccountDTO
{
    public Guid Id { get; set; }
    public string RelatedEntityType { get; set; }
    public Guid RelatedEntityId { get; set; }
    public string RelatedEntityDba { get; set; }
    public string AccountType { get; set; } //Bank, Card, Wallet etc.
}