using System;
using System.Collections.Generic;

namespace FlexCharge.Orders.DTO;

public class DashboardDataDTO
{
    public DashboardDataDTO()
    {
        DailyKPIs = new();
    }

    public Guid MerchantId { get; set; }
    public string StoreName { get; set; }
    public string? StoreId { get; set; }

    public List<DailyKPIs> DailyKPIs { get; }
}

public class DailyKPIs
{
    public DateTime CreatedDate { get; set; }

    #region Payments (Transmitted)

    public int TotalPaymentsCount { get; set; }
    public long TotalPaymentsAmount { get; set; }
    public int DeclinedPaymentsCount { get; set; }
    public long DeclinedPaymentsAmount { get; set; }

    public int SuccessfulPaymentsCount { get; set; }
    public long SuccessfulPaymentsAmount { get; set; }

    // Refunds / Chargebacks
    public int CreditedPaymentsCount { get; set; }
    public long CreditedPaymentsAmount { get; set; }

    #endregion

    #region Evaluated by Flex Charge

    // Evaluated
    public int EvaluatedOffersCount { get; set; }
    public long EvaluatedOffersAmount { get; set; }

    //Eligible, but not yet approved
    public int EligibleOffersCount { get; set; }
    public long EligibleOffersAmount { get; set; }

    // Approved
    public int ApprovedOrdersCount { get; set; }
    public long ApprovedOrdersAmount { get; set; }
    


    // public int DeclinedOrdersCount => ProcessedOrdersCount - ProcessedSuccessfulOrderCount;
    // public long DeclinedOrderAmount => ProcessedOrdersAmount - ProcessedSuccessfulOrderAmount;

    #endregion
    

    public int LostPaymentsOrdersCount { get; set; }
    public long LostPaymentsAmount { get; set; }
    public long LostPaymentsAverageAmount { get; set; }

    public int LostCustomerPaymentsOrdersCount { get; set; }
    public long LostCustomerPaymentsAmount { get; set; }
    public long LostCustomerPaymentsAverageAmount { get; set; }

    public long RevenueLostTotal { get; set; }
    public decimal RevenueLostTotalPercentage { get; set; }
    

}