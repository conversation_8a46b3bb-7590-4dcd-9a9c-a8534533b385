using System;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Common.BackgroundJobs;
using FlexCharge.Common.Telemetry;
using FlexCharge.Orders.Activities;
using FlexCharge.Orders.Services.PartnersServices.FeeCollectionServices;
using MassTransit;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Orders.Services.BatchServices.Batches;

public class CalculateFIPADBatchesCommand : BackgroundWorkerCommand
{
    protected override async Task ExecuteAsync(IServiceProvider serviceProvider, CancellationToken cancellationToken)
    {
        using var workspan = Workspan.Start<CalculateFIPADBatchesCommand>()
            .LogEnterAndExit();

        var adminFeesCollectionService = serviceProvider.GetRequiredService<IFeesCollectionService>();

        try
        {
            await adminFeesCollectionService.CalculateFeesAsync(cancellationToken);
        }
        catch (Exception e)
        {
            workspan.RecordException(e, "CalculateFIPADBatchesCommand: Can't execute admin fees calculations");

            var activityService = serviceProvider.GetRequiredService<IActivityService>();
            await activityService.CreateActivityAsync(PayoutErrorActivities.ExecutingPayouts_Error);

            var publisher = serviceProvider.GetRequiredService<IPublishEndpoint>();
            await publisher.Publish(new { });

            throw;
        }
    }
}