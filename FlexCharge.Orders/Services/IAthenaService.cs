using System.Threading.Tasks;
using FlexCharge.Orders.DTO;

namespace FlexCharge.Orders.Services;

public interface IAthenaService
{
    public Task<DashboardDataDTO> ExecuteDashboardQueryAsync(
        string query, string athenaDatabaseName,
        string athenaOutputBucket,
        int executionTimeout = 100000,
        int retryTimeInterval = 1000);

    public Task<DashboardAggregatedDataDTO> ExecuteDashboardAggregatedKPIsQueryAsync(
        string query, string athenaDatabaseName,
        string athenaOutputBucket,
        int executionTimeout = 100000,
        int retryTimeInterval = 1000);
}