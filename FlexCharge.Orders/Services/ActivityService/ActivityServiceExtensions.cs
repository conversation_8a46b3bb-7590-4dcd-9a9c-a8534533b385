using System;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Orders.Entities;

namespace FlexCharge.Eligibility.Services.ActivityService;

public static class ActivityServiceExtensions
{
    public static async Task AddActivityAsync<TActivityNameEnum>(this IActivityService activityService,
        TActivityNameEnum activityNameEnum,
        Order? order,
        string eventName = null,
        object data = null,
        string subcategory = null,
        Action<IPayloadMetadataSetter> meta = null
    )
        where TActivityNameEnum : Enum
    {
        await activityService.CreateActivityAsync(activityNameEnum,
            set => set
                .TenantId(order?.MerchantId)
                .CorrelationId(order?.Id)
                .Subcategory(subcategory)
                .EventRaised(eventName)
                .Data(data)
                .Meta(meta));
    }
}