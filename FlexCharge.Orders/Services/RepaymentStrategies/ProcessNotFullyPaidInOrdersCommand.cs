using System;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.BackgroundJobs;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Orders.Services.RepaymentStrategies;

public class ProcessNotFullyPaidInOrdersCommand : BackgroundWorkerCommand
{
    protected override async Task ExecuteAsync(IServiceProvider serviceProvider, CancellationToken cancellationToken)
    {
        var repaymentStrategyManager = serviceProvider.GetRequiredService<IRepaymentStrategyManagerService>();

        await repaymentStrategyManager.ProcessNotFullyPaidInOrdersAsync(cancellationToken);
    }
}