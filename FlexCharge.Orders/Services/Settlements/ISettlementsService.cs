using System;
using System.Threading;
using System.Threading.Tasks;

namespace FlexCharge.Orders.Services.Settlements;

public interface ISettlementsService
{
    Task<string> GenerateAsync(Guid mid, int? pageNumber, int? pageSize, string? batchType, CancellationToken token);
    Task<string> AdminGenerateAsync(Guid? mid, Guid? partnerId, int? pageNumber, int? pageSize, string? batchType, CancellationToken token);
    Task<string> PartnerGenerateAsync(Guid pid, Guid? mid, int? pageNumber, int? pageSize, string? batchType, CancellationToken token);

    Task<string> GenerateDetailsAsync(Guid id, int pageSize, int pageNumber, Guid? mid, Guid? pid,
        CancellationToken token);
}