using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Orders.Migrations
{
    /// <inheritdoc />
    public partial class OrderCountEstimator : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_Orders_CreatedOn",
                table: "Orders");

            // migrationBuilder.DropIndex(
            //     name: "IX_Orders_Email",
            //     table: "Orders");
            //
            // migrationBuilder.DropIndex(
            //     name: "IX_Orders_FirstName",
            //     table: "Orders");
            //
            // migrationBuilder.DropIndex(
            //     name: "IX_Orders_LastName",
            //     table: "Orders");

            // migrationBuilder.DropIndex(
            //     name: "IX_Orders_StatusCategory",
            //     table: "Orders");

            migrationBuilder.CreateIndex(
                name: "IX_Orders_CreatedOn",
                table: "Orders",
                column: "CreatedOn")
                .Annotation("Npgsql:CreatedConcurrently", true)
                .Annotation("Npgsql:IndexMethod", "brin");
            
            migrationBuilder.Sql("CREATE FUNCTION count_estimator(query text) RETURNS bigint AS $$" 
                                 +" DECLARE " +
                                 "rec record; " +
                                 "rows bigint; " +
                                 " BEGIN " +
                                 "FOR rec IN EXECUTE 'EXPLAIN ' || query LOOP " +
                                 "rows := substring(rec.\"QUERY PLAN\" FROM ' rows=([[:digit:]]+)'); " +
                                 "EXIT WHEN rows IS NOT NULL; " +
                                 " END LOOP; " +
                                 " RETURN rows;" +
                                 " END; " +
                                 " $$ LANGUAGE plpgsql VOLATILE STRICT;");
            
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_Orders_CreatedOn",
                table: "Orders");

            migrationBuilder.CreateIndex(
                name: "IX_Orders_CreatedOn",
                table: "Orders",
                column: "CreatedOn");

            // migrationBuilder.CreateIndex(
            //     name: "IX_Orders_Email",
            //     table: "Orders",
            //     column: "Email");
            //
            // migrationBuilder.CreateIndex(
            //     name: "IX_Orders_FirstName",
            //     table: "Orders",
            //     column: "FirstName");
            //
            // migrationBuilder.CreateIndex(
            //     name: "IX_Orders_LastName",
            //     table: "Orders",
            //     column: "LastName");
            //
            // migrationBuilder.CreateIndex(
            //     name: "IX_Orders_StatusCategory",
            //     table: "Orders",
            //     column: "StatusCategory");
        }
    }
}
