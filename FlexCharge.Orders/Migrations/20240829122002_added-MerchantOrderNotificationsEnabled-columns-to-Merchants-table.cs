using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Orders.Migrations
{
    /// <inheritdoc />
    public partial class addedMerchantOrderNotificationsEnabledcolumnstoMerchantstable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "CITMerchantOrderNotificationsEnabled",
                table: "Merchants",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "MITMerchantOrderNotificationsEnabled",
                table: "Merchants",
                type: "boolean",
                nullable: false,
                defaultValue: false);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CITMerchantOrderNotificationsEnabled",
                table: "Merchants");

            migrationBuilder.DropColumn(
                name: "MITMerchantOrderNotificationsEnabled",
                table: "Merchants");
        }
    }
}
