using System;
using FlexCharge.Orders.Entities;
using Newtonsoft.Json;

namespace FlexCharge.Orders;

public class PRODUCTION_TEST_MODE
{
    public static bool IS_PRODUCTION_TEST_MODE(Merchant merchant, Order order)
    {
        Guid partnerIdWithProductionTestMode = Guid.Parse("7187eadc-81d1-47c4-b1f0-8cc61b1e793a");

        if (merchant.Pid == partnerIdWithProductionTestMode &&
            order.IsMIT == false &&
            order.Amount == 100)
        {
            if (order.FirstName == "TREY" && order.LastName == "ZUZEY")
            {
                return true;
            }
        }

        return false;
    }
}