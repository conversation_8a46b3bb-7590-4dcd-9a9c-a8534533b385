using FlexCharge.Common;
using FlexCharge.Common.Authentication;
using FlexCharge.Orders.DTO;
using FlexCharge.Orders.Entities;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Common.Authentication.BasicAuthentication;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.MassTransit;
using FlexCharge.Common.Telemetry;
using FlexCharge.Common.Telemetry.HttpRequests;
using FlexCharge.Contracts.Commands;
using FlexCharge.Orders.Activities;
using FlexCharge.Orders.DTO.Refunds;
using FlexCharge.Orders.Services;
using MassTransit;
using Microsoft.EntityFrameworkCore;

namespace FlexCharge.Orders.Controllers
{
    [Route("public/Orders")]
    [ApiController]
    [Authorize(AuthenticationSchemes = AuthenticationSchemas.BasicAuthentication)]
    public class OrdersPublicAPIController : BaseController
    {
        private readonly AppOptions _globalData;
        private readonly IActivityService _activityService;
        private readonly IOrderService _orderService;
        private ISecurityCheckService _securityCheckService;
        private readonly PostgreSQLDbContext _context;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IPublishEndpoint _publisher;
        private readonly ReadOnlyPostgreSQLDbContext _readOnlyDbContext;

        public OrdersPublicAPIController(
            PostgreSQLDbContext context,
            IOrderService orderService,
            IOptions<AppOptions> globalData,
            IHttpContextAccessor httpContextAccessor,
            IActivityService activityService,
            ISecurityCheckService securityCheckService,
            ReadOnlyPostgreSQLDbContext readOnlyDbContext,
            IPublishEndpoint publisher
        )
        {
            _context = context;
            _globalData = globalData.Value;
            _orderService = orderService;
            _activityService = activityService;
            _securityCheckService = securityCheckService;
            _httpContextAccessor = httpContextAccessor;
            _publisher = publisher;
            _readOnlyDbContext = readOnlyDbContext;
        }

        [HttpGet] // GET ALL
        [ProducesResponseType(typeof(OrdersQueryResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> Get(string? query, bool? ghostMode,
            [FromQuery] List<OrderStatusCategory>? status,
            DateTime? from,
            DateTime? to,
            DateTime? placedFrom,
            DateTime? placedTo,
            Guid? mid,
            Guid? sid,
            Guid? pid,
            string? orderBy,
            string? sort,
            string? sortField,
            string? timezone,
            string? email,
            string? firstName,
            string? lastName,
            bool? isMIT,
            Guid? orderId,
            string? externalOrderId,
            Guid? contactId,
            //List<string>? types,
            CancellationToken token,
            int pageSize = 10,
            int pageNumber = 1)
        {
            using var workspan = Workspan.StartEndpoint<OrdersPublicAPIController>(this, query, _globalData);

            try
            {
                OrdersQueryResponse response;

                if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN))
                {
                    response = await _orderService.AdminGetAsync(mid, pid, query, ghostMode, status, from, to, placedFrom,
                        placedTo, timezone, orderBy, sort, sortField, pageSize, pageNumber, orderId, externalOrderId,
                        contactId, null, isMIT, sid, email, firstName, lastName, null, null);
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN) || 
                         HttpContext.IsUserInGroup(SuperAdminGroups.INTEGRATION_PARTNER_ADMIN))
                {
                    var isIntegrationPartner = HttpContext.IsUserInGroup(SuperAdminGroups.INTEGRATION_PARTNER_ADMIN);
                    
                    response = await _orderService.PartnerGetAsync(GetPID(), isIntegrationPartner, mid.Value, pid, query, ghostMode, status,
                            from, to, placedFrom, placedTo, timezone, orderBy, sort, sortField, pageSize, pageNumber,
                            orderId, externalOrderId, contactId, null, isMIT, sid, email, firstName, 
                            lastName, null, null);
                }
                else
                {
                    // Remove statuses of orders that are not allowed to be shown to merchants
                    status?.RemoveAll(x => x
                        is OrderStatusCategory.processing
                        or OrderStatusCategory.problem
                        or OrderStatusCategory.draft
                        or OrderStatusCategory.onhold);

                    response = await _orderService.GetAsync(GetMID(), pid, query, status, from, to, placedFrom, placedTo,
                        timezone, orderBy, sort, sortField, pageSize, pageNumber, orderId, externalOrderId,
                        contactId, null, isMIT, sid, email, firstName, lastName, null, null);
                }

                return ReturnResponse(response);
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed fetching records");
            }
        }

        [HttpGet()] // GET BY ID
        [Route("{id:guid}")]
        [PublicApiTelemetry(JwtTenantIdClaim = MyClaimTypes.MERCHANT_ID)]
        [ProducesResponseType(typeof(PublicOrderQueryResponseDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> Get([FromRoute] Guid id, CancellationToken token)
        {
            var mid = GetMID();
            using var workspan = Workspan.StartEndpoint<OrdersPublicAPIController>(this, id, _globalData)
                .Baggage("Mid", mid)
                .Baggage("OrderId", id)
                .LogEnterAndExit();

            try
            {
                if (id == Guid.Empty)
                {
                    ModelState.AddModelError("general", "Invalid id");
                    return ValidationProblem();
                }
                
                _httpContextAccessor.AddCorrelation(mid, id);

                var res = await _orderService.PublicGetByIdAsync(mid, id);

                return res == null ? NotFound() : Ok(res);
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed fetching record");
            }
        }

        [HttpPost]
        [Route("{id:guid}/refund")]
        [ProducesResponseType(typeof(RefundResult), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> RefundPost([FromRoute] Guid id, RefundRequest payload, CancellationToken token)
        {
            var mid = GetMID();

            using var workspan = Workspan.Start<OrdersPublicAPIController>()
                .Baggage("Mid", mid)
                .Baggage("OrderId", id)
                .LogEnterAndExit();

            RefundResult response = new RefundResult();
            try
            {
                if (!await CheckModelStateAsync(mid, null))
                    return ValidationProblem();


                if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN))
                {
                    //response = await _paymentService.CreditAsync(mid, request, token: token);
                    response.AddError("Unsupported operation", "UNSUPPORTED_OPERATION", "UNSUPPORTED_OPERATION");
                    response.Status = nameof(RefundStatus.FAILED);
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN))
                {
                    //response = await _paymentService.CreditAsync(GetMID(), request, token: token);
                    response.AddError("Unsupported operation", "UNSUPPORTED_OPERATION", "UNSUPPORTED_OPERATION");
                    response.Status = nameof(RefundStatus.FAILED);
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.INTEGRATION_PARTNER_ADMIN))
                {
                    //response = await _paymentService.CreditAsync(GetMID(), request, token: token);
                    response.AddError("Unsupported operation", "UNSUPPORTED_OPERATION", "UNSUPPORTED_OPERATION");
                    response.Status = nameof(RefundStatus.FAILED);
                }
                else
                {
                    response = await _orderService.RefundAsync(mid, id, payload, token);
                }

                if (!response.Success)
                    return BadRequest(response);

                return Ok(response);
            }
            catch (FlexValidationException validationException)
            {
                workspan.RecordEndpointCriticalApiError(validationException);

                response.AddError(validationException.Message, validationException.Code.ToString(),
                    validationException.Code.ToString());

                response.ResponseCode = validationException.Code.ToString();
                response.ResponseMessage = validationException.Message;

                response.Status = nameof(RefundStatus.FAILED);
            }
            catch (FlexChargeException e)
            {
                workspan.RecordEndpointCriticalApiError(e);

                response.AddError("Failed to refund order", "REFUND_FAILED", "RefundFailed");
                response.Status = nameof(RefundStatus.FAILED);
            }
            catch (Exception e)
            {
                workspan.RecordEndpointCriticalApiError(e);

                response.AddError("Failed to refund order", "RefundFailed", "RefundFailed");
                response.Status = nameof(RefundStatus.FAILED);
            }

            return BadRequest(response);
        }

        [HttpPost]
        [Route("{id:guid}/void")]
        [ProducesResponseType(typeof(VoidResult), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        private async Task<IActionResult> VoidPost([FromRoute] Guid id, VoidRequest request, CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<OrdersPublicAPIController>(this, request, _globalData)
                .Tag(nameof(request.Mid), request.Mid);

            try
            {
                if (!await CheckAndCorrectMidAsync(request)) return ValidationProblem();
                _securityCheckService.EnsureHasAccessToOrdersPublicApi(request.Mid, GetPID());

                if (!await CheckModelStateAsync(request.Mid, null))
                    return ValidationProblem();

                RefundResult response;
                if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN))
                {
                    //response = await _paymentService.CreditAsync(mid, request, token: token);
                    response = new RefundResult()
                    {
                        Status = nameof(VoidStatus.Failed)
                    };
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN))
                {
                    //response = await _paymentService.CreditAsync(GetMID(), request, token: token);
                    response = new RefundResult()
                    {
                        Status = nameof(VoidStatus.Failed)
                    };
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.INTEGRATION_PARTNER_ADMIN))
                {
                    //response = await _paymentService.CreditAsync(GetMID(), request, token: token);
                    response = new RefundResult()
                    {
                        Status = nameof(VoidStatus.Failed)
                    };
                }
                else
                {
                    //response = await _paymentService.CreditAsync(GetMID(), request, token: token);
                    response = new RefundResult()
                    {
                        Status = nameof(VoidStatus.Failed)
                    };
                }

                return ReturnResponse(response);
            }
            catch (Exception e)
            {
                workspan.RecordEndpointCriticalApiError(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed Void");
            }
        }

        [HttpPost]
        [Route("{id:guid}/cancel")]
        [ProducesResponseType(typeof(VoidResult), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> CancelMitOrderRetries([FromRoute] Guid id, CancellationToken token)
        {
            var workspan = Workspan.StartEndpoint<OrdersPublicAPIController>(this, null, _globalData)
                .Baggage("OrderId", id);

            Order order = null;
            try
            {
                order = await _context.Orders.SingleOrDefaultAsync(x => x.Id == id && x.MerchantId == GetMID());

                if (order == null)
                {
                    return NotFound();
                }

                await _publisher.RunIdempotentCommandWithoutResponseAsync(
                    new StopMitOrderRetriesCommand(id, null, null, SuperAdminGroups.MERCHANT_ADMIN, GetMID(), false));

                await _activityService.CreateActivityAsync(OrdersProcessingActivities.Order_PublicCancelled,
                    set => set
                        .CorrelationId(id).TenantId(order.MerchantId)
                );
            }
            catch (Exception e)
            {
                await _activityService.CreateActivityAsync(
                    OrdersProcessingErrorActivities.Order_PublicProcessingCancelledOrder_Error,
                    set => set
                        .CorrelationId(id).TenantId(order?.MerchantId)
                );

                workspan.RecordEndpointCriticalApiError(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed cancel order");
            }

            return Ok();
        }

        [HttpPost]
        [Route("{id:guid}/external-cancel")]
        [ProducesResponseType(typeof(VoidResult), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> CancelByExternalId([FromRoute] string id, CancellationToken token)
        {
            var workspan = Workspan.StartEndpoint<OrdersPublicAPIController>(this, null, _globalData)
                .Baggage("ExternalOrderId", id);

            Order order = null;
            try
            {
                order = await _context.Orders.SingleOrDefaultAsync(x =>
                    x.ReferenceNumber == id && x.MerchantId == GetMID());

                if (order == null)
                {
                    return NotFound();
                }

                order.StatusCategory = OrderStatusCategory.cancelled.ToString();
                _context.Orders.Update(order);
                await _context.SaveChangesAsync(token);

                await _activityService.CreateActivityAsync(OrdersProcessingActivities.Order_PublicCancelled,
                    set => set
                        .CorrelationId(order.Id).TenantId(order.MerchantId)
                        .Meta(meta => meta
                            .SetValue("ExternalOrderId", id)
                        ));
            }
            catch (Exception e)
            {
                await _activityService.CreateActivityAsync(
                    OrdersProcessingErrorActivities.Order_PublicProcessingCancelledOrder_Error,
                    set => set
                        .CorrelationId(order?.Id).TenantId(order?.MerchantId)
                        .Meta(meta => meta
                            .SetValue("ExternalOrderId", id)
                        ));

                workspan.RecordEndpointCriticalApiError(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed cancel order");
            }

            return Ok();
        }


        #region Common

        private async Task<bool> CheckModelStateAsync(Guid? mid, Guid? orderId)
        {
            if (!ModelState.IsValid)
            {
                await _activityService.CreateActivityAsync(ApiErrorActivities.ModelStateError, ModelState,
                    set => set.TenantId(mid).CorrelationId(orderId));

                Workspan.Current?.RecordEndpointBadRequest(ModelState);

                return false;
            }

            return true;
        }

        private async Task<bool> CheckAndCorrectMidAsync(IRequestWithOptionalMid request)
        {
            using var workspan = Workspan.Start<OrdersPublicAPIController>();

            //Check Mid in case no PartnerId is specified in JWT

            Guid? partnerID = GetPID();

            //No partner Id => check that request Mid == Mid from JTW
            if (partnerID == Guid.Empty || partnerID == null)
            {
                #region Using MerchantId from JWT and comparing it to Mid from JWT

                partnerID = null;

                var midFromJWT = GetMID();
                workspan.Baggage("Mid", midFromJWT);

                if (request.Mid != null)
                {
                    if (request.Mid != midFromJWT)
                    {
                        workspan.LogSecurity.Error(
                            "Request Mid is different than JWT MID. Request Mid = {requestMid}. JWT Mid = {jwtMid}",
                            request.Mid, midFromJWT);

                        await _activityService.CreateActivityAsync(
                            SecurityCriticalActivities.MerchantIdInRequestIsDifferentThanInJwt,
                            data: request.Mid,
                            set => set.TenantId(midFromJWT));

                        ModelState.AddModelError("mid", $"Wrong Merchant Id");
                        workspan.RecordEndpointBadRequest(ModelState);

                        return false;
                    }
                    else
                    {
                        workspan.Log.Information(
                            "Request Mid is specified and correct. Request Mid = {requestMid}. JWT Mid = {jwtMid}",
                            request.Mid, midFromJWT);
                    }
                }
                else
                {
                    workspan.Log.Information("Request Mid is not specified. Using Mid from JWT. JWT Mid = {jwtMid}",
                        midFromJWT);

                    request.Mid = midFromJWT;
                }

                #endregion
            }
            else
            {
                workspan.Baggage("Pid", partnerID);
            }

            //Check that Mid is specified
            if (request.Mid != null && request.Mid != Guid.Empty)
            {
                return true;
            }
            else
            {
                await _activityService.CreateActivityAsync(ApiErrorActivities.MissingMerchantId,
                    data: partnerID);

                ModelState.AddModelError("mid", "Merchant Id is required");
                workspan.RecordEndpointBadRequest(ModelState);

                return false;
            }
        }

        #endregion
    }
}