using FlexCharge.Common;
using FlexCharge.Common.Authentication;
using FlexCharge.Orders.DTO;
using FlexCharge.Orders.Entities;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using FlexCharge.Common.AutoMapper;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.Telemetry;
using FlexCharge.Orders.DTO.Batches;
using FlexCharge.Orders.Services.FinancialAccountsServices;
using FlexCharge.Orders.Services.PayoutServices;
using FlexCharge.Utils;

namespace FlexCharge.Orders.Controllers
{
    [Route("[controller]")]
    [ApiController]
    [JwtAuth]
    public class PayoutsController : BaseController
    {
        private readonly AppOptions _globalData;
        private readonly PostgreSQLDbContext _dbContext;
        private readonly IMapper _mapper;
        private readonly IBatchService _batchService;
        private readonly IPayoutService _payoutService;
        private readonly IFinancialAccountsService _financialAccountsService;


        public PayoutsController(
            IOptions<AppOptions> globalData,
            PostgreSQLDbContext dbContext,
            IMapper mapper,
            IBatchService batchService,
            IPayoutService payoutService,
            IFinancialAccountsService financialAccountsService)
        {
            _globalData = globalData.Value;
            _dbContext = dbContext;
            _mapper = mapper;
            _batchService = batchService;
            _payoutService = payoutService;
            _financialAccountsService = financialAccountsService;
        }

        [HttpGet] // GET ALL NOT POSTED
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(IEnumerable<PayoutDTO>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetNotPostedPayouts([FromQuery] PayoutQueryDTO payload,
            CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<PayoutsController>(this, payload, _globalData)
                .Baggage(nameof(payload.Mid), payload.Mid);

            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            try
            {
                var payouts = new List<PayoutDTO>();

                if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN))
                {
                    var receiverId = Guid.Empty;
                    if (payload.Mid.HasValue)
                        receiverId = await _financialAccountsService.GetIdByMerchantIdAsync(payload.Mid.Value, token);

                    payouts = await _payoutService.GetPayoutsAsync(Guid.Empty, receiverId, payload.PartnerId,
                        payload.Status,
                        payload.BatchType,
                        DatesHelper.GetStartOfMonthOrDefault(payload.YearAndMonth),
                        DatesHelper.GetEndOfMonthOrDefault(payload.YearAndMonth));
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN))
                {
                    var senderId = await _financialAccountsService.GetIdByPartnerIdAsync(GetPID(), token);
                    payouts = await _payoutService.GetPayoutsAsync(senderId, Guid.Empty, payload.PartnerId,
                        payload.Status,
                        payload.BatchType,
                        DatesHelper.GetStartOfMonthOrDefault(payload.YearAndMonth),
                        DatesHelper.GetEndOfMonthOrDefault(payload.YearAndMonth));
                }
                else
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }

                return Ok(payouts);
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed fetching records");
            }
        }

        [HttpGet("pending")] //GET ALL PENDING
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(IEnumerable<PayoutDTO>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetPendingPayouts([FromQuery] PayoutQueryDTO payload, CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<PayoutsController>(this, payload, _globalData)
                .Baggage(nameof(payload.Mid), payload.Mid);

            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            try
            {
                List<PayoutDTO> payouts;

                payload.Status = PayoutStatus.Pending;

                if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN))
                {
                    var receiverId = Guid.Empty;
                    if (payload.Mid.HasValue)
                        receiverId = await _financialAccountsService.GetIdByMerchantIdAsync(payload.Mid.Value, token);

                    payouts = await _payoutService.GetPayoutsAsync(Guid.Empty, receiverId, payload.PartnerId,
                        payload.Status,
                        payload.BatchType,
                        DatesHelper.GetStartOfMonthOrDefault(payload.YearAndMonth),
                        DatesHelper.GetEndOfMonthOrDefault(payload.YearAndMonth));
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN))
                {
                    var senderId = await _financialAccountsService.GetIdByPartnerIdAsync(GetPID(), token);
                    payouts = await _payoutService.GetPayoutsAsync(senderId, Guid.Empty, payload.PartnerId,
                        payload.Status,
                        payload.BatchType,
                        DatesHelper.GetStartOfMonthOrDefault(payload.YearAndMonth),
                        DatesHelper.GetEndOfMonthOrDefault(payload.YearAndMonth));
                }
                else
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }

                return Ok(payouts);
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed fetching records");
            }
        }


        /// <summary>
        /// Payout Details
        /// </summary>
        /// <param name="id">Payout Id</param>
        /// <param name="token"></param>
        /// <param name="pageSize"></param>
        /// <param name="pageNumber"></param>
        /// <returns></returns>
        [HttpGet()] // PAYMENT DETAILS (DRILL-DOWN TO ORDERS)
        [Route("{id:guid}/details")]
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(PagedDTO<PayoutDetailsDTO>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> Get([FromRoute] Guid id,
            CancellationToken token,
            int pageSize = 10,
            int pageNumber = 1)
        {
            using var workspan = Workspan.StartEndpoint<PayoutsController>(this, id, _globalData);

            try
            {
                var payoutDetails = _dbContext.Batches
                    .Where(x => x.Id == id)
                    .SelectMany(x => x.BatchRecords)
                    .GroupJoin(_dbContext.Orders, x => x.RelatedOrderId, x => x.Id,
                        (groupedBatchRecord, groupedOrder) => new {groupedBatchRecord, groupedOrder})
                    .SelectMany(x => x.groupedOrder.DefaultIfEmpty(), (batchRecord, order) => new {batchRecord, order})
                    .Select(
                        z => new PayoutDetailsDTO()
                        {
                            OrderDate = z.order.OrderPlacedDate,
                            ExternalOrderId = z.order.ReferenceNumber,
                            MerchantId = z.order.MerchantId,
                            Type = EnumHelpers.ParseGetDescriptionOrInput<BatchRecordTypes>(z.batchRecord
                                .groupedBatchRecord.Type),
                            PaymentType =
                                EnumHelpers.ParseGetDescriptionOrInput<DirectionEnum>(z.batchRecord.groupedBatchRecord
                                    .Direction),
                            Amount = Formatters.IntToDecimal(z.batchRecord.groupedBatchRecord.Amount),
                            FlexChargeFee = Formatters.IntToDecimal(z.batchRecord.groupedBatchRecord.Fee),
                            Payout = z.batchRecord.groupedBatchRecord.Direction == nameof(DirectionEnum.Credit)
                                ? Formatters.IntToDecimal(z.batchRecord.groupedBatchRecord.Amount -
                                                          z.batchRecord.groupedBatchRecord.Fee)
                                : Formatters.IntToDecimal(-z.batchRecord.groupedBatchRecord.Amount -
                                                          z.batchRecord.groupedBatchRecord.Fee),
                            OrderId = z.order.Id,
                            Adjustment = Formatters.IntToDecimal(z.batchRecord.groupedBatchRecord.Adjustment),
                            Currency = z.batchRecord.groupedBatchRecord.Batch.Currency,
                            CurrencyCode = z.batchRecord.groupedBatchRecord.Batch.CurrencyCode,
                            CurrencySymbol = z.batchRecord.groupedBatchRecord.Batch.CurrencySymbol
                        }
                    );

                var eeomn = _mapper.Map<IPagedList<PayoutDetailsDTO>, PagedDTO<PayoutDetailsDTO>>(await payoutDetails
                    .OrderByDescending(x => x.OrderDate).ToPagedListAsync(pageNumber, pageSize));

                return Ok(eeomn);
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed fetching record");
            }
        }


        [HttpPost("{id:guid}/post")]
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> PostBatch([FromRoute] Guid id, CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<PayoutsController>(this, id, _globalData);

            try
            {
                if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN))
                {
                    await _batchService.PostBatch(id);
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN))
                {
                    await _batchService.PostBatch(id);
                }

                return Ok();
            }
            catch (FlexValidationException e)
            {
                workspan.RecordException(e);

                ModelState.AddModelError(e.Property, e.Message);

                return BadRequest(ModelState);
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed posting batch");
            }
        }

        [HttpPost("{id:guid}/unpost")]
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> UnpostBatch([FromRoute] Guid id, CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<PayoutsController>(this, id, _globalData);

            try
            {
                if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN))
                {
                    await _batchService.UnPostBatch(null, id);
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN))
                {
                    await _batchService.UnPostBatch(GetPID(), id);
                }

                return Ok();
            }
            catch (FlexValidationException e)
            {
                workspan.RecordException(e);

                ModelState.AddModelError(e.Property, e.Message);

                return BadRequest(ModelState);
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed batch unpost");
            }
        }

        [HttpGet("export")] // EXPORT ALL NOT POSTED
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(string), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> Export([FromQuery] PayoutQueryDTO payload,
            CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<PayoutsController>(this, payload, _globalData)
                .Baggage(nameof(payload.Mid), payload.Mid);

            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            try
            {
                string csv;
                if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN))
                {
                    var receiverId = Guid.Empty;
                    if (payload.Mid.HasValue)
                        receiverId = await _financialAccountsService.GetIdByMerchantIdAsync(payload.Mid.Value, token);

                    csv = await _payoutService.ExportUnpostedAsync(Guid.Empty, receiverId, payload.PartnerId,
                        payload.BatchType, DatesHelper.GetStartOfMonthOrDefault(payload.YearAndMonth),
                        DatesHelper.GetEndOfMonthOrDefault(payload.YearAndMonth));
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN))
                {
                    var senderFinancialAccountId =
                        await _financialAccountsService.GetIdByPartnerIdAsync(GetPID(), token);
                    if (senderFinancialAccountId == Guid.Empty)
                        return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");

                    csv = await _payoutService.ExportUnpostedAsync(senderFinancialAccountId, Guid.Empty,
                        payload.PartnerId, payload.BatchType,
                        DatesHelper.GetStartOfMonthOrDefault(payload.YearAndMonth),
                        DatesHelper.GetEndOfMonthOrDefault(payload.YearAndMonth));
                }
                else
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }

                return Ok(csv);
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed fetching records");
            }
        }

        [HttpGet("export-pending")] //EXPORT ALL PENDING
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(string), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> ExportPendingPayouts([FromQuery] PayoutQueryDTO payload,
            CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<PayoutsController>(this, payload, _globalData)
                .Baggage(nameof(payload.Mid), payload.Mid);

            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            try
            {
                string csv;
                if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN))
                {
                    var receiverId = Guid.Empty;
                    if (payload.Mid.HasValue)
                        receiverId = await _financialAccountsService.GetIdByMerchantIdAsync(payload.Mid.Value, token);

                    csv = await _payoutService.ExportPendingAsync(Guid.Empty, receiverId, payload.PartnerId,
                        payload.BatchType, DatesHelper.GetStartOfMonthOrDefault(payload.YearAndMonth),
                        DatesHelper.GetEndOfMonthOrDefault(payload.YearAndMonth));
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN))
                {
                    var senderFinancialAccountId =
                        await _financialAccountsService.GetIdByPartnerIdAsync(GetPID(), token);
                    if (senderFinancialAccountId == Guid.Empty)
                        return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");

                    csv = await _payoutService.ExportPendingAsync(senderFinancialAccountId, Guid.Empty,
                        payload.PartnerId, payload.BatchType,
                        DatesHelper.GetStartOfMonthOrDefault(payload.YearAndMonth),
                        DatesHelper.GetEndOfMonthOrDefault(payload.YearAndMonth));
                }
                else
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }

                return Ok(csv);
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed fetching records");
            }
        }

        [HttpGet("export-shelve")] //EXPORT ALL PENDING
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(IEnumerable<PayoutDTO>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> ExportShelvedPayouts([FromQuery] PayoutQueryDTO payload,
            CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<PayoutsController>(this, payload, _globalData)
                .Baggage(nameof(payload.Mid), payload.Mid);

            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            try
            {
                string csv;
                if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN))
                {
                    var receiverId = Guid.Empty;
                    if (payload.Mid.HasValue)
                        receiverId = await _financialAccountsService.GetIdByMerchantIdAsync(payload.Mid.Value, token);

                    csv = await _payoutService.ExportShelvedAsync(Guid.Empty, receiverId, payload.PartnerId,
                        payload.BatchType, DatesHelper.GetStartOfMonthOrDefault(payload.YearAndMonth),
                        DatesHelper.GetEndOfMonthOrDefault(payload.YearAndMonth));
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN))
                {
                    var senderFinancialAccountId =
                        await _financialAccountsService.GetIdByPartnerIdAsync(GetPID(), token);
                    if (senderFinancialAccountId == Guid.Empty)
                        return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");

                    csv = await _payoutService.ExportShelvedAsync(senderFinancialAccountId, Guid.Empty,
                        payload.PartnerId, payload.BatchType,
                        DatesHelper.GetStartOfMonthOrDefault(payload.YearAndMonth),
                        DatesHelper.GetEndOfMonthOrDefault(payload.YearAndMonth));
                }
                else
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }

                return Ok(csv);
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed fetching records");
            }
        }

        [HttpGet()] // EXPORT PAYMENT DETAILS (DRILL-DOWN TO ORDERS)
        [Route("{id:guid}/export-details")]
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(PayoutDetailsDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> ExportPayoutDetails([FromRoute] Guid id,
            CancellationToken token,
            int pageSize = 100,
            int pageNumber = 1)
        {
            using var workspan = Workspan.StartEndpoint<PayoutsController>(this, id, _globalData);

            try
            {
                string payoutDetails;
                if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN))
                {
                    payoutDetails =
                        await _payoutService.ExportDetailsAsync(Guid.Empty, Guid.Empty, id, pageSize, pageNumber);
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN))
                {
                    var senderFinancialAccountId =
                        await _financialAccountsService.GetIdByPartnerIdAsync(GetPID(), token);
                    if (senderFinancialAccountId == Guid.Empty)
                        return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");

                    payoutDetails =
                        await _payoutService.ExportDetailsAsync(senderFinancialAccountId, Guid.Empty, id, pageSize,
                            pageNumber);
                }
                else
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }

                byte[] byteArray = Encoding.UTF8.GetBytes(payoutDetails);

                // Should not dispose the stream as it will be used by the File method and will be dispose after
                return File(new MemoryStream(byteArray), "text/plain", "Unposted payout details.csv");
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed fetching record");
            }
        }

        [HttpPost("{id:guid}/adjustment")]
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(PayoutDetailsDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> AddAdjustment([FromRoute] Guid id, AdjustmentCreateRequestDTO payload,
            CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<PayoutsController>(this, id, _globalData);

            try
            {
                if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN))
                {
                    await _payoutService.AddAdjustmentAsync(Guid.Empty, Guid.Empty, id, payload);
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN))
                {
                    var senderFinancialAccountId =
                        await _financialAccountsService.GetIdByPartnerIdAsync(GetPID(), token);
                    if (senderFinancialAccountId == Guid.Empty)
                        return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");

                    await _payoutService.AddAdjustmentAsync(senderFinancialAccountId, Guid.Empty, id, payload);
                }
                else
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }

                return Ok();
            }
            catch (FlexValidationException e)
            {
                workspan.RecordException(e);

                ModelState.AddModelError(e.Property, e.Message);
                return BadRequest(ModelState);
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, e.Message);
            }
        }

        [HttpPost("{id:guid}/comment")]
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(PayoutDetailsDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> AddComment([FromRoute] Guid id, CommentCreateRequestDTO payload,
            CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<PayoutsController>(this, id, _globalData);

            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            try
            {
                if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN))
                {
                    await _payoutService.AddCommentAsync(Guid.Empty, Guid.Empty, id, payload.Comment);
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN))
                {
                    var senderFinancialAccountId =
                        await _financialAccountsService.GetIdByPartnerIdAsync(GetPID(), token);
                    if (senderFinancialAccountId == Guid.Empty)
                        return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");

                    await _payoutService.AddCommentAsync(senderFinancialAccountId, Guid.Empty, id, payload.Comment);
                }
                else
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }

                return Ok();
            }
            catch (FlexValidationException e)
            {
                workspan.RecordException(e);

                ModelState.AddModelError(e.Property, e.Message);
                return BadRequest(ModelState);
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, e.Message);
            }
        }

        /// <summary>
        /// Apply Reserve utilization
        /// </summary>
        /// <param name="id"></param>
        /// <param name="payload"></param>
        /// <param name="token"></param>
        /// <returns></returns>
        [HttpPost("{id:guid}/apply-reserve-utilization")]
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(PayoutDetailsDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> ApplyReserveUtilization([FromRoute] Guid id, UtilizeFundsReserveDTO payload,
            CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<PayoutsController>(this, id, _globalData);

            try
            {
                if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN))
                {
                    await _payoutService.ApplyReserveUtilizationAsync(Guid.Empty, Guid.Empty, id, payload);
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN))
                {
                    var senderFinancialAccountId =
                        await _financialAccountsService.GetIdByPartnerIdAsync(GetPID(), token);
                    if (senderFinancialAccountId == Guid.Empty)
                        return StatusCode(StatusCodes.Status403Forbidden, "Failed fetching record");

                    await _payoutService.ApplyReserveUtilizationAsync(senderFinancialAccountId, Guid.Empty, id,
                        payload);
                }
                else
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }

                return Ok();
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed applying reserve");
            }
        }

        /// <summary>
        /// Reset Reserve utilization
        /// </summary>
        /// <param name="id"></param>
        /// <param name="payload"></param>
        /// <param name="token"></param>
        /// <returns></returns>
        [HttpPost("{id:guid}/remove-reserve-utilization")]
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(PayoutDetailsDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> RemoveReserveUtilization([FromRoute] Guid id, CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<PayoutsController>(this, id, _globalData);

            try
            {
                if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN))
                {
                    await _payoutService.RemoveReserveUtilizationAsync(Guid.Empty, Guid.Empty, id);
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN))
                {
                    var senderFinancialAccountId =
                        await _financialAccountsService.GetIdByPartnerIdAsync(GetPID(), token);
                    if (senderFinancialAccountId == Guid.Empty)
                        return StatusCode(StatusCodes.Status403Forbidden, "Failed fetching record");

                    await _payoutService.RemoveReserveUtilizationAsync(senderFinancialAccountId, Guid.Empty, id);
                }
                else
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }

                return Ok();
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed removing reserve utilization");
            }
        }

        [HttpGet("shelve")] //GET ALL SHELVED
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(IEnumerable<BatchDTO>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetShelvedPayouts([FromQuery] PayoutQueryDTO payload, CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<PayoutsController>(this, payload, _globalData)
                .Baggage(nameof(payload.Mid), payload.Mid);

            try
            {
                var shelvedPayouts = new List<BatchDTO>();
                if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN))
                {
                    if (payload.Mid.HasValue)
                    {
                        var receiverId =
                            await _financialAccountsService.GetIdByMerchantIdAsync(payload.Mid.Value, token);
                        shelvedPayouts = await _batchService.GetShelvedAsync(Guid.Empty, receiverId, payload.PartnerId,
                            payload.BatchType,
                            DatesHelper.GetStartOfMonthOrDefault(payload.YearAndMonth),
                            DatesHelper.GetEndOfMonthOrDefault(payload.YearAndMonth));
                    }
                    else
                        shelvedPayouts = await _batchService.GetShelvedAsync(Guid.Empty, Guid.Empty, payload.PartnerId,
                            payload.BatchType, DatesHelper.GetStartOfMonthOrDefault(payload.YearAndMonth),
                            DatesHelper.GetEndOfMonthOrDefault(payload.YearAndMonth));

                    return Ok(shelvedPayouts.OrderByDescending(x => x.PayoutDate));
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN))
                {
                    var senderFinancialAccountId =
                        await _financialAccountsService.GetIdByPartnerIdAsync(GetPID(), token);
                    if (senderFinancialAccountId == Guid.Empty)
                        return StatusCode(StatusCodes.Status403Forbidden, "Failed fetching record");

                    shelvedPayouts =
                        await _batchService.GetShelvedAsync(senderFinancialAccountId, Guid.Empty, payload.PartnerId,
                            payload.BatchType, DatesHelper.GetStartOfMonthOrDefault(payload.YearAndMonth),
                            DatesHelper.GetEndOfMonthOrDefault(payload.YearAndMonth));

                    return Ok(shelvedPayouts.OrderByDescending(x => x.PayoutDate));
                }

                return StatusCode(StatusCodes.Status403Forbidden, "Failed fetching record");
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed fetching records");
            }
        }

        [HttpPost("{id:guid}/shelve")]
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ShelveBatch([FromRoute] Guid id, CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<PayoutsController>(this, id, _globalData);

            try
            {
                await _batchService.ShelveAsync(id);

                return Ok();
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed posting batch");
            }
        }

        [HttpPost("{id:guid}/unshelve")]
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> UnshelveBatch([FromRoute] Guid id, CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<PayoutsController>(this, id, _globalData);

            try
            {
                await _batchService.UnshelveAsync(id);

                return Ok();
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed posting batch");
            }
        }
    }
}