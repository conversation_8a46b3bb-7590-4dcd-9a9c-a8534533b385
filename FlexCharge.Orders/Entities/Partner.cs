using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore.Metadata.Internal;

namespace FlexCharge.Orders.Entities;

// public class Partner : AuditableEntity
// {
//     public string Name { get; set; }
//
//     public IEnumerable<Merchant> Merchants { get; set; }
//
//     public Guid? FinancialAccountId { get; set; }
//     public FinancialAccount FinancialAccount { get; set; }
// }