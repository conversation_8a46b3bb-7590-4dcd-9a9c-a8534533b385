using System;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.MassTransit;
using FlexCharge.Contracts;
using MassTransit;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace FlexCharge.Orders.Consumers;

public class OrderCreatedEventConsumer : ConsumerBase<OrderCreatedEvent>
{
    public OrderCreatedEventConsumer(IServiceScopeFactory serviceScopeFactory) : base(serviceScopeFactory)
    {
    }

    protected override async Task ConsumeMessage(OrderCreatedEvent message, CancellationToken cancellationToken)
    {
        try
        {
            Workspan.Log.Information("Value: {Value}", message.OrderId);
        }
        catch (Exception e)
        {
            Workspan.RecordException(e, $"EXCEPTION: OrderCreatedEventConsumer > FAILED");
        }

        await Task.CompletedTask;
    }
}