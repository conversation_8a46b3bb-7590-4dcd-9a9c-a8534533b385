using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.MassTransit;
using FlexCharge.Contracts.Commands;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Orders.Consumers;

public class
    GetOrderTimeInformationCommandConsumer : CommandConsumer<GetOrderTimeInformationCommand,
    GetOrderTimeInformationCommandResponse>
{
    private readonly ReadOnlyPostgreSQLDbContext _readOnlyDbContext;

    public GetOrderTimeInformationCommandConsumer(
        IServiceScopeFactory serviceScopeFactory,
        ReadOnlyPostgreSQLDbContext readOnlyDbContext
    ) : base(serviceScopeFactory)
    {
        _readOnlyDbContext = readOnlyDbContext;
    }

    protected override async Task<GetOrderTimeInformationCommandResponse> ConsumeCommand(
        GetOrderTimeInformationCommand command, CancellationToken cancellationToken)
    {
        var order = await _readOnlyDbContext
            .Orders.Where(x => x.Id == command.OrderId)
            .SingleOrDefaultAsync();

        if (order == null)
            return new GetOrderTimeInformationCommandResponse() {OrderFound = false};

        return new GetOrderTimeInformationCommandResponse
        {
            OrderFound = true,
            CreatedOn = order.CreatedOn,
            ModifiedOn = order.ModifiedOn,
            OrderPlacedDate = order.OrderPlacedDate,
            ExpiryDate = order.ExpiryDate
        };
    }
}