using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.MassTransit;
using FlexCharge.Contracts;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Orders.Consumers;

public class OrderSessionMatchedEventConsumer : ConsumerBase<OrderSessionMatchedEvent>
{
    private readonly PostgreSQLDbContext _dbContext;

    public OrderSessionMatchedEventConsumer(IServiceScopeFactory serviceScopeFactory,
        PostgreSQLDbContext dbContext) : base(serviceScopeFactory)
    {
        _dbContext = dbContext;
    }

    protected override async Task ConsumeMessage(OrderSessionMatchedEvent message, CancellationToken cancellationToken)
    {
        var order = await _dbContext.Orders.SingleAsync(x => x.Id == message.OrderId);

        order.BrowserInformation = message.BrowserInformation;

        await _dbContext.SaveChangesAsync(cancellationToken);
    }
}