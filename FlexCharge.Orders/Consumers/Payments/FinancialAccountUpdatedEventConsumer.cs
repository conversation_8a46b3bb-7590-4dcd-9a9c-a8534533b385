using System;
using System.Linq;
using System.Threading.Tasks;
using FlexCharge.Common.DistributedLock;
using FlexCharge.Common.Shared.Payments;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Contracts.FinancialAccounts;
using FlexCharge.Orders.DistributedLock;
using FlexCharge.Orders.Entities;
using FlexCharge.Orders.Services.FinancialAccountsServices;
using FlexCharge.Orders.Services.Merchants;
using MassTransit;
using Microsoft.EntityFrameworkCore;

namespace FlexCharge.Orders.Consumers;

public class FinancialAccountUpdatedEventConsumer : IConsumer<FinancialAccountUpdatedEvent>
{
    private IFinancialAccountsService _financialAccountsService;
    private readonly IDistributedLockService _distributedLockService;

    public FinancialAccountUpdatedEventConsumer(IFinancialAccountsService financialAccountsService,
        IDistributedLockService distributedLockService)
    {
        _financialAccountsService = financialAccountsService;
        _distributedLockService = distributedLockService;
    }

    public async Task Consume(ConsumeContext<FinancialAccountUpdatedEvent> context)
    {
        using var workspan = Workspan.Start<FinancialAccountUpdatedEventConsumer>()
            .Context(context)
            .LogEnterAndExit();

        try
        {
            await using var @lock = await _distributedLockService
                .AcquireLockAsync(LockKeyFactory.CreateFinancialAccountKey(context.Message.Id),
                    TimeSpan.FromSeconds(15),
                    maxRetryDuration: TimeSpan.FromMinutes(1));

            await _financialAccountsService.UpsertAsync(new FinancialAccount
            {
                Id = context.Message.Id,
                RelatedEntityType = context.Message.RelatedEntityType,
                RelatedEntityId = context.Message.RelatedEntityId,
                RelatedEntityDba = context.Message.RelatedEntityDba,
                AccountName = context.Message.Name,
                AccountType = context.Message.AccountType,
                Currency = context.Message.Currency,
            }, context.CancellationToken);
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
        }
    }
}