using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Contracts.Commands;
using FlexCharge.Contracts.Commands.Tracking;
using FlexCharge.Orders.Entities;
using FlexCharge.Orders.Services;
using FlexCharge.Orders.Services.Merchants;
using MassTransit;
using Microsoft.Extensions.Caching.Distributed;

namespace FlexCharge.Orders.RepaymentStrategies;

public interface IRepaymentStrategy
{
    string StrategyId { get; }
    //string LastChargeOperationResponseCode { get; }

    void Initialize(Order order,
        Merchant merchant, Site site,
        IReadOnlyDictionary<string, string> strategyParameters,
        IPublishEndpoint publishEndpoint,
        PostgreSQLDbContext dbContext,
        //IOrderService orderService,
        IRequestClient<CapturePaymentCommand> capturePaymentRequest,
        IRequestClient<DebitPaymentCommand> debitPaymentRequest,
        IRequestClient<VoidPaymentCommand> voidPaymentRequest,
        IMerchantsService merchantsService,
        IActivityService activityService);

    //Task ScheduleAsync(Guid schedulerJobId, TimeSpan? delayExecution = null);

    Task ExecuteAsync();
}