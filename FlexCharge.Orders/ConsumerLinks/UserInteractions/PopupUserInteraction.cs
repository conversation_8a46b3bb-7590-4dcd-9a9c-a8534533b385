using System.Threading.Tasks;
using FlexCharge.Common.Shared.UIBuilder;
using Newtonsoft.Json;

namespace FlexCharge.Orders.ConsumerLinks.UserInteractions;

public abstract class PopupUserInteraction : UserInteractionBase
{
    public override bool ShowCloseButton => false;
    public override bool ShowLogo => LogoUrl != null;

    public virtual bool ShowPopupCloseCounter => false;
    public virtual bool ShowOKButton => false;

    protected async Task<UserInteractionResult> ShowAnotherInteractionAsync(string userInteractionId)
    {
        NextUserInteraction = GetUserInteractionId(userInteractionId);
        return await Task.FromResult(UserInteractionResult.SHOW_ANOTHER_INTERACTION);
    }

    protected async Task<UserInteractionResult> ShowAnotherInteractionAsync<TInteractionData>(string userInteractionId,
        TInteractionData data)
        where TInteractionData : class
    {
        NextUserInteraction = GetUserInteractionId(userInteractionId);
        NextUserInteractionData = data != null ? JsonConvert.SerializeObject(data) : null;
        return await Task.FromResult(UserInteractionResult.SHOW_ANOTHER_INTERACTION);
    }


    protected async Task<UserInteractionResult> CloseThisPopupAsync()
    {
        return await Task.FromResult(UserInteractionResult.CLOSE);
    }

    protected override void CreateFormButtons()
    {
        if (ShowOKButton)
        {
            var okButton =
                UI.OKButton()
                    .Text("OK");

            if (ShowPopupCloseCounter)
            {
                okButton.Class("ui-widget-button-counter");
            }
        }
    }
}