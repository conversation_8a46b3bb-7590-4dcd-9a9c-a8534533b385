// using System;
// using System.Collections.Generic;
// using System.Threading.Tasks;
// using FlexCharge.Common.Shared.UIBuilder;
// using FlexCharge.Eligibility.Controllers;
//
// namespace FlexCharge.Orders.ConsumerLinks.UserInteractions.Implementations.ClickToRefund;
//
// public class I0105_RefundFailed : PopupUserInteraction
// {
//     public override bool IsImplemented => true;
//
//     protected override void Create()
//     {
//         UI.Title()
//             .Text($"Refund Failed.");
//         
//         UI.HorizontalSeparator();
//
//         UI.SubTitle()
//             .Text($"This can be due to the following reasons:");
//         
//         using (UI.StartFullRowList())
//         {
//             UI.ListItem()
//                 .Text(
//                     $"Refund is already issued. You will receive email notification in this case.");
//             
//             UI.ListItem()
//                 .Text(
//                     $"Order is cancelled or non-completed.");
//             
//             UI.ListItem()
//                 .Text(
//                     $"Some technical problem occured. Please, try again later.");
//
//         }
//     }
//
//     protected override void CreateFormButtons()
//     {
//     }
//
//     protected override async Task<UserInteractionResult> ExecuteAsync(NextPutRequest request)
//     {
//        return await CloseThisPopupAsync();
//     }
// }