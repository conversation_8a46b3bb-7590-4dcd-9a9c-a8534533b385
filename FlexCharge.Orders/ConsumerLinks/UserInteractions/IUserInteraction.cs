using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Common.Shared.UIBuilder;
using FlexCharge.Eligibility.Controllers;
using FlexCharge.Orders.Entities;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Orders.ConsumerLinks.UserInteractions;

public interface IUserInteraction
{
    string UserInteractionId { get; }

    /// <summary>
    /// To support executing only user interaction that are implemented
    /// </summary>
    bool IsImplemented { get; }

    void Initialize(IServiceScopeFactory serviceScopeFactory, IActivityService activityService);

    Task<bool> CanBeExecutedAsync();

    Dictionary<string, string> ChallengeParameters { get; }

    bool IsUserCancellable { get; }

    bool InIFrame { get; set; }
    bool RequiresIFrame { get; }


    /// <summary>
    /// If cure returns ANOTHER_CURE result, this property will hold cure to execute name
    /// </summary>
    string? NextUserInteraction { get; }

    string? NextUserInteractionData { get; }

    /// <summary>
    /// If True, user interaction will be reset and closed.
    /// On next execution, it will be started again from the initial interaction.
    /// </summary>
    public bool ResetUserInteraction { get; }


    Task<List<UIComponent>> CreateUserInterfaceAsync();
    Task<UserInteractionResult> ProcessResultsAsync(NextPutRequest request);


    /// <summary>
    /// 
    /// </summary>
    /// <param name="request"></param>
    /// <returns>Returns false in case of general validation error -> NOT ELIGIBLE</returns>
    bool TryValidateUserAnswers(NextPutRequest request, out IEnumerable<AnswerValidationError> validationErrors);

    Task<UserInteractionPreExecutionResult> TryPreExecuteAsync();
    Task InitializeInteractionAsync(Link link);
    Task OnUserInterfaceCreated();


    void ReplaceTitle(string newTitle);
    void SetPartnerName(string name);
    void SetOuterLogoUrl(string url);
    void SetLogoUrl(string url);
    void SetTermsAndConditionsUrl(string url);
    void SetPrivacyPolicyUrl(string url);
}