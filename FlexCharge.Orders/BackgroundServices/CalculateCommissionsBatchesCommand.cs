using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.BackgroundJobs;
using FlexCharge.Common.Telemetry;
using FlexCharge.Orders.Services.BatchServices.Batches;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Orders.Services.PayoutServices;

public class CalculateCommissionsBatchesCommand : BackgroundWorkerCommand
{
    public Guid? partnerId { get; set; }
    public DateTime start { get; set; }
    public DateTime end { get; set; }

    public CalculateCommissionsBatchesCommand(Guid? partnerId, DateTime start, DateTime end)
    {
        this.partnerId = partnerId;
        this.start = start;
        this.end = end;
    }

    protected override async Task ExecuteAsync(IServiceProvider serviceProvider, CancellationToken cancellationToken)
    {
        using var workspan = Workspan.Start<CalculateFIPADBatchesCommand>()
            .LogEnterAndExit();

        var batchService = serviceProvider.GetRequiredService<IBatchService>();
        //var partnersService = serviceProvider.GetRequiredService<IPartnerService>();
        using var dbContext = serviceProvider.GetRequiredService<PostgreSQLDbContext>();

        try
        {
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, "EXCEPTION: CalculateCommissionsBatchesCommand > ExecuteAsync");
        }
    }
}