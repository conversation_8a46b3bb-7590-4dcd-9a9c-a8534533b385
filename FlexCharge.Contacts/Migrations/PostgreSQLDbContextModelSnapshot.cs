// <auto-generated />
using System;
using FlexCharge.Contacts;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace FlexCharge.Contacts.Migrations
{
    [DbContext(typeof(PostgreSQLDbContext))]
    partial class PostgreSQLDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "7.0.1")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("FlexCharge.Contacts.Entities.Contact", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Currency")
                        .HasColumnType("text");

                    b.Property<string>("DriverLicenseCountry")
                        .HasColumnType("text");

                    b.Property<DateTime?>("DriverLicenseExpirationDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DriverLicenseNumber")
                        .HasColumnType("text");

                    b.Property<string>("DriverLicenseState")
                        .HasColumnType("text");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<string>("FirstName")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool?>("IsExternal")
                        .HasColumnType("boolean");

                    b.Property<string>("LastName")
                        .HasColumnType("text");

                    b.Property<string>("Meta")
                        .HasColumnType("jsonb");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("PassportCountry")
                        .HasColumnType("text");

                    b.Property<DateTime?>("PassportExpirationDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("PassportNumber")
                        .HasColumnType("text");

                    b.Property<string>("PatronDescription")
                        .HasColumnType("text");

                    b.Property<string>("PatronId")
                        .HasColumnType("text");

                    b.Property<string>("PatronLanguage")
                        .HasColumnType("text");

                    b.Property<string>("PatronStatus")
                        .HasColumnType("text");

                    b.Property<string>("PatronType")
                        .HasColumnType("text");

                    b.Property<string>("Phone")
                        .HasColumnType("text");

                    b.Property<string>("ProfileUrl")
                        .HasColumnType("text");

                    b.Property<string>("SecondaryEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.ToTable("Contacts");
                });

            modelBuilder.Entity("FlexCharge.Contacts.Entities.xContactAccounts", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("AccountId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("ContactId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<Guid>("MerchantId")
                        .HasColumnType("uuid");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("PartnerId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("ContactId");

                    b.ToTable("xContactAccounts");
                });

            modelBuilder.Entity("FlexCharge.Contacts.Entities.xContactAccounts", b =>
                {
                    b.HasOne("FlexCharge.Contacts.Entities.Contact", "Contact")
                        .WithMany("ContactAccounts")
                        .HasForeignKey("ContactId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Contact");
                });

            modelBuilder.Entity("FlexCharge.Contacts.Entities.Contact", b =>
                {
                    b.Navigation("ContactAccounts");
                });
#pragma warning restore 612, 618
        }
    }
}
