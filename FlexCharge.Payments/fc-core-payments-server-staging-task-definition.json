{"containerDefinitions": [{"name": "fc-core-payments-server", "image": "************.dkr.ecr.us-east-1.amazonaws.com/fc-core-server-payments:8f31fb9993e42e8ef513fd02312a27dc8cd8a701", "cpu": 0, "portMappings": [{"containerPort": 80, "hostPort": 80, "protocol": "tcp"}], "essential": true, "environment": [{"name": "DB_USERNAME", "value": "payments_service_staging"}, {"name": "DB_DATABASE", "value": "fc_payments"}, {"name": "DB_HOST", "value": "flexcharge-staging-2023-08-08.ctwfhnhdjewu.us-east-1.rds.amazonaws.com"}, {"name": "DB_PORT", "value": "5432"}, {"name": "ASPNETCORE_ENVIRONMENT", "value": "Staging"}, {"name": "AWS_COGNITO_USER_POOL_ID", "value": "us-east-1_rCUpTgXY4"}, {"name": "SNS_IAM_REGION", "value": "us-east-1"}, {"name": "TOKENEX_ID", "value": "****************"}, {"name": "OPEN_BANKING_ENCRYPTION_KMS_KEY_ID", "value": "arn:aws:kms:us-east-1:************:key/mrk-d2de2d873dde4dda85853ab693685a84"}, {"name": "KOUNT_API", "value": "https://api-sandbox.kount.com"}, {"name": "KOUNT_ISSUER", "value": "https://login.kount.com/oauth2/ausdppkujzCPQuIrY357"}, {"name": "MYRCVR_API", "value": "https://api.reports.myrcvr.com/data/received_alert"}, {"name": "REDSHIFT_CONNECTION_STRING", "value": "NO_Sandbox_Redshift"}], "mountPoints": [], "volumesFrom": [], "secrets": [{"name": "DB_PASSWORD", "valueFrom": "arn:aws:secretsmanager:us-east-1:************:secret:STG_DB_PAYMENTS_PASSWORD-9fWL4O"}, {"name": "AWS_IAM_COGNITO_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:************:secret:STG_AWS_IAM_COGNITO_KEY-SVc81A"}, {"name": "AWS_IAM_COGNITO_SECRET", "valueFrom": "arn:aws:secretsmanager:us-east-1:************:secret:STG_AWS_IAM_COGNITO_SECRET-7KOA9j"}, {"name": "AWS_IAM_COGNITO_CLIENT_ID", "valueFrom": "arn:aws:secretsmanager:us-east-1:************:secret:STG_AWS_IAM_COGNITO_CLIENT_ID-xZNZOA"}, {"name": "SNS_IAM_ACCESS_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:************:secret:STG_SNS_IAM_ACCESS_KEY-lZ8I9V"}, {"name": "SNS_IAM_SECRET_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:************:secret:STG_SNS_IAM_SECRET_KEY-YwuJZJ"}, {"name": "PLAID_CLIENT_ID", "valueFrom": "arn:aws:secretsmanager:us-east-1:************:secret:STAGING_PLAID_CLIENT_ID-3Ha319"}, {"name": "PLAID_CLIENT_SECRET", "valueFrom": "arn:aws:secretsmanager:us-east-1:************:secret:STAGING_PLAID_SECRET-RCJgrG"}, {"name": "SVB_ACH_CLIENT_ID", "valueFrom": "arn:aws:secretsmanager:us-east-1:************:secret:STAGING-SVB-ACH-CLIENT-ID-6pDddS"}, {"name": "SVB_ACH_CLIENT_SECRET", "valueFrom": "arn:aws:secretsmanager:us-east-1:************:secret:STAGING-SVB-ACH-CLIENT-SECRET-n7SQiv"}, {"name": "TOKENEX_API_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:************:secret:STAGING-TOKENEX-API-KEY-PKJxFY"}, {"name": "API_CLIENT_JWT_SIGNING_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:************:secret:STAGING-API-CLIENT-JWT-SIGNING-KEY-BQqxFQ"}, {"name": "STRIPE_APPS_STRIPE_API_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:************:secret:staging/integrations/stripe-apps/stripe-api-key-3K7wvY"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/fc-core-payments-server-staging", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs", "awslogs-create-group": "true"}}}], "family": "fc-core-payments-server-staging", "taskRoleArn": "arn:aws:iam::************:role/ecsTaskExecutionWithSecretAccess-Staging-Role", "executionRoleArn": "arn:aws:iam::************:role/ecsTaskExecutionWithSecretAccess-Staging-Role", "networkMode": "awsvpc", "volumes": [], "placementConstraints": [], "requiresCompatibilities": ["FARGATE"], "cpu": "512", "memory": "1024"}