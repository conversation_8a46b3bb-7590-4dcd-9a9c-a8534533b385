namespace FlexCharge.Payments.Services.SpreedlyService;

public class Spreedly3DSMerchantOptions
{
    public const string SectionKey = "spreadly3DSMerchantConfiguration";
    
    public string MerchantProfileDescription { get; set; }
    public string Type { get; set; }
    public bool Sandbox { get; set; }
    public Spreedly3DSCardSchemas CardSchemas { get; set; }

    public class Spreedly3DSCardSchemas
    {
        public CardSchemaOptions Visa { get; set; }
        public CardSchemaOptions Mastercard { get; set; }
        public CardSchemaOptions Amex { get; set; }



        public class CardSchemaOptions
        {
            public string AcquirerBin { get; set; }
            public string AcquirerMerchantId { get; set; }
            public string MCC { get; set; }
            public string MerchantName { get; set; }
            public string MerchantCountry { get; set; }
            public string MerchantUrl { get; set; }
            public string MerchantPassword { get; set; }
        }
    }
}