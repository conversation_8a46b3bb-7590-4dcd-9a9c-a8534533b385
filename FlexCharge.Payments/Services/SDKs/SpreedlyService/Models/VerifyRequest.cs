using System;
using System.Text.Json.Serialization;
using Newtonsoft.Json;

namespace FlexCharge.Payments.Services.SpreedlyService;

public class VerifyRequest : SpreedlyRequest
{
    [JsonProperty("transaction")] public VerifyTransaction Transaction { get; set; }
}

public class VerifyTransaction
{
    [JsonProperty("credit_card")] public CreditCard CreditCard { get; set; }
    
    [JsonProperty("payment_method_token")]
    public string PaymentMethodToken { get; set; }

    [JsonProperty("retain_on_success")]
    public bool RetainOnSuccess { get; set; }
    
    [JsonProperty("billing_address")]
    public BillingAddressDto BillingAddress { get; set; }

    //A boolean option to keep the cvv cached for a few minutes. Otherwise cvv is deleted immediately.
    [JsonProperty("continue_caching")] public bool ContinueCaching { get; set; }

    [JsonProperty("three_ds_version")]
    public string ThreeDsVersion { get; set; }

    [JsonProperty("three_ds")]
    public ThreeDs ThreeDs { get; set; }
}