using System;

namespace FlexCharge.Payments.Services.SDKs.TokenExService.Models.ThreeDSecure.Authentication;

public record AuthenticationRequest(
    int MethodCompletionIndicator,
    string MessageVersion,
    BrowserInfo BrowserInfo,
    string AcquirerBin,
    CardholderDetails CardholderDetails,
    CardDetails CardDetails,
    int ChallengeWindowSize,
    int DeviceChannel,
    string DirectoryServerIdentifier,
    bool GenerateChallengeRequest,
    MerchantDetails MerchantDetails,
    int MessageCategory,
    string NotificationUrl,
    int? AuthenticationIndicator,
    PurchaseDetails PurchaseDetails,
    int TransactionType
);

public record BrowserInfo(
    string AcceptHeaders,
    string IpAddress,
    bool JavascriptEnabled,
    string Language,
    string ColorDepth,
    string ScreenHeight,
    string ScreenWidth,
    string TimeZone,
    string UserAgent
);

public record CardholderDetails(
    string Name,
    string EmailAddress
);

public record CardDetails(
    string Number,
    string CardExpiryDate,
    int? AccountType
);

public record MerchantDetails(
    string AcquirerMerchantId,
    string CategoryCode,
    string CountryCode,
    string Name
);

public record PurchaseDetails(
    //Purchase amount in minor units of Currency with all punctuation removed.
    int Amount,
    //3-digit ISO 4217 Currency code string, in which purchase amount is expressed.
    string Currency,
    //Minor units of Currency as specified in the ISO 4217 Currency exponent. Example: USD = 2, Yen = 0
    int Exponent,
    //Date and time of the purchase, expressed in UTC.
    string Date
)
{
    //Date after which no further authorizations shall be performed. Format: YYYYMMDD
    //V1 Conditions: Required if 3DS Requestor Authentication Indicator= 02 or 03.
    //V2 Conditions: Required if 3DS Requestor Authentication Indicator= 02 or 03 OR 3RI Indicator = 01 or 02
    //Visa: Field is required if available
    public string? RecurringExpiry { get; set; }

    //Indicates the minimum number of days between authorizations.
    //V1 Conditions: Required if 3DS Requestor Authentication Indicator= 02 or 03.
    //V2 Conditions: Required if 3DS Requestor Authentication Indicator= 02 or 03 OR 3RI Indicator = 01 or 02
    //Visa: Field is required if available
    public int? RecurringFrequency { get; set; }

    private enum CurrencyISO4217Code
    {
        USD = 840,
    }

    public static PurchaseDetails CreateSinglePurchase(int amount, string currency, DateTime purchaseTime)
    {
        var currencyCode = currency.ToUpper() switch
        {
            "USD" => CurrencyISO4217Code.USD,
            _ => throw new NotImplementedException()
        };

        return new PurchaseDetails(
            Amount: amount,
            Currency: ((int)currencyCode).ToString(),
            Exponent: 2,
            Date: purchaseTime.ToUniversalTime().ToString("yyyyMMddhhmmss"));
    }

    public static PurchaseDetails CreateRecurringTransaction(int amount, string currency, DateTime purchaseTime,
        DateTime recurringExpiry, int recurringFrequency)
    {
        var purchaseDetails = CreateSinglePurchase(amount, currency, purchaseTime);
        purchaseDetails.RecurringExpiry = recurringExpiry.ToString("yyyyMMdd");
        purchaseDetails.RecurringFrequency = recurringFrequency;

        return purchaseDetails;
    }
};