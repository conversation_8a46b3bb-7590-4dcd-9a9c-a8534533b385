namespace FlexCharge.Payments.Services.TokenExService.Models.ThreeDSecure;

public enum MessageCategory
{
    /// <summary>
    /// Payment Authentication (PA)
    /// </summary>
    PaymentAuthentication = 1,

    /// <summary>
    /// Non-Payment Authentication (NPA)
    /// </summary>
    NonPaymentAuthentication = 2,

    /// <summary>
    /// Identity Check Insights (without authentication)
    /// </summary>
    /// <remarks>See: https://docs.3dsecure.io/3dsv2/specification_210.html</remarks>
    MastercardIdentityCheckInsights = 80
}

/// <summary>
/// The Authentication Indicator goes further than the Message Category
/// to specify what type of authentication is being requested
/// </summary>
/// <remarks>See: https://docs.3dsecure.io/3dsv2/specification_210.html</remarks>
public enum RequestorAuthenticationIndicator
{
    PaymentTransaction = 1,
    RecurringTransaction = 2,
    InstalmentTransaction = 3,
    AddCard = 4,
    MaintainCard = 5,
    CardholderVerificationAsPartOfEmvTokenIdandV = 6,
    //80-99 	Reserved for DS use
}

public enum TransactionType
{
    GoodsServicePurchase = 1,
    CheckAcceptance = 3,
    AccountFunding = 10,
    CashTransaction = 11,
    PrepaidActivationAndLoad = 28
}

public enum DeviceChannel
{
    /// <summary>
    /// Application
    /// </summary>
    App = 1,

    /// <summary>
    /// Browser
    /// </summary>
    Browser = 2,

    /// <summary>
    /// 3DS Requestor Initiated (3RI, merchant-initiated)
    /// </summary>
    MerchantInitiated3RI = 3
}

/// <summary>
/// Determines whether the 3DS Method was successfully completed, unsuccessfully completed, or unavailable.
/// </summary>
/// <remarks>
/// See: https://docs.tokenex.com/docs/3-d-secure-device-fingerprinting
/// </remarks>
public enum MethodCompletionIndicator
{
    ThreeDSMethodSuccessfullyCompleted = 1,
    ThreeDSMethodUnSuccessful = 2,
    ResultUnavailable = 3
}

public enum ChallengeWindowSize
{
    Window_250x400 = 1,
    Window_390x400 = 2,
    Window_500x600 = 3,
    Window_600x400 = 4,
    FullScreen = 5
}