using FlexCharge.Payments.Services.PaymentServices.ResponseCodeMapping;

namespace FlexCharge.Payments.Services.PaymentServices.Providers.Dummy;

public class DummyProviderResponseMap : ProviderResponseMapBase
{
    protected override string MapName => "Dummy";

    public override MappingType MappingType => MappingType.ResponseCodeAndMessage | MappingType.ResponseCode;

    public override bool IsApprovedResponse(ProviderResponse providerResponse)
    {
        return providerResponse.ResponseCode == "succeeded";
    }
}