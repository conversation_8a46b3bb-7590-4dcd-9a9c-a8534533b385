namespace FlexCharge.Payments.Services.PaymentServices.Providers.Adyen;

public enum AdyenPaymentStatus
{
    /// <summary>
    ///  This is the initial state for all transactions and corresponds to the registration of a validated payment attempt. The payment is Received when an Offer is converted to a payment. 
    /// </summary>
    Received,
    Authorized,
    SentFor<PERSON>ettle,
    SettleScheduled,
    Settled,
    SentForRefund,
    RefundScheduled,
    Refunded,
    Refused,
    Error,
    Expired,
    Canceled,
    AuthorizedPending,
    SettledBulk,
    RefundedBulk
}


// Received: This is the initial state for all transactions and corresponds to the registration of a validated payment attempt. The payment is Received when an Offer is converted to a payment. 
// Authorized: The payment is approved by the financial institution. This status indicates that the delivery of goods and services can proceed. For POS payments, this corresponds with an approved transaction from the cash register and when the decision is made to hand over goods. You can find information on the Authorized Pending status here.
// SentForSettle: The request for transferring the funds has been sent to the financial institution. For some payment methods, the payment must be captured before changing to SentForSettle. Once the SentForSettle status is reached, the transaction can no longer be canceled and should be refunded instead.
// SettleScheduled: The net transaction amount will be paid out to you with a delay according to your SalesDayPayout schedule. This status applies to Sales Day Payout transactions only and appears in Reports as Settled. 
// Settled: The financial institution has transferred the funds to Adyen. 
// SentForRefund: Here, the request to refund the payment was sent to the financial institution. A payment can only be refunded after it has reached the status SentForSettle. Refund requests cannot be reversed. 
// RefundScheduled: The net transaction amount of the refund will be debited on your behalf with a delay according to your Sales Day Payout schedule. This status applies to Sales Day Payout transactions only and appears in Reports as Refunded.
// Refunded: This means that the financial institution has completed the reimbursement to the shopper. 
// Note: The statuses RefundedAcquirer and RefundedBulk also mean that your transaction is refunded. 
// Refused: A transaction is refused when it was declined by the financial institution or if the fraud score exceeds 99 points. This state is final. 
// Error: A transaction changes to the error status when the payment attempt was validated and received correctly but an error occurred while communicating with the financial institution. This state is final. 
// Expired: A payment automatically sets to Expired when the authorization stays open for more than 4 weeks, for example if it has not been canceled or captured. This is a final state. 
// Canceled: A cancellation blocks funds transfer for an authorized payment. It is possible to cancel a payment only until it reaches the SentForSettle state. This state is final.
// AuthorizedPending: This status only applies to POS transactions. The issuer approved the transaction online but we have not received confirmation from the payment terminal that the transaction is completed. From this status, the payment can go to the Authorized status or the Canceled status.
// SettledBulk: This status applies for Visa and Mastercard transactions only. It equals SentForSettle or Settled statuses and is displayed in the Customer Area and the Daily & Monthly Finance Report. 
// RefundedBulk: This status applies for Visa and Mastercard transactions only. It equals SentForRefund or Refunded statuses and is displayed in the Customer Area and the Daily & Monthly Finance Report. 
// Was this article helpful?