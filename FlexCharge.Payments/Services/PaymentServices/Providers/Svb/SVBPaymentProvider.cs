using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Shared.Payments;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.CardBrands;
using FlexCharge.Payments.Services.PaymentServices.Interfaces;
using FlexCharge.Payments.Services.PaymentServices.Models;
using FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels;
using FlexCharge.Payments.Services.SVBAchServices;
using FlexCharge.Payments.Services.SVBAchServices.Models;
using Newtonsoft.Json;
using PaymentMethodType = FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels.PaymentMethodType;

namespace FlexCharge.Payments.Services.PaymentServices.Providers.Svb;

public class SVBPaymentProvider : PaymentProviderBase
{
    private ISVBService _achService;

    public SVBPaymentProvider(ISVBService achService)
    {
        _achService = achService;
    }

    public override async Task<AchTransferResult> AchCreditAsync(IAchTransferRequest payload,
        CancellationToken token = default)
    {
        using var workspan = Workspan.Start<SVBPaymentProvider>()
            .LogEnterAndExit();

        try
        {
            var transferResponse = await ExecuteAchCredit(payload, token);

            workspan
                .Tag("TransferResponse", transferResponse);

            var response = ProcessAchTransferResponse(transferResponse);

            workspan
                .Response(transferResponse);

            return response;
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);

            var achTransferResponse = new AchTransferResult();

            achTransferResponse.RawResult = JsonConvert.SerializeObject(e);
            achTransferResponse.AddError(e.Message);

            return achTransferResponse;
        }
    }


    public override async Task<AchTransferResult> AchDebitAsync(IAchTransferRequest payload,
        CancellationToken token = default)
    {
        using var workspan = Workspan.Start<SVBPaymentProvider>()
            .Baggage("AchTransferRequest", payload)
            .LogEnterAndExit();

        try
        {
            var transferResponse = await ExecuteAchDebit(payload, token);
            var response = ProcessAchTransferResponse(transferResponse);

            workspan.Response(response).Log.Information("Flex AchDebit response");

            if (response.Success)
            {
                DomesticAch transferStatus;
                try
                {
                    // We need to query the ACH transfer to get the real processing status
                    transferStatus = await _achService.QueryTransferAsync(response.ProviderTransactionToken,
                        null, token);

                    workspan.Tag("TransferStatus", transferStatus);

                    if (!transferStatus.IsAchTransactionInProcessOrSucceeded())
                    {
                        workspan.Log.Fatal("ACH transfer is not in processing state after AchDebit");
                        // TODO: Throw exception here once we know what we are receiving in production 
                        // throw new FlexChargeException($"ACH transfer is not in processing state. Status: {transferStatus?.Status}");
                    }
                }
                catch (Exception e)
                {
                    workspan.RecordFatalException(e,
                        "ProcessAchTransfer returned Success, but Failed to query ACH transfer. check ACH transfer");
                }
            }

            return response;
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);

            var achTransferResponse = new AchTransferResult();

            achTransferResponse.RawResult = JsonConvert.SerializeObject(e);
            achTransferResponse.AddError(e.Message);

            return achTransferResponse;
        }
    }

    public override async Task<AchCancelResult> AchCancelAsync(IAchCancelPaymentRequest payload,
        CancellationToken token = default)
    {
        using var workspan = Workspan.Start<SVBPaymentProvider>()
            .LogEnterAndExit();

        try
        {
            var transferResponse = await ExecuteAchCancel(payload, token);

            workspan
                .Response(transferResponse);

            var response = ProcessAchCancelResponse(transferResponse);

            workspan
                .Response(response);

            #region Commented

            // if (response.Success)
            // {
            //     DomesticAch transferStatus;
            //     try
            //     {
            //         // We need to query the ACH transfer to get the real processing status
            //         transferStatus = await _achService.QueryTransferAsync(response.ProviderTransactionToken,
            //             null, payload.Mid, token);
            //     }
            //     catch (Exception e)
            //     {
            //         throw new FlexChargeException(e, "Failed to query ACH transfer");
            //     }
            //
            //     if (!transferStatus.IsCanceled())
            //     {
            //         workspan.Log.Fatal("ACH transfer is not in processing state after AchCancel. Status: {Status}",
            //             transferStatus?.Status);
            //         // TODO: Throw exception here once we know what we are receiving in production 
            //         // throw new FlexChargeException($"ACH transfer is not in processing state. Status: {transferStatus?.Status}");
            //     }
            // }

            #endregion

            return response;
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);

            var achCancelResult = new AchCancelResult();

            achCancelResult.RawResult = JsonConvert.SerializeObject(e);
            achCancelResult.AddError(e.Message);

            return achCancelResult;
        }
    }

    public async Task<PaymentStatusResult> GetPaymentStatusAsync(Guid mid, string paymentId,
        PaymentMethodType methodType,
        string provider, CancellationToken token = default)
    {
        using var workspan = Workspan.Start<SVBPaymentProvider>()
            .Baggage("Mid", mid)
            .Baggage("PaymentId", paymentId)
            .Baggage("PaymentMethodType", methodType)
            .Baggage("Provider", provider)
            .LogEnterAndExit();

        var paymentStatusResults = new PaymentStatusResult();

        switch (methodType)
        {
            case PaymentMethodType.CreditCard:
            case PaymentMethodType.DebitCard:

                break;
            case PaymentMethodType.Echeck:
            case PaymentMethodType.Ach:

                // if (!Guid.TryParse(paymentId, out Guid guidPaymentId))
                // {
                //     workspan.Log.Error("Invalid payment id: {Id}", paymentId);
                //     break;
                // }

                var res = await _achService.QueryTransferAsync(paymentId, null, token);

                workspan
                    .Response(res);

                if (res != null)
                    paymentStatusResults.Results.Add(new KeyValuePair<string, string>(paymentId, res.Status));

                break;
            case PaymentMethodType.Check:
            default:
                throw new ArgumentOutOfRangeException(nameof(methodType), methodType, null);
        }

        return paymentStatusResults;
    }

    public async Task<PaymentStatusResult> GetPaymentsStatusAsync(Guid mid, string[] paymentIds,
        PaymentMethodType methodType,
        string provider, CancellationToken token = default)
    {
        using var workspan = Workspan.Start<SVBPaymentProvider>()
            .Baggage("Mid", mid)
            .Baggage("PaymentIds", paymentIds)
            .Baggage("PaymentMethodType", methodType)
            .Baggage("Provider", provider)
            .LogEnterAndExit();

        var paymentStatusResults = new PaymentStatusResult();

        switch (methodType)
        {
            case PaymentMethodType.CreditCard:
            case PaymentMethodType.DebitCard:

                break;
            case PaymentMethodType.Echeck:
            case PaymentMethodType.Ach:
                foreach (var paymentId in paymentIds)
                {
                    // if (!Guid.TryParse(id, out Guid guidId))
                    // {
                    //     workspan.Log.Error("Invalid payment id: {id}", id);
                    //     continue;
                    // }

                    var res = await _achService.QueryTransferAsync(paymentId, null, token);
                    paymentStatusResults.Results.Add(new KeyValuePair<string, string>(paymentId, res.ReturnCode));

                    workspan
                        .Response(res);
                }

                break;
            case PaymentMethodType.Check:
            default:
                throw new ArgumentOutOfRangeException(nameof(methodType), methodType, null);
        }

        return paymentStatusResults;
    }

    #region Commented

    // private async Task<Transaction> InitiateAchTransaction(Merchant merchant, IAchRequest payload,
    //     TransactionType type,
    //     CancellationToken token)
    // {
    //     var trx = new Transaction
    //     {
    //         ParentId = default,
    //         DynamicDescriptor = payload.CompanyEntryDescription,
    //         Amount = payload.Amount,
    //         Currency = payload.Currency,
    //         Type = type.ToString(),
    //         Status = TransactionStatus.Initialized,
    //         PaymentType = PaymentMethodType.Ach.ToString(),
    //         ProviderName = payload.Provider,
    //         ProviderId = payload.ProviderId,
    //         ProcessorName = payload.Processor,
    //         PayerId = payload.Sender?.Id ?? Guid.Empty,
    //         Merchant = merchant,
    //         OrderId = payload.OrderId,
    //         IsRecurring = false
    //     };
    //
    //     _context.Transactions.Add(trx);
    //     await _context.SaveChangesAsync(token);
    //
    //     return trx;
    // }

    #endregion

    private async Task<TransferResponse> ExecuteAchDebit(IAchTransferRequest payload,
        CancellationToken token)
    {
        using var workspan = Workspan.Start<SVBPaymentProvider>()
            .LogEnterAndExit();

        try
        {
            if (payload.IsVerifiedAch)
            {
                #region Verified ACH Debit

                var response = await _achService.VerifiedDebitAsync(new VerifiedTransferRequest
                {
                    BatchDetails = new BatchDetails
                    {
                        SvbAccountNumber = payload.Receiver.AccountNumber,
                        SecCode = payload.SecType.ToString(),
                        SettlementPriority = SettlementPriorityEnum.STANDARD.ToString(),
                        CompanyEntryDescription = payload.CompanyEntryDescription,
                        EffectiveEntryDate = payload.EffectiveEntryDate,
                        Currency = payload.Currency
                    },
                    Transfers = new List<VerifiedTransfer>()
                    {
                        new()
                        {
                            Amount = payload.Amount,
                            IdentificationNumber = payload.IdentificationNumber,
                            CounterpartyName = payload.Sender.Name,
                            ProviderService = payload.Provider,
                            ProviderId = payload.Sender.ProcessorToken
                        }
                    }
                }, token);

                workspan
                    .Response(response);

                return response;

                #endregion
            }
            else
            {
                #region ACH Debit

                var response = await _achService.DebitAsync(new TransferRequest()
                {
                    BatchDetails = new BatchDetails
                    {
                        SvbAccountNumber = payload.Receiver.AccountNumber,
                        //Direction = DirectionEnum.CREDIT.ToString(),
                        SecCode = payload.SecType.ToString(),
                        SettlementPriority = SettlementPriorityEnum.STANDARD.ToString(),
                        CompanyEntryDescription = payload.CompanyEntryDescription,
                        EffectiveEntryDate = payload.EffectiveEntryDate,
                        Currency = payload.Currency
                    },
                    Transfers = new List<Transfer>()
                    {
                        new()
                        {
                            Amount = payload.Amount,
                            IdentificationNumber = payload.IdentificationNumber,
                            // For Debit, the sender is the receiver!!!
                            ReceiverAccountNumber = payload.Sender.AccountNumber,
                            ReceiverRoutingNumber = payload.Sender.RoutingNumber,
                            ReceiverAccountType = payload.Sender.AccountType.ToString().ToUpper(),
                            ReceiverName = payload.Sender.Name
                        }
                    }
                }, token);

                workspan
                    .Response(response);

                return response;

                #endregion
            }
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);
            throw;
        }
    }

    private async Task<TransferCancelResponse> ExecuteAchCancel(IAchCancelPaymentRequest payload,
        CancellationToken token)
    {
        using var workspan = Workspan.Start<SVBPaymentProvider>()
            .LogEnterAndExit();

        try
        {
            if (payload.IsVerifiedAch)
            {
                #region Verified ACH Cancel

                var response = await _achService.VerifiedCancelAsync(payload.ProviderTransactionToken, token);

                workspan
                    .Response(response);

                return response;

                #endregion
            }
            else
            {
                #region ACH Cancel

                var response = await _achService.CancelAsync(payload.ProviderTransactionToken, token);

                workspan
                    .Response(response);

                return response;

                #endregion
            }
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);
            throw;
        }
    }

    private async Task<TransferResponse> ExecuteAchCredit(IAchTransferRequest payload,
        CancellationToken token)
    {
        using var workspan = Workspan.Start<SVBPaymentProvider>()
            .LogEnterAndExit();

        try
        {
            if (payload.IsVerifiedAch)
            {
                #region Verified ACH Credit

                var response = await _achService.VerifiedCreditAsync(new VerifiedTransferRequest
                {
                    BatchDetails = new BatchDetails
                    {
                        SvbAccountNumber = payload.Sender.AccountNumber,
                        SecCode = payload.SecType.ToString(),
                        SettlementPriority = SettlementPriorityEnum.STANDARD.ToString(),
                        CompanyEntryDescription = payload.CompanyEntryDescription,
                        EffectiveEntryDate = payload.EffectiveEntryDate,
                        Currency = payload.Currency
                    },
                    Transfers = new List<VerifiedTransfer>()
                    {
                        new()
                        {
                            Amount = payload.Amount,
                            IdentificationNumber = payload.IdentificationNumber,
                            CounterpartyName = payload.Receiver.Name,
                            ProviderService = payload.Provider,
                            ProviderId = payload.Receiver.ProcessorToken
                        }
                    }
                }, token);

                workspan
                    .Response(response);

                return response;

                #endregion
            }
            else
            {
                #region ACH Credit

                var response = await _achService.CreditAsync(new TransferRequest()
                {
                    BatchDetails = new BatchDetails
                    {
                        SvbAccountNumber = payload.Sender.AccountNumber,
                        //Direction = DirectionEnum.CREDIT.ToString(),
                        SecCode = SecCodeEnum.PPD.ToString(),
                        SettlementPriority = SettlementPriorityEnum.STANDARD.ToString(),
                        CompanyEntryDescription = payload.CompanyEntryDescription,
                        EffectiveEntryDate = payload.EffectiveEntryDate,
                        Currency = payload.Currency
                    },
                    Transfers = new List<Transfer>()
                    {
                        new()
                        {
                            Amount = payload.Amount,
                            IdentificationNumber = payload.IdentificationNumber,
                            ReceiverAccountNumber = payload.Receiver.AccountNumber,
                            ReceiverRoutingNumber = payload.Receiver.RoutingNumber,
                            ReceiverAccountType = payload.Receiver.AccountType.ToString().ToUpper(),
                            ReceiverName = payload.Receiver.Name
                        }
                    }
                }, token);

                workspan
                    .Response(response);

                return response;

                #endregion
            }
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);
            throw;
        }
    }

    private AchTransferResult ProcessAchTransferResponse(TransferResponse transferResponse)
    {
        using var workspan = Workspan.Start<SVBPaymentProvider>()
            .Tag("TransferResponse", transferResponse)
            //.Tag("Transaction", transaction)
            .LogEnterAndExit();

        var response = new AchTransferResult();

        try
        {
            var achTransferResults = transferResponse?.Results;
            if (achTransferResults?[0].ValidationStatus?.Trim().ToUpper() == "SUCCESS")
            {
                response.ProviderResponseCode = "100";
                response.ProviderResponseMessage = achTransferResults?[0].ValidationStatus;
                response.ProviderTransactionToken = achTransferResults[0].Id;
            }
            else
            {
                workspan.Log.Fatal("AchTransfer error");

                if (transferResponse == null)
                {
                    workspan.Log
                        .Fatal("AchTransfer error: AchTransferResponse is NULL");

                    response.ProviderResponseCode = null;
                    response.ProviderResponseMessage = "AchTransferResponse is NULL";

                    response.AddErrorWithCode(response.ProviderResponseCode, response.ProviderResponseMessage);
                }
                else if (achTransferResults?.Any() == true &&
                         achTransferResults[0].Errors?.Any() == true)
                {
                    workspan
                        .Tag("ValidationStatus", achTransferResults[0].ValidationStatus);

                    workspan.Log
                        .Warning("AchTransfer failed");


                    response.ProviderResponseCode =
                        achTransferResults[0].Errors[0].Name ?? achTransferResults[0].Errors[0].Message;
                    response.ProviderResponseMessage = achTransferResults[0].Errors[0].Message ??
                                                       achTransferResults[0].Errors[0].Name;

                    response.AddErrorWithCode(response.ProviderResponseCode, response.ProviderResponseMessage);
                }
                else if (transferResponse?.Errors?.Any() == true)
                {
                    workspan.Log
                        .Fatal("AchTransfer: transfer error");

                    response.ProviderResponseCode =
                        transferResponse.Errors[0].Name ?? transferResponse.Errors[0].Message;
                    response.ProviderResponseMessage =
                        transferResponse.Errors[0].Message ?? transferResponse.Errors[0].Name;

                    response.AddErrorWithCode(response.ProviderResponseCode, response.ProviderResponseMessage);
                }
                else
                {
                    workspan.Log
                        .Fatal("AchTransfer error");

                    if (!string.IsNullOrWhiteSpace(transferResponse.Name) ||
                        !string.IsNullOrWhiteSpace(transferResponse.Message))
                    {
                        response.ProviderResponseCode = transferResponse.Name ?? transferResponse.Message;
                        response.ProviderResponseMessage = transferResponse.Message ?? transferResponse.Name;

                        response.AddErrorWithCode(response.ProviderResponseCode, response.ProviderResponseMessage);
                    }
                    else
                    {
                        workspan.Log
                            .Fatal("AchTransfer: unknown error");

                        response.ProviderResponseCode = null;
                        response.ProviderResponseMessage = "Unknown error";

                        response.AddErrorWithCode(response.ProviderResponseCode, response.ProviderResponseMessage);
                    }
                }
            }

            //TODO: What to do on 500 or timeout??? Get ACH status with retries????

            //Its the same but once gateway is deprecated we can remove this in this case SVB is the gateway
            //achTransferResponse.Gateway = achTransferResponse.Processor = transaction.ProcessorName;
            //achTransferResponse.Provider = transaction.ProviderName;
            //achTransferResponse.TransactionId = transaction.Id;
            //achTransferResponse.PaymentInstrumentId = transaction.PaymentMethodId ?? Guid.Empty;
            //achTransferResponse.Message = JsonConvert.SerializeObject(transferResponse);

            response.RawResult = JsonConvert.SerializeObject(transferResponse);
            response.Provider = CurrentPaymentProvider;

            if (response.ProviderResponseCode != null)
            {
                var internalResponse = SvbResponseMapper.GetMappedResponse(response.ProviderResponseCode);
                response.InternalResponseCode = internalResponse?.MappedResponseCode;
                response.InternalResponseMessage = internalResponse?.MappedResponseMessage;
                response.InternalResponseGroup = internalResponse?.MappedResponseGroup.ToString();
            }

            //transaction.Meta = System.Text.Json.JsonSerializer.SerializeToDocument(transferResponse);
        }
        catch (Exception e)
        {
            response.RawResult = JsonConvert.SerializeObject(e);
            workspan.RecordFatalException(e);
            response.AddError(e.Message);
        }

        return response;
    }

    private AchCancelResult ProcessAchCancelResponse(TransferCancelResponse cancelResponse)
    {
        using var workspan = Workspan.Start<SVBPaymentProvider>()
            .Tag("TransferResponse", cancelResponse)
            //.Tag("Transaction", transaction)
            .LogEnterAndExit();

        var response = new AchCancelResult();

        try
        {
            if (cancelResponse?.status == nameof(AchTransferStatus.CANCELED))
            {
                response.ProviderResponseCode = "100";
                response.ProviderResponseMessage = cancelResponse.status;
                response.ProviderTransactionToken = cancelResponse.Id;
            }
            else
            {
                workspan.Log.Fatal("AchCancel error");

                if (cancelResponse == null)
                {
                    workspan.Log
                        .Fatal("AchTransfer error: AchCancelResponse is NULL");

                    response.ProviderResponseCode = null;
                    response.ProviderResponseMessage = "AchCancelResponse is NULL";

                    response.AddErrorWithCode(response.ProviderResponseCode, response.ProviderResponseMessage);
                }
                else if (cancelResponse?.Errors?.Any() == true)
                {
                    workspan.Log
                        .Fatal("AchCancel: cancel error");

                    response.ProviderResponseCode =
                        cancelResponse.Errors[0].Name ?? cancelResponse.Errors[0].Message;
                    response.ProviderResponseMessage =
                        cancelResponse.Errors[0].Message ?? cancelResponse.Errors[0].Name;

                    response.AddErrorWithCode(response.ProviderResponseCode, response.ProviderResponseMessage);
                }
                else
                {
                    workspan.Log
                        .Fatal("AchCancel error");

                    if (!string.IsNullOrWhiteSpace(cancelResponse.Name) ||
                        !string.IsNullOrWhiteSpace(cancelResponse.Message))
                    {
                        response.ProviderResponseCode = cancelResponse.Name ?? cancelResponse.Message;
                        response.ProviderResponseMessage = cancelResponse.Message ?? cancelResponse.Name;

                        response.AddErrorWithCode(response.ProviderResponseCode, response.ProviderResponseMessage);
                    }
                    else
                    {
                        workspan.Log
                            .Fatal("AchCancel: unknown error");

                        response.ProviderResponseCode = null;
                        response.ProviderResponseMessage = "Unknown error";

                        response.AddErrorWithCode(response.ProviderResponseCode, response.ProviderResponseMessage);
                    }
                }
            }


            //TODO: What to do on 500 or timeout??? Get ACH status with retries????

            response.RawResult = JsonConvert.SerializeObject(cancelResponse);
            response.Provider = CurrentPaymentProvider;

            if (response.ProviderResponseCode != null)
            {
                var internalResponse = SvbResponseMapper.GetMappedResponse(response.ProviderResponseCode);
                response.InternalResponseCode = internalResponse?.MappedResponseCode;
                response.InternalResponseMessage = internalResponse?.MappedResponseMessage;
                response.InternalResponseGroup = internalResponse?.MappedResponseGroup.ToString();
            }

            //transaction.Meta = System.Text.Json.JsonSerializer.SerializeToDocument(transferResponse);
        }
        catch (Exception e)
        {
            response.RawResult = JsonConvert.SerializeObject(e);
            workspan.RecordFatalException(e);
            response.AddError(e.Message);
        }

        return response;
    }

    //public bool IsSandbox { get; }
    public override bool SupportCapture => false;
    public override bool SupportPartiallyRefund => false;
    public override bool SupportRefund => false;
    public override bool SupportVoid => false;
    public override bool SupportTokenization => false;
    public override bool RequirePostProcessPayment => false;
    public override bool SupportPayouts => true;
    public override string CurrentPaymentProvider => GatewayTypesConstants.Svb;
    public override bool SupportsAch => true;
    public override bool SupportsCreditCards => false;
    public override bool SupportsCreditCardVerification => false;
    public override bool SupportsExternalThreeDS => false;
    public override bool SupportsStandaloneCredit => false;
    public override CardBrand[] NetworkTokenCardBrands => null;
    public override bool SupportsSubscription => false;
}