using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Amazon.KeyManagementService;
using Amazon.KeyManagementService.Model;
using FlexCharge.Common.Telemetry;
using FlexCharge.Payments.Services.PaymentServices.Models;
using Jose;
using Polly.CircuitBreaker;

namespace FlexCharge.Payments.Services.PaymentServices.Providers.Checkout;

public class CheckoutAccountUpdater
{
    private int keySize = 2048;


    private async Task<UpdatedCardDTO> GetUpdatedCardDTO(CheckoutUpdatedCardSource cardSource,
        string encryptedPrivateKey,
        Workspan workspan)
    {
        var newNumber = await DecryptPan(cardSource.EncryptedCardNumber, encryptedPrivateKey, workspan);
        UpdatedCardDTO result = new()
        {
            Year = cardSource.ExpiryYear,
            Month = cardSource.ExpiryMonth,
            EncodedCardNumber = newNumber
        };
        return result;
    }


    private static async Task<string> DecryptPan(string encryptedPan, string privateKey, Workspan workspan)
    {
        try
        {
            var kmsClient = new AmazonKeyManagementServiceClient();

            var ciphertext = new MemoryStream();
            var stringForDecryption = Convert.FromBase64String(privateKey);
            ciphertext.Write(stringForDecryption, 0, stringForDecryption.Length);
            var dRequest = new DecryptRequest()
            {
                KeyId = "arn:aws:kms:us-east-1:************:key/7475f409-6374-46ea-be65-7d1ed510e97c",
                CiphertextBlob = ciphertext
            };
            var plainText2 = (await kmsClient.DecryptAsync(dRequest)).Plaintext;
            //Convert.FromBase64String(base64String);
            var decryptedString = Encoding.UTF8.GetString(plainText2.ToArray()) ?? string.Empty;

            var rsa = RSA.Create(2048);
            var bytes = Convert.FromBase64String(decryptedString); //Encoding.UTF8.GetBytes(privateKey);
            rsa.ImportPkcs8PrivateKey(bytes.AsSpan(), out var _);
            var token = JWE.Decrypt(encryptedPan, rsa, JweAlgorithm.RSA_OAEP_256, JweEncryption.A256GCM);
            return token.Plaintext;
        }
        catch (Exception e)
        {
#if DEBUG
            if (e.Message.Contains("Platform"))
            {
                Console.WriteLine("To use RSA in .NET 6 on MAC, please, run command in terminal:");
                Console.WriteLine("sudo ln -s /opt/homebrew/lib/libssl.3.dylib /usr/local/lib/");
            }
#endif
            workspan.RecordException(e);

            return null;
        }

        return null;
    }

    public async Task<string> DecryptPrivateKey(string encodedPrivateKey,
        string arn = "arn:aws:kms:us-east-1:************:key/7475f409-6374-46ea-be65-7d1ed510e97c")
    {
        using var workspan = Workspan.Start<CheckoutAccountUpdater>().LogEnterAndExit();

        try
        {
            var decryptedString = string.Empty;
            var stringForDecryption = Convert.FromBase64String(encodedPrivateKey);
            var kmsClient = new AmazonKeyManagementServiceClient();

            using var ciphertext = new MemoryStream();
            ciphertext.Write(stringForDecryption, 0, stringForDecryption.Length);

            var decryptRequest = new DecryptRequest()
            {
                KeyId = arn,
                CiphertextBlob = ciphertext
            };
            var plainText = (await kmsClient.DecryptAsync(decryptRequest)).Plaintext;

            decryptedString = Encoding.UTF8.GetString(plainText.ToArray()) ?? string.Empty;
            return decryptedString;
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, "EXCEPTION: CHECKOUT ACCOUNT UPDATER ");
            //throw;
            return null;
        }

        workspan.Log.Information("EXIT: DecryptCC");
        return null;
    }

    public static CheckoutUpdatedCard GetCheckoutUpdatedCard(string responseBody, string encryptedPrivateKey,
        Workspan workspan)
    {
        CheckoutUpdatedCard updatedCard = null;
        try
        {
            var document = JsonDocument.Parse(responseBody);
            var root = document.RootElement;
            if (!root.TryGetProperty("source", out var src))
            {
                //throw new Exception("Checkout updater response source property is empty");
                return null;
            }

            var source = src.GetRawText();
            if (string.IsNullOrWhiteSpace(source))
            {
                return null;
            }

            workspan.Log.Information("Found CheckoutUpdatedCard");
            var updatedCardSource = JsonSerializer.Deserialize<CheckoutUpdatedCardSource>(source);
            if (updatedCardSource.EncryptedCardNumber == null && updatedCardSource.Bin is null)
            {
                return null;
            }

            updatedCard = new()
            {
                ExpiryYear = updatedCardSource.ExpiryYear,
                ExpiryMonth = updatedCardSource.ExpiryMonth,
                EncryptedCardNumber = updatedCardSource.EncryptedCardNumber,
                KmsKey = "arn:aws:kms:us-east-1:************:key/7475f409-6374-46ea-be65-7d1ed510e97c",
                EncryptedPrivateKey = encryptedPrivateKey,
            };
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            return null;
        }

        return updatedCard;
    }


    public static async Task<UpdatedCardDTO> GetUpdatedCard(string responseBody, string encryptedPrivateKey,
        Workspan workspan)
    {
        try
        {
            var document = JsonDocument.Parse(responseBody);
            var root = document.RootElement;
            var source = root.GetProperty("source").GetRawText();
            if (string.IsNullOrWhiteSpace(source))
            {
                return null;
            }

            var updatedCard = JsonSerializer.Deserialize<CheckoutUpdatedCardSource>(source);

            // if (updatedCard.Fingerprint == null) // todo-check it!  bug on checkout side, should not be null if card is updated
            // {
            //     return null;
            // }

            var newNumber = updatedCard.EncryptedCardNumber is null
                ? null
                : await DecryptPan(updatedCard.EncryptedCardNumber, encryptedPrivateKey, workspan);

            UpdatedCardDTO result = new()
            {
                Year = updatedCard.ExpiryYear,
                Month = updatedCard.ExpiryMonth,
                EncodedCardNumber = newNumber
            };
            return result;
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            return null;
        }
    }
}