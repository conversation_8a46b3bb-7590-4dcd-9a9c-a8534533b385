namespace FlexCharge.Payments.Services.PaymentServices.Providers.Checkout;

public class CheckoutRiskResponses
{
    /// <summary>
    /// The payment failed due to a security violation. If the issue persists please contact us.
    /// </summary>
    private const string Risk_blocked_transaction = "40101";

    /// <summary>
    /// Gateway reject - IP address blacklist
    /// </summary>
    private const string Gateway_reject_card_number_blacklist = "40202";

    private const string Gateway_reject_email_blacklist = "40203";
    private const string Gateway_reject_phone_number_blacklist = "40204";

    private const string Gateway_Reject_BIN_number_blacklist = "40205";
}

