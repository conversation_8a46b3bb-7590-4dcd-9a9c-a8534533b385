namespace FlexCharge.Payments.Services.PaymentServices.Providers.Nuvei;

public record DisputeModel
(
    int ClientId,
    string EventType,
    string EventDateUTC,
    string EventCorrelationId,
    Chargeback Chargeback,
    TransactionDetails TransactionDetails
);

public record Chargeback(
    string Date,
    string ChargebackStatusCategory,
    string Type,
    object Status,
    double Amount,
    string Currency,
    double ReportedAmount,
    string ReportedCurrency,
    string ISOCurrency,
    string ChargebackReason,
    string ReasonMessage,
    string DisputeDueDate,
    string ChargebackReasonCategory
);

public record TransactionDetails(
    long TransactionId,
    string TransactionDate,
    string ClientUniqueId,
    string AcquirerName,
    string MaskedCardNumber,
    string Arn
);


