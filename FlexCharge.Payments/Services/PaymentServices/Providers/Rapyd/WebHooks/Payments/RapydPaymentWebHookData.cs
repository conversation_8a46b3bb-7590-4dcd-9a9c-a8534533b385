using FlexCharge.Payments.Services.PaymentServices.Providers.Rapyd.Models;

namespace FlexCharge.Payments.Services.PaymentServices.Providers.Rapyd.WebHooks;

public class RapydPaymentWebHookData
{
    public RapydAddress address { get; set; }
    public double amount { get; set; }
    public string auth_code { get; set; }
    public string cancel_reason { get; set; }
    public bool captured { get; set; }
    public string complete_payment_url { get; set; }
    public string country_code { get; set; }
    public string created_at { get; set; }
    public string currency_code { get; set; }
    public string customer_token { get; set; }
    public string description { get; set; }
    public string destination_card { get; set; }
    public RapydDispute dispute { get; set; }
    public string error_code { get; set; }

    public string error_message { get; set; }
    public string error_payment_url { get; set; }
    public RapydEscrow escrow { get; set; }
    public int? escrow_release_days { get; set; }
    public string ewallet_id { get; set; }
    public RapydWallet[] ewallets { get; set; } //todo
    public int? expiration { get; set; }
    public string failure_code { get; set; }
    public string failure_message { get; set; }
    public RapydFixedSide? fixed_side { get; set; }
    public string flow_type { get; set; }
    public double? fx_rate { get; set; }
    public string group_payment { get; set; }
    public string id { get; set; }
    public InitiationType? initiation_type { get; set; }
    public RapydInstruction? instructions { get; set; } //todo
    public string invoice { get; set; }
    public bool is_partial { get; set; }
    public string merchant_reference_id { get; set; }
    public double? merchant_requested_amount { get; set; }
    public string merchant_requested_currency { get; set; }
    public object metadata { get; set; } //todo
    public string mid { get; set; }
    public RapydNextAction? next_action { get; set; } //TODO
    public string order { get; set; }
    public double? original_amount { get; set; }
    public RapydOutcome outcome { get; set; }
    public bool paid { get; set; }
    public int? paid_at { get; set; }
    public string payment_account_reference { get; set; }
    public RapydPaymentFees payment_fees { get; set; } //todo
    public string payment_method { get; set; }
    public RapydPaymentMethodData payment_method_data { get; set; } //todo
    public RapydPaymentMethodOptions payment_method_options { get; set; } //todo
    public string payment_method_type { get; set; }
    public RapydPaymentMethodTypeCategory? payment_method_type_category { get; set; }
    public string receipt_email { get; set; }
    public string receipt_number { get; set; }
    public string redirect_url { get; set; }
    public bool refunded { get; set; }
    public double? refunded_amount { get; set; }
    public RapydRefund[] refunds { get; set; } //todo
    public RapydRemitterInformation remitter_information { get; set; } //todo
    public string requested_currency { get; set; }
    public bool save_payment_method { get; set; }
    public string statement_descriptor { get; set; }
    public RapydPaymentStatus? status { get; set; } //todo
    public object textual_codes { get; set; } //todo
    public object visual_codes { get; set; } //todo
}