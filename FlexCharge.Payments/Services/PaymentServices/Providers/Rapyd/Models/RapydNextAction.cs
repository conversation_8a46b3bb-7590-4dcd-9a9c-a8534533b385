using System.Runtime.Serialization;
using System.Text.Json.Serialization;
using FlexCharge.Utils.JsonConverters;

namespace FlexCharge.Payments.Services.PaymentServices.Providers.Rapyd.Models;

[JsonConverter(typeof(JsonStringEnumConverterEx<RapydNextAction>))]
public enum RapydNextAction
{
    [EnumMember(Value = "3d_verification")]
    _3d_verification,
    pending_capture,
    pending_confirmation,
    not_applicable
}