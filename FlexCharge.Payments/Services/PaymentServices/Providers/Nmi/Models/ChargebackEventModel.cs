namespace FlexCharge.Payments.Services.PaymentServices;

using System;
using System.Collections.Generic;

public class ChargebackEvent
{
    public Guid EventId { get; set; }
    public string EventType { get; set; }
    public EventBody EventBody { get; set; }
}

public class EventBody
{
    public Mcht Merchant { get; set; }
    public Prccor Processor { get; set; }
    public int Count { get; set; }
    public decimal ChargebackAmount { get; set; }
    public List<Chargeback> Chargebacks { get; set; }

    public class Mcht
    {
        public string Id { get; set; }
        public string Name { get; set; }
    }

    public class Prccor
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Type { get; set; }
    }

    public class Chargeback
    {
        public string Id { get; set; }
        public string Date { get; set; }
        public string CustomerName { get; set; }
        public string CcNumber { get; set; }
        public decimal Amount { get; set; }
        public string Reason { get; set; }
    }
}