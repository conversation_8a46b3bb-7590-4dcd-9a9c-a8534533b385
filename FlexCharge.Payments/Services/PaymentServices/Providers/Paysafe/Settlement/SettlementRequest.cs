using FlexCharge.Payments.Services.PaymentServices.Providers.Paysafe.Authorization;
using FlexCharge.Payments.Services.PaymentServices.Providers.Paysafe.Authorization.Airline;
using FlexCharge.Payments.Services.PaymentServices.Providers.Paysafe.Authorization.Cruise;

namespace FlexCharge.Payments.Services.PaymentServices.Providers.Paysafe.Settlement;

public class SettlementRequest
{
    public string MerchantRefNum { get; set; }
    public int Amount { get; set; }

    /// <summary>
    /// This validates that this request is not a duplicate.
    /// A request is considered a duplicate if the merchantRefNum
    /// has already been used in a previous request within the past 90 days.
    /// </summary>
    public bool DupCheck { get; set; } = true;
    public SplitPay[] Splitpay{ get; set; }
    public AirlineTravelDetails AirlineTravelDetails { get; set; }
    public CruiselineTravelDetails CruiselineTravelDetails { get; set; }
    /// <summary>
    /// Transaction identifier that can be used to reconcile this transaction with the acquirer/processor.
    /// Note: Not all processing gateways support this parameter.
    /// Contact your account manager for more information
    /// </summary>
    public string GatewayReconciliationId { get; set; }
}