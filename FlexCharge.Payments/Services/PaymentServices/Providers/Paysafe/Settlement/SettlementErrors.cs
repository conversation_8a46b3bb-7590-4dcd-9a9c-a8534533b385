using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Paysafe.ErrorCodes
{
    public enum SettlementErrors
    {
        /// <summary>
        /// You have submitted an invalidly formatted 
        /// Authorization ID for this Settlement.
        /// </summary>
        AuthID_InvalidFormat = 3200,

        AuthID_NotFound = 3201,
        /// <summary>
        /// You have exceeded the maximum number of Settlements allowed.
        /// </summary>
        MaxSettmentsNumber_Allowed = 3202,
        AuthSettledOrCancelled = 3203,
        SettlmentAmountExceedsAuthorization = 3204,
        AuthExpired = 3205,
        /// <summary>
        /// 
        /// </summary>
        GatewayReject = 3206,
        IssuerRejectByPolicy = 3207
    }
}
