using FlexCharge.Payments.Services.PaymentServices.Providers.Paysafe.Settlement;

namespace FlexCharge.Payments.Services.PaymentServices.Providers.Paysafe.Webhooks;

public class WebhookRequest
{
    public string AccountId { get; set; }
    public string Id { get; set; }
    public string MerchantRefNum { get; set; }
    public int Amount { get; set; }
    public string Usage { get; set; }
    public string ExecutionMode { get; set; }
    public string CurrencyCode  { get; set; }
    public WebhooksEvents Type { get; set; }
    public SettlementStatus Status { get; set; }
    public string PaymentType { get; set; }
}

