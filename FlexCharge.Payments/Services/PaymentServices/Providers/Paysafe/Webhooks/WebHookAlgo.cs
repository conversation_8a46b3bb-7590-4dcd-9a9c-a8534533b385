using System;
using System.Security.Cryptography;
using System.Text;

namespace FlexCharge.Payments.Services.PaymentServices.Providers.Paysafe.Webhooks;

public static class WebHookAlgo
{
    public static bool IsValid(string key, string body, string signature)
    {
        // var digest = HMAC_SHA256 (hma<PERSON><PERSON><PERSON>, UTF 8 string containing the JSON webhook request body)
        // signature = base 64 (digest)
        using HMACSHA256 hmac = new HMACSHA256(Encoding.UTF8.GetBytes(key));

        var bodyBytes = Encoding.UTF8.GetBytes(body);
        var hash = hmac.ComputeHash(bodyBytes);
        var sig = Convert.ToBase64String(hash);

        return sig == signature;
    }
}