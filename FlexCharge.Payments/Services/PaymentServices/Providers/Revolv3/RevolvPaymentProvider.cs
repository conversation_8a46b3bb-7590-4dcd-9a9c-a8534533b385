using System;
using System.Linq;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.GeoServices;
using FlexCharge.Common.Shared.Payments;
using FlexCharge.Common.Telemetry;
using FlexCharge.Common.Validation.DataAnnotationValidation;
using FlexCharge.Common.Validation.FluentValidation;
using FlexCharge.Contracts.CardBrands;
using FlexCharge.Payments.Entities;
using FlexCharge.Payments.Services.PaymentServices.Interfaces;
using FlexCharge.Payments.Services.PaymentServices.Models;
using FlexCharge.Payments.Services.PaymentServices.PaymentErrorCodes;
using FlexCharge.Payments.Services.PaymentServices.PaymentErrorCodes.Tsys;
using FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels;
using FlexCharge.Payments.Services.PaymentServices.Providers.Revolv3.Model;
using FlexCharge.Utils;
using FlexCharge.Utils.JsonConverters;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Exception = System.Exception;


namespace FlexCharge.Payments.Services.PaymentServices.Providers.Revolv3;

public class RevolvPaymentProvider : PaymentProviderBase
{
    private readonly PostgreSQLDbContext _context;
    private Revolv3Sdk _sdk;
    private JsonSerializerOptions _jsonOptions;

    // private JsonSerializerSettings _jsonSerializeSettings = new()
    // {
    //     ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore,
    //     ContractResolver = new CamelCasePropertyNamesContractResolver(),
    //     Converters = new List<JsonConverter>() { new StringEnumConverter() }
    // };

    public RevolvPaymentProvider(
        PostgreSQLDbContext context,
        Revolv3Sdk sdk)
    {
        _context = context;
        _sdk = sdk;
        _jsonOptions = new()
        {
            ReferenceHandler = ReferenceHandler.IgnoreCycles,
            WriteIndented = true,
            PropertyNameCaseInsensitive = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };
        _jsonOptions.Converters.Add(new JsonStringEnumConverter());
        _jsonOptions.Converters.Add(new ExceptionConverter<Exception>());
    }

    private async Task<RevolvConfiguration> GetCredentialsAsync(bool isSandbox,
        Guid supportedGatewayId, CancellationToken token = default)
    {
        var gateway = await _context.SupportedGateways
            .Where(x => x.Id == supportedGatewayId && x.Sandbox == isSandbox)
            .FirstOrDefaultAsync(token);
        if (gateway == null)
            throw new Exception("Revolv3 gateway not found");


        var config = JsonSerializer.Deserialize<RevolvConfiguration>(gateway.Configuration, _jsonOptions);
        if (!DataAnnotationValidationHelper.TryValidate(config, out var errors))
        {
            throw new FlexValidationException("Validation for Revolv3 " +
                                              $"failed with errors: {JsonSerializer.Serialize(errors)}");
        }

        return config;
    }


    private Interfaces.IResult UpdateResult(
        RevolvPaymentResponseError response,
        Interfaces.IResult result,
        Guid orderId,
        RevolvProcessor processor = RevolvProcessor.worldpay)
    {
        var workspan = Workspan.Current;
        result.Status = TransactionStatus.Failed;
        result.ProviderResponseCode = response.errorCode;
        result.ProviderResponseMessage = response.message;
        result.AddErrorWithCode(response.errorCode, response.message);
        result.RawResult = JsonSerializer.Serialize(response, _jsonOptions);

        SetInternalCode(result, processor, response.message);


        workspan.Log.Information("RevolvPaymentProvider  for {orderId}  failed  with {ResponseSummary}", orderId,
            response.message);
        return result;
    }

    private Interfaces.IResult UpdateResult(
        (string json, string path) jsonError,
        Interfaces.IResult result,
        Guid orderId,
        RevolvProcessor processor = RevolvProcessor.worldpay)
    {
        var workspan = Workspan.Current;
        result.Status = TransactionStatus.Failed;
        result.ProviderResponseCode = "JsonParseError";
        result.ProviderResponseMessage = "JsonParseError";
        result.AddErrorWithCode("JsonParseError", jsonError.path);
        result.RawResult = jsonError.json;
        result.InternalResponseCode = "-1";

        workspan.Log.Information("RevolvPaymentProvider  for {orderId}  failed  with {ResponseSummary}", orderId,
            "JsonParseError");
        return result;
    }

    private Interfaces.IResult UpdateResult(
        Exception e,
        Interfaces.IResult result,
        Guid orderId,
        RevolvProcessor processor = RevolvProcessor.worldpay)
    {
        var workspan = Workspan.Current;
        result.RawResult = JsonSerializer.Serialize(e);
        result.Status = TransactionStatus.Failed;
        result.AddError(e.Message);
        if (e.Data.Count > 0)
        {
            foreach (var key in e.Data.Keys)
            {
                result.AddError(e.Data[key].ToString());
            }
        }

        result.InternalResponseCode = "-1";
        workspan.RecordException(e, "Revolv exception");
        return result;
    }

    private void SetInternalCode(
        Interfaces.IResult result,
        RevolvProcessor processor,
        string message)
    {
        var respCode = processor switch
        {
            RevolvProcessor.worldpay => WorldPayToIsoMapper.Map(message),
            RevolvProcessor.tsys => TsysMapper.Map(message),
            _ => throw new Exception("Unknown provider")
        };

        var internalResponse = InternalResponseMapper.GetMappedResponse(respCode);
        result.InternalResponseCode = respCode;
        result.InternalResponseMessage = internalResponse?.MappedResponseMessage;
        result.InternalResponseGroup = internalResponse?.MappedResponseGroup.ToString();
    }

    private Interfaces.IResult UpdateResult(
        RevolvBadRequestResponse e,
        Interfaces.IResult result,
        Guid orderId,
        RevolvProcessor processor = RevolvProcessor.worldpay)
    {
        var workspan = Workspan.Current;

        SetInternalCode(result, processor, e.message);


        result.RawResult = JsonSerializer.Serialize(e);
        result.Status = TransactionStatus.Failed;
        result.AddError(e.message);
        workspan.RecordError("Revolv bad request {BadRequestResponse} message", e.message);
        return result;
    }


    private Interfaces.IResult UpdateResult(
        RevolvHttpError e,
        Interfaces.IResult result,
        Guid orderId,
        RevolvProcessor processor = RevolvProcessor.worldpay)
    {
        var workspan = Workspan.Current;
        result.RawResult = JsonSerializer.Serialize(e);
        result.Status = TransactionStatus.Failed;
        result.AddErrorWithCode(e.Code.ToString(), e.Content);
        workspan.RecordError("Revolv unknown {HTTPError} exception", e.Code);
        return result;
    }


    public async Task<AuthResult> AuthorizeAsync(AuthorizationRequest authRequest, CancellationToken token = default)
    {
        using var workspan = Workspan.Start<RevolvPaymentProvider>().LogEnterAndExit();
        var amount = Math.Round(authRequest.Amount / 100M, 2);

        var config = await GetCredentialsAsync(authRequest.SupportedGateway.Sandbox,
            authRequest.SupportedGateway.Id, token);

        var card = authRequest.CreditCard;
        var billingAddress = authRequest.BillingAddress;
        var shippingAddress = authRequest.ShippingAddress;
        var threeDs = authRequest.ThreeDS;
        var reference = Guid.NewGuid();

        var useSchemeTransactionId = !authRequest.IsCit && authRequest.NetworkReferenceData?.TransactionId is not null;

        // var phone = TransformPhoneToE164(billingAddress.PhoneNumber, billingAddress.Country);


        AuthResult InitResult()
        {
            AuthResult result = new()
            {
                OrderId = authRequest.OrderId,
                Provider = this.CurrentPaymentProvider,
                BinNumber = authRequest.CreditCard?.Bin,
                PaymentInstrumentId = authRequest.PaymentInstrumentId,
                CavvCode = authRequest.ThreeDS?.AuthenticationValue,
                ProcessorId = authRequest.Gateway?.SupportedGateway?.ProcessorId
            };


            return result;
        }


        RevolvAuthRequest GetRequest()
        {
            var request = new RevolvAuthRequest()
            {
                amount = new RevolvAmount()
                {
                    value = amount,
                    currency = authRequest.CurrencyCode
                },
                paymentMethod = new()
                {
                    billingAddress = billingAddress is null
                        ? null
                        : new RevolvBillingAddress()
                        {
                            addressLine1 = billingAddress.Address1,
                            addressLine2 = billingAddress.Address2,
                            city = billingAddress.City,
                            state = billingAddress.State,
                            postalCode = billingAddress.Zip,
                            country = billingAddress.Country,
                            phoneNumber = billingAddress.PhoneNumber,
                            email = billingAddress.Email
                        },
                    creditCard = new()
                    {
                        paymentAccountNumber = card.Number,
                        expirationDate = $"{card.Month.ToString().PadLeft(2, '0')}{card.Year}",
                        securityCode = card.VerificationValue
                    },
                    billingFirstName = card.FirstName,
                    billingLastName = card.LastName,
                    merchantPaymentMethodRefId = Guid.NewGuid().ToString()
                },
                networkProcessing = authRequest.IsCit
                    ? null
                    : new()
                    {
                        originalNetworkTransactionId = useSchemeTransactionId
                            ? authRequest.NetworkReferenceData?.TransactionId
                            : null,
                        processingType = useSchemeTransactionId
                            ? NetworkProcessingType.merchantInitiatedCOF
                            : NetworkProcessingType.initialCOF
                    },
            };

            return request;
        }

        var result = InitResult();

        var request = GetRequest();
        var validResult = new RevolvAuthRequest.Validator().Validate(request);
        validResult.ThrowOnErrors(logErrors: true);


        var logRequest = request.CopyAndRedact(r =>
        {
            if (r.paymentMethod?.creditCard is not null)
            {
                r.paymentMethod.creditCard.paymentAccountNumber = "****";
                r.paymentMethod.creditCard.securityCode = r.paymentMethod.creditCard.securityCode is null
                    ? null
                    : r.paymentMethod.creditCard.securityCode?.Length.ToString();
            }
        });

        workspan.Log.Information("RevolveRequest {RevolveAuthRequest} for {OrderId}", logRequest, authRequest.OrderId);


        try
        {
            var response = await _sdk.Authorize(request, config, token);


            var _ = response switch
            {
                (RevolvAuthResponse success, null, null, null, null) => UpdateResult(success, result,
                    authRequest.OrderId, config.Processor),
                (null, RevolvPaymentResponseError error, null, null, null) => UpdateResult(error, result,
                    authRequest.OrderId, config.Processor),
                (null, null, RevolvHttpError httpError, null, null) => UpdateResult(httpError, result,
                    authRequest.OrderId, config.Processor),
                (null, null, null, JsonException e, null) => UpdateResult(e, result, authRequest.OrderId),
                (null, null, null, null, RevolvBadRequestResponse badreq) => UpdateResult(badreq, result,
                    authRequest.OrderId, config.Processor),
                _ => throw new Exception("Unknown response type")
            };
        }
        catch (Exception e)
        {
            UpdateResult(e, result, authRequest.OrderId, config.Processor);
        }

        return result;
    }


    private Interfaces.IResult UpdateResult(
        RevolvAuthResponse response,
        AuthResult result,
        Guid orderId,
        RevolvProcessor processor = RevolvProcessor.worldpay)
    {
        var workspan = Workspan.Current;
        var isSuccess = response.paymentMethodAuthorizationId.HasValue;
        if (isSuccess)
        {
            result.Status = TransactionStatus.Completed;
            result.ProviderResponseCode = "COMPLETED";
            result.ProviderResponseMessage = "COMPLETED";
            //temporarily stub
            result.CvvCode = "M";
            result.AvsCode = "Y";
            var internalResponse = InternalResponseMapper.GetMappedResponse("0");
            result.InternalResponseCode = internalResponse?.MappedResponseCode;
            result.InternalResponseMessage = internalResponse?.MappedResponseMessage;
            result.InternalResponseGroup = internalResponse?.MappedResponseGroup.ToString();
        }
        else
        {
            var internalResponse = InternalResponseMapper.GetMappedResponse("-1");
            result.InternalResponseCode = internalResponse?.MappedResponseCode;
            result.InternalResponseMessage = internalResponse?.MappedResponseMessage;
            result.InternalResponseGroup = internalResponse?.MappedResponseGroup.ToString();
            result.Status = TransactionStatus.Failed;
            result.ProviderResponseCode = "FAILED";
            result.ProviderResponseMessage = "FAILED";
        }

        result.ProviderTransactionToken = response.paymentMethodAuthorizationId.ToString();
        result.RawResult = JsonSerializer.Serialize(response, _jsonOptions);
        result.SchemeTransactionId = response?.networkTransactionId;


        //  result.ProcessorId = response?.pro.ToString(); todo-check
        return result;
    }

    private Interfaces.IResult UpdateResult(
        RevolvVoidResponse response,
        VoidPaymentResult result,
        Guid orderId,
        RevolvProcessor processor = RevolvProcessor.worldpay)
    {
        var workspan = Workspan.Current;
        if (response.message == "Approved")
        {
            result.Status = TransactionStatus.Completed;
            result.ProviderResponseCode = "COMPLETED";
            result.ProviderResponseMessage = response.message;
        }
        else
        {
            result.Status = TransactionStatus.Failed;
            result.ProviderResponseCode = "FAILED";
            result.ProviderResponseMessage = response.message;
        }

        result.Status = TransactionStatus.Completed;
        result.ProviderResponseCode = "COMPLETED";
        result.ProviderResponseMessage = "COMPLETED";
        //result.ProviderTransactionToken = response.paymentMethodAuthorizationId.ToString();
        result.RawResult = JsonSerializer.Serialize(response, _jsonOptions);
        //result.SchemeTransactionId = response?.networkTransactionId;
        //  result.ProcessorId = response?.pro.ToString(); todo-check
        return result;
    }


    private Interfaces.IResult UpdateResult(
        RevolvRefundResponse response,
        ICreditPaymentResult result,
        Guid orderId,
        RevolvProcessor processor = RevolvProcessor.worldpay)

    {
        var workspan = Workspan.Current;
        var lastrefund = response.refunds.Last();

        bool isSuccess = lastrefund.invoiceAttemptStatus == "Success";
        if (isSuccess)
        {
            result.Status = TransactionStatus.Completed;
            result.ProviderResponseCode = "COMPLETED";
            result.ProviderResponseMessage = "COMPLETED";
        }
        else
        {
            result.Status = TransactionStatus.Failed;
            result.ProviderResponseCode = "FAILED";
            result.ProviderResponseMessage = "FAILED";
        }


        //  result.ProviderTransactionToken = response.paymentMethodAuthorizationId.ToString();
        result.RawResult = JsonSerializer.Serialize(response, _jsonOptions);
        //result.SchemeTransactionId = response?.networkTransactionId;
        //  result.ProcessorId = response?.pro.ToString(); todo-check
        return result;
    }


    private Interfaces.IResult UpdateResult(
        RevolvSalesResponse response,
        SaleResult result,
        Guid orderId,
        RevolvProcessor processor = RevolvProcessor.worldpay)
    {
        var workspan = Workspan.Current;
        result.Status = TransactionStatus.Completed;
        result.ProviderResponseCode = "COMPLETED";
        result.ProviderResponseMessage = "COMPLETED";
        //  result.ProviderTransactionToken = response.paymentMethodAuthorizationId.ToString();
        result.RawResult = JsonSerializer.Serialize(response, _jsonOptions);
        result.SchemeTransactionId = response?.networkTransactionId;

        var internalResponse = InternalResponseMapper.GetMappedResponse("0");
        result.InternalResponseCode = internalResponse?.MappedResponseCode;
        result.InternalResponseMessage = internalResponse?.MappedResponseMessage;
        result.InternalResponseGroup = internalResponse?.MappedResponseGroup.ToString();
        //temporarily stub
        result.CvvCode = "M";
        result.AvsCode = "Y";

        //  result.ProcessorId = response?.pro.ToString(); todo-check
        return result;
    }


    private Interfaces.IResult UpdateResult(
        RevolvCaptureResponse response,
        CapturePaymentResult result,
        Guid orderId,
        RevolvProcessor processor = RevolvProcessor.worldpay)
    {
        var workspan = Workspan.Current;
        if (response?.message == "Approved")
        {
            result.Status = TransactionStatus.Completed;
            result.ProviderResponseCode = "COMPLETED";
            result.ProviderResponseMessage = response.message;
            var internalResponse = InternalResponseMapper.GetMappedResponse("0");
            result.InternalResponseCode = internalResponse?.MappedResponseCode;
            result.InternalResponseMessage = internalResponse?.MappedResponseMessage;
            result.InternalResponseGroup = internalResponse?.MappedResponseGroup.ToString();
        }
        else
        {
            result.Status = TransactionStatus.Failed;
            result.ProviderResponseCode = "FAILED";
            result.ProviderResponseMessage = response?.message;
            SetInternalCode(result, processor, response?.message);
        }

        result.RawResult = JsonSerializer.Serialize(response, _jsonOptions);
        result.ProviderTransactionToken = response?.invoiceId is null
            ? null
            : response.invoiceId.ToString();

        // result.SchemeTransactionId = response?.networkTransactionId;
        //  result.ProcessorId = response?.pro.ToString(); todo-check
        return result;
    }

    public async Task<CapturePaymentResult> CaptureAsync(CapturePaymentRequest request,
        CancellationToken token = default)
    {
        using var workspan = Workspan.Start<RevolvPaymentProvider>().LogEnterAndExit();
        var amount = Math.Round(request.Amount / 100M, 2);

        var config = await GetCredentialsAsync(request.SupportedGateway.Sandbox,
            request.SupportedGateway.Id, token);
        var descriptor = request.Descriptor;
        // var card = request.CreditCard;
        // var billingAddress = authRequest.BillingAddress;
        // var shippingAddress = authRequest.ShippingAddress;
        // var threeDs = authRequest.ThreeDS;
        var reference = Guid.NewGuid();


        CapturePaymentResult InitResult()
        {
            CapturePaymentResult result = new();

            result.TransactionId = request.OrderId;
            result.Provider = this.CurrentPaymentProvider;
            result.ProviderTransactionToken = request.TransactionToken;
            result.PaymentInstrumentId = request.PaymentInstrumentId;

            return result;
        }


        RevolvCaptureRequest GetRequest()
        {
            RevolvCaptureRequest result = new();
            result.invoice = new RevolvInvoice()
            {
                amount = new RevolvAmount()
                {
                    value = amount,
                    currency = request.CurrencyCode
                },
                merchantInvoiceRefId = request.OrderId.ToString(),
            };
            result.dynamicDescriptor = GetDescriptor(descriptor);


            return result;
        }

        var result = InitResult();

        var rrequest = GetRequest();
        var validResult = new RevolvCaptureRequest.Validator().Validate(rrequest);
        validResult.ThrowOnErrors(logErrors: true);


        workspan.Log.Information("RevolveRequest {RevolveCaptureRequest} for {OrderId}", request, request.OrderId);

        try
        {
            var response =
                await _sdk.Capture(rrequest, config, long.Parse(request.TransactionToken), token); //todo-validate


            var _ = response switch
            {
                (RevolvCaptureResponse success, null, null, null, null) => UpdateResult(success, result,
                    request.OrderId, config.Processor),

                (null, RevolvPaymentResponseError errror, null, null, null) => UpdateResult(errror, result,
                    request.OrderId, config.Processor),
                (null, null, RevolvHttpError unknownError, null, null) => UpdateResult(unknownError, result,
                    request.OrderId, config.Processor),
                (null, null, null, JsonException e, null) => UpdateResult(e, result, request.OrderId),
                (null, null, null, null, RevolvBadRequestResponse badreq) => UpdateResult(badreq, result,
                    request.OrderId, config.Processor),
                _ => throw new Exception("Unknown response type")
            };
        }
        catch (Exception e)
        {
            UpdateResult(e, result, request.OrderId, config.Processor);
        }

        return result;
    }


    private RevolvDynamicDescriptor GetDescriptor(DescriptorDTO descriptor)
    {
        return descriptor is null
            ? null
            : new()
            {
                countryCode = descriptor.Country?.Length == 3
                    ? new GeoServices().Get2LetterCountryCode(descriptor.Country)
                    : descriptor.Country?.Length == 2
                        ? descriptor.Country
                        : null,
                city = descriptor.City,
                subMerchantName = descriptor.Name,
                subMerchantPhone = descriptor.Phone,
                subMerchantId = descriptor.MerchantId
            };
    }


    public async Task<SaleResult> SaleAsync(SaleRequest request, CancellationToken token = default)
    {
        using var workspan = Workspan.Start<RevolvPaymentProvider>().LogEnterAndExit();
        var amount = Math.Round(request.Amount / 100M, 2);

        var config = await GetCredentialsAsync(request.SupportedGateway.Sandbox,
            request.SupportedGateway.Id, token);
        var descriptor = request.Descriptor;
        // var card = request.CreditCard;
        // var billingAddress = authRequest.BillingAddress;
        // var shippingAddress = authRequest.ShippingAddress;
        // var threeDs = authRequest.ThreeDS;
        var reference = Guid.NewGuid();


        SaleResult InitResult()
        {
            SaleResult result = new();

            result.TransactionId = request.OrderId;
            result.Provider = this.CurrentPaymentProvider;
            result.PaymentInstrumentId = request.PaymentInstrumentId;
            return result;
        }


        RevolvSalesRequest GetRequest()
        {
            RevolvSalesRequest result = new();
            result.invoice = new()
            {
                amount = new()
                {
                    value = amount,
                    currency = request.CurrencyCode,
                },
                merchantInvoiceRefId = request.OrderId.ToString(),
            };
            result.dynamicDescriptor = GetDescriptor(descriptor);

            result.paymentMethod = new RevolvPaymentMethod()
            {
                billingAddress = request.BillingAddress is null
                    ? null
                    : new RevolvBillingAddress()
                    {
                        addressLine1 = request.BillingAddress.Address1,
                        addressLine2 = request.BillingAddress.Address2,
                        city = request.BillingAddress.City,
                        state = request.BillingAddress.State,
                        postalCode = request.BillingAddress.Zip,
                        country = request.BillingAddress.Country,
                        phoneNumber = request.BillingAddress.PhoneNumber,
                        email = request.BillingAddress.Email
                    },
                creditCard = new RevolvCreditCard()
                {
                    expirationDate = $"{request.CreditCard.Month.ToString().PadLeft(2, '0')}{request.CreditCard.Year}"
                },
                billingFirstName = request.CreditCard.FirstName,
                billingLastName = request.CreditCard.LastName,
                merchantPaymentMethodRefId = Guid.NewGuid().ToString()
            };
            result.networkProcessing = request.IsCit
                ? null
                : new RevolvNetworkProcessing()
                {
                    originalNetworkTransactionId = request.NetworkReferenceData?.TransactionId,
                    processingType = request.NetworkReferenceData?.TransactionId is not null
                        ? NetworkProcessingType.merchantInitiatedCOF
                        : NetworkProcessingType.initialCOF
                };
            return result;
        }


        var result = InitResult();
        var rrequest = GetRequest();

        var validResult = new RevolvSalesRequest.Validator().Validate(rrequest);
        validResult.ThrowOnErrors(logErrors: true);

        var logRequest = rrequest.CopyAndRedact(r =>
        {
            if (r.paymentMethod?.creditCard is not null)
            {
                r.paymentMethod.creditCard.paymentAccountNumber = "****";
                r.paymentMethod.creditCard.securityCode = r.paymentMethod.creditCard.securityCode is null
                    ? null
                    : r.paymentMethod.creditCard.securityCode?.Length.ToString();
            }
        });

        workspan.Log.Information("{RevolveAuthRequest} for {OrderId}", logRequest, request.OrderId);

        try
        {
            var response = await _sdk.Sale(rrequest, config, token);
            var _ = response switch
            {
                (RevolvSalesResponse success, null, null, null, null) => UpdateResult(success, result, request.OrderId,
                    config.Processor),
                (null, RevolvPaymentResponseError error, null, null, null) => UpdateResult(error, result,
                    request.OrderId, config.Processor),
                (null, null, RevolvHttpError httpError, null, null) => UpdateResult(httpError, result,
                    request.OrderId, config.Processor),
                (null, null, null, JsonException e, null) => UpdateResult(e, result, request.OrderId, config.Processor),
                (null, null, null, null, RevolvBadRequestResponse badreq) => UpdateResult(badreq, result,
                    request.OrderId, config.Processor),
                _ => throw new Exception("Unknown response type")
            };
        }
        catch (Exception e)
        {
            UpdateResult(e, result, request.OrderId, config.Processor);
        }


        return result;
    }

    public async Task<AchTransferResult> AchCreditAsync(IAchTransferRequest request,
        CancellationToken token = default)
    {
        throw new NotImplementedException();
    }

    public async Task<AchTransferResult> AchDebitAsync(IAchTransferRequest request,
        CancellationToken token = default)
    {
        throw new NotImplementedException();
    }

    public async Task<AchCancelResult> AchCancelAsync(IAchCancelPaymentRequest payload,
        CancellationToken token = default)
    {
        throw new NotImplementedException();
    }

    public async Task<AchCancelResult> AchCancelAsync(AchCancelPaymentRequest payload,
        CancellationToken token = default)
    {
        throw new NotImplementedException();
    }


    public async Task<ICreditPaymentResult> CreditAsync(ICreditPaymentRequest request,
        CancellationToken token = default)
    {
        using var workspan = Workspan.Start<RevolvPaymentProvider>().LogEnterAndExit();

        var amount = Math.Round(request.Amount / 100M, 2);

        var config = await GetCredentialsAsync(request.SupportedGateway.Sandbox,
            request.SupportedGateway.Id, token);

        //var descriptor = request.Descriptor;
        // var card = request.CreditCard;
        // var billingAddress = authRequest.BillingAddress;
        // var shippingAddress = authRequest.ShippingAddress;
        // var threeDs = authRequest.ThreeDS;
        var reference = Guid.NewGuid();

        var invoiceId = long.Parse(request.ProviderTransactionToken);

        var invoice = await _sdk.GetInvoice(config, invoiceId, token);

        bool canRefund = true;
        if (invoice.SuccessfulResponse is not null)
        {
            var status = invoice.SuccessfulResponse.invoiceStatus;
            canRefund = status switch
            {
                RevolvInvoiceStatus.Paid or RevolvInvoiceStatus.PartialRefund
                    => true,
                _ => false
            };
        }
        else
        {
            workspan.Log.Information(
                "Error getting invoice data"); // dif we cannot get info about invoice-let's try refund
        }


        CreditPaymentResult InitResult()
        {
            CreditPaymentResult result = new();

            result.Provider = this.CurrentPaymentProvider;
            result.InternalTransactionId = request.TransactionId;
            result.PaymentInstrumentId = request.PaymentInstrumentId;
            result.ProcessorId = request.SupportedGateway.ProcessorId;
            result.SupportedGatewayId = request.SupportedGateway.Id;
            return result;
        }


        RevolvRefundRequest GetRequest()
        {
            var request = new RevolvRefundRequest()
            {
                amount = amount,
            };
            return request;
        }

        var result = InitResult();
        if (!canRefund)
        {
            result.Status = TransactionStatus.Failed;
            result.ProviderResponseCode = "CannotRefund";
            result.ProviderResponseMessage = "CannotRefund";
            result.AddErrorWithCode("CannotRefund", "CannotRefund");
            return result;
        }


        var rrequest = GetRequest();

        var validResult = new RevolvRefundRequest.Validator().Validate(rrequest);
        validResult.ThrowOnErrors(logErrors: true);

        workspan.Log.Information("RevolveRefundRequest {RevolveRefundRequest} for {OrderId}", rrequest,
            request.OrderId);


        try
        {
            var response = await _sdk.Refund(rrequest, config, long.Parse(request.ProviderTransactionToken), token);

            var _ = response switch
            {
                (RevolvRefundResponse success, null, null, null, null) =>
                    UpdateResult(success, result, request.OrderId, config.Processor),

                (null, RevolvPaymentResponseError error, null, null, null) => UpdateResult(error, result,
                    request.OrderId, config.Processor),

                (null, null, RevolvHttpError httpError, null, null) => UpdateResult(httpError, result,
                    request.OrderId, config.Processor),

                (null, null, null, JsonException e, null) => UpdateResult(e, result, request.OrderId, config.Processor),
                (null, null, null, null, RevolvBadRequestResponse badreq) => UpdateResult(badreq, result,
                    request.OrderId, config.Processor),
                _ => throw new Exception("Unknown response type")
            };
        }
        catch (Exception e)
        {
            UpdateResult(e, result, request.OrderId, config.Processor);
        }

        return result;
    }


    public async Task<ICreditPaymentResult> StandaloneCreditAsync(ICreditPaymentRequest request,
        SupportedGateway supportedGateway,
        CancellationToken token = default)
    {
        throw new NotImplementedException();
    }

    public async Task<IVoidPaymentResult> VoidAsync(IVoidPaymentRequest voidPaymentRequest,
        CancellationToken token = default)
    {
        using var workspan = Workspan.Start<RevolvPaymentProvider>().LogEnterAndExit();
        var config = await GetCredentialsAsync(voidPaymentRequest.SupportedGateway.Sandbox,
            voidPaymentRequest.SupportedGateway.Id, token);

        var amount = Math.Round(voidPaymentRequest.Amount / 100M, 2);

        VoidPaymentResult InitResult()
        {
            VoidPaymentResult result = new();

            result.Provider = this.CurrentPaymentProvider;
            // result.InternalTransactionId = voidPaymentRequest.TransactionId;
            result.PaymentInstrumentId = voidPaymentRequest.PaymentInstrumentId;
            result.ProcessorId = voidPaymentRequest.SupportedGateway.ProcessorId;
            result.SupportedGatewayId = voidPaymentRequest.SupportedGateway.Id;
            return result;
        }

        RevolvVoidRequest GetRequest()
        {
            var request = new RevolvVoidRequest()
            {
                amount = amount,
                reason = voidPaymentRequest.Reason,
                paymentMethodAuthorizationId = long.Parse(voidPaymentRequest.ProviderTransactionToken)
            };

            return request;
        }

        var result = InitResult();
        var request = GetRequest();

        var validResult = new RevolvVoidRequest.Validator().Validate(request);
        validResult.ThrowOnErrors(logErrors: true);


        workspan.Log.Information(" RevolveRequest {RevolvVoidRequest} for {OrderId}", request,
            voidPaymentRequest.OrderId);

        try
        {
            var response = await _sdk.Void(request, config, token);

            var _ = response switch
            {
                (RevolvVoidResponse success, null, null, null, null) =>
                    UpdateResult(success, result, voidPaymentRequest.OrderId, config.Processor),

                (null, RevolvPaymentResponseError error, null, null, null) =>
                    UpdateResult(error, result, voidPaymentRequest.OrderId, config.Processor),

                (null, null, RevolvHttpError httpError, null, null) =>
                    UpdateResult(httpError, result, voidPaymentRequest.OrderId, config.Processor),

                (null, null, null, JsonException e, null) =>
                    UpdateResult(e, result, voidPaymentRequest.OrderId, config.Processor),

                (null, null, null, null, RevolvBadRequestResponse badreq) =>
                    UpdateResult(badreq, result, voidPaymentRequest.OrderId, config.Processor),

                _ => throw new Exception("Unknown response type")
            };
        }
        catch (Exception e)
        {
            UpdateResult(e, result, voidPaymentRequest.OrderId, config.Processor);
        }

        return result;
    }

    //todo-check with Revolv team-Verify is not documented
    public async Task<IVerifyInstrumentResult> VerifyAsync(IVerifyInstrumentRequest payload,
        CancellationToken token = default)
    {
        var authRequest = new AuthorizationRequest()
        {
            Amount = 0,
            Descriptor = payload.Descriptor,
            AccountLastUpdatedAt = payload.AccountLastUpdatedAt,
            Device = payload.Device,
            Gateway = payload.Gateway,
            SupportedGateway = payload.SupportedGateway,
            CreditCard = payload.CreditCard,
            AccountUpdaterSettings = payload.AccountUpdaterSettings,
            BillingAddress = payload.BillingAddress,
            IsCit = payload.IsCit,
            Discount = 0,
            Mid = payload.Mid,
            OrderId = payload.OrderId,
            CurrencyCode = payload.CurrencyCode,
            Modifiers = payload.Modifiers,
            ShippingAddress = null,
            NetworkTokenInfo = payload.NetworkTokenInfo,
            PaymentInstrumentId = payload.PaymentInstrumentId,
        };

        var authResult = await AuthorizeAsync(authRequest, token);
        var verifyResult = new VerifyInstrumentResult()
        {
            Status = authResult.Status,
            ProviderResponseCode = authResult.ProviderResponseCode,
            ProviderResponseMessage = authResult.ProviderResponseMessage,
            RawResult = authResult.RawResult,
            ProviderTransactionToken = authResult.ProviderTransactionToken,
            SchemeTransactionId = authResult.SchemeTransactionId,
            ProcessorId = authResult.ProcessorId,
            OrderId = authResult.OrderId,
            PaymentInstrumentId = authResult.PaymentInstrumentId,
            BinNumber = authResult.BinNumber,
            CavvCode = authResult.CavvCode,
        };
        return verifyResult;
    }


    public override bool SupportCapture => true;
    public override bool SupportPartiallyRefund => true;
    public override bool SupportRefund => true;
    public override bool SupportVoid => true;
    public override bool SupportTokenization => true;
    public override bool RequirePostProcessPayment => true;
    public override bool SupportPayouts => false;
    public override string CurrentPaymentProvider => GatewayTypesConstants.Revolv3;
    public override bool SupportsAch => false;
    public override bool SupportsCreditCards => true;
    public override bool SupportsCreditCardVerification => true;
    public override bool SupportsSubscription => false;
    public override bool SupportsStandaloneCredit => true;
    public override bool SupportsExternalThreeDS => true;
    public override CardBrand[] NetworkTokenCardBrands => new[] {CardBrand.Visa, CardBrand.MasterCard};
    public bool Supports3DSDataOnly { get; set; } = false;
}