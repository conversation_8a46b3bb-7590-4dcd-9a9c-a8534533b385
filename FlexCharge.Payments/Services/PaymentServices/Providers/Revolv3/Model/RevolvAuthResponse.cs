namespace FlexCharge.Payments.Services.PaymentServices.Providers.Revolv3.Model;

public class RevolvAuthResponse
{
    public string networkTransactionId { get; set; }
    public long? paymentMethodAuthorizationId { get; set; }
    public RevolvPaymentMethod paymentMethod { get; set; }
    public long paymentMethodId { get; set; }
    public long billingAddressId { get; set; }
    public RevolvBillingAddress billingAddress { get; set; }
    public string merchantPaymentMethodRefId { get; set; }

    public RevolvPaymentMethodAchDetails paymentMethodAchDetails { get; set; }
    public RevolvPaymentMethodCardDetails paymentMethodCreditCardDetails { get; set; }
}