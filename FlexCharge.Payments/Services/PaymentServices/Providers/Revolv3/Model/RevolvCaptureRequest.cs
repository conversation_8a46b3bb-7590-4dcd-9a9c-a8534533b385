using FluentValidation;

namespace FlexCharge.Payments.Services.PaymentServices.Providers.Revolv3.Model;

public class RevolvCaptureRequest
{
    public long? customerId { get; set; }
    public RevolvInvoice invoice { get; set; }

    public RevolvDynamicDescriptor dynamicDescriptor { get; set; }

    public class Validator : AbstractValidator<RevolvCaptureRequest>
    {
        public Validator()
        {
            RuleFor(x => x.invoice).NotNull();
            RuleFor(x => x.dynamicDescriptor).NotNull();
        }
    }
}