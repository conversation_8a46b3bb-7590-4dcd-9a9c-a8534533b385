using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;

namespace FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels
{
    public enum PayoutStatus
    {
        UNPROCESSED,
        PROCESSING,
        FAILED,
        CANCELED,
        SUCCESS
    }

    public enum PaymentStatus
    {
        /// <summary>
        /// Pending
        /// </summary>
        [Description("-1")] Initiated = -1,

        /// <summary>
        /// Pending - Code 0
        /// </summary>
        [Description("000")] Approved = 0,

        /// <summary>
        /// Pending
        /// </summary>
        [Description("10")] Pending = 10,

        /// <summary>
        /// Authorized
        /// </summary>
        [Description("20")] Authorized = 20,

        /// <summary>
        /// Paid
        /// </summary>
        [Description("30")] Paid = 30,

        /// <summary>
        /// Micro payment Refunded - 303
        /// </summary>
        [Description("303")] MicroPaymentRefunded = 303,

        /// <summary>  
        /// Refunded - 301 - Need to debit the PM
        /// </summary>
        [Description("301")] RefundedNotCollected = 301,

        /// <summary>
        /// Partially Refunded
        /// </summary>
        [Description("350")] PartiallyRefunded = 350,

        /// <summary>
        /// Refunded
        /// </summary>
        [Description("400")] Refunded = 400,

        /// <summary>
        /// Chargeback
        /// </summary>
        [Description("401")] Chargeback = 401,

        /// <summary>
        /// Chargeback To collect - funds that need to return to operating account
        /// </summary>
        [Description("402")] ChargebackToCollect = 402,

        /// <summary>
        /// Chargeback To manually collect - funds that need to return to operating account manually
        /// </summary>
        [Description("4022")] ChargebackToManuallyCollect = 4022,

        /// <summary>
        /// Chargeback Collected - funds that moved to Settlment account
        /// </summary>
        [Description("403")] ChargebackCollected = 403,

        /// <summary>
        /// Returned
        /// </summary>
        [Description("405")] Returned = 405,

        /// <summary>
        /// Chargeback Recovered - funds that moved to operating account
        /// </summary>
        [Description("455")] ChargebackRecovered = 455,

        /// <summary>
        /// Voided
        /// </summary>
        [Description("50")] Voided = 50,

        /// <summary>
        /// Paidout
        /// </summary>
        [Description("60")] NSF = 60,

        /// <summary>
        /// Failed
        /// </summary>
        [Description("4")] Failed = 4,

        /// <summary>
        /// Failed
        /// </summary>
        [Description("5")] WatingForRetry = 5,

        /// <summary>
        /// Settled
        /// </summary>
        [Description("700")] Settled = 700,

        /// <summary>
        /// Partially Paidout
        /// </summary>
        [Description("800")] PartiallyPaidout = 800,

        /// <summary>
        /// Paidout
        /// </summary>
        [Description("801")] Paidout = 801
    }

    // public enum MoneyMovementType
    // {
    //     /// <summary>
    //     /// Funding Instruction PayFac Credit (FIPC) - used to move funds from the PayFac Settlement account to the
    //     /// PayFac Operating account.
    //     /// </summary>
    //     FIPC,
    //     /// <summary>
    //     /// Funding Instruction PayFac Debit (FIPD) - used to move funds from the PayFac Operating account to the
    //     /// PayFac Settlement account.
    //     /// </summary>
    //     FIPD,
    //     /// <summary>
    //     /// Funding Instruction Reserve Credit (FIRC) - used to move funds from the PayFac Settlement account to
    //     ///the PayFac Reserve account.
    //     /// </summary>
    //     FIRC,
    //     /// <summary>
    //     /// Funding Instruction Reserve Debit (FIRD) - used to move funds from the PayFac Reserve account to the
    //     ///PayFac Settlement account.
    //     /// </summary>
    //     FIRD,
    //     /// <summary>
    //     /// Funding Instruction merchant Credit (FIMC) - used to move funds from the Settlement account
    //     ///to the merchant Operating account.
    //     /// </summary>
    //     FIMC,
    //     /// <summary>
    //     /// Funding Instruction Sub-merchant Debit (FIMD) - used to move funds from the merchant Operating
    //     ///account to the PayFac Settlement account.
    //     /// </summary>
    //     FISD,
    //     /// <summary>
    //     /// Funding Instruction Vendor Credit (FIVC) - used to move funds from the PayFac Settlement account to
    //     ///the Vendor account.
    //     /// </summary>
    //     FIVC,
    //     /// <summary>
    //     /// Funding Instruction Vendor Debit (FIVD) - used to move funds from the Vendor account to the PayFac
    //     ///Settlement account.
    //     /// </summary>
    //     FIVD,
    //     /// <summary>
    //     /// Funding Instruction Physical Check Credit (FICC) - used to move funds from the PayFac Settlement
    //     ///account to the Physical Check account.
    //     /// </summary>
    //     FICC,
    //     /// <summary>
    //     /// Funding Instruction Physical Check Debit (FICD) - used to move funds from the Physical Check account
    //     ///to the PayFac Settlement.
    //     /// </summary>
    //     FICD
    // }
    // public enum OrderStatus
    // {
    //     /// <summary>
    //     /// Pending
    //     /// </summary>
    //     Pending = 10,
    //     /// <summary>
    //     /// Processing
    //     /// </summary>
    //     Processing = 20,
    //     /// <summary>
    //     /// Complete
    //     /// </summary>
    //     Complete = 30,
    //     /// <summary>
    //     /// Cancelled
    //     /// </summary>
    //     Cancelled = 40
    // }

    public enum PaymentMethodType
    {
        /// <summary>
        /// Credit Card
        /// </summary>
        [Description("CreditCard")] CreditCard,

        /// <summary>
        /// Credit Card
        /// </summary>
        [Description("DebitCard")] DebitCard,

        /// <summary>
        /// ECheck
        /// </summary>
        [Description("Echeck")] Echeck,

        /// <summary>
        /// Ach
        /// </summary>
        [Description("Ach")] Ach,

        /// <summary>
        /// Check
        /// </summary>
        [Description("Check")] Check,

        /// <summary>
        /// Alternative payment token
        /// </summary>
        [Description("Apm")] Apm,
    }

    public enum SaleTransferIdPrefix
    {
        general,
        psp
    }

    // public enum CreditCardTypeType
    // {
    //     [Description("Visa")]
    //     VI = 0,
    //     [Description("MasterCard")]
    //     MC = 1,
    //     [Description("Discover")]
    //     DI = 2,
    //     [Description("Amex")]
    //     AX = 3
    //     //[Description("Switch")]
    //     //SWITCH = 4,
    //     //[Description("Solo")]
    //     //SOLO = 5,
    //     //[Description("Maestro")]
    //     //MAESTRO = 6
    // }

    public enum AchReturnCodes
    {
        [Description("Insufficient funds in account")]
        R01 = 01,
        [Description("Account is closed")] R02 = 02,
        [Description("No account on file")] R03 = 03,

        [Description("Invalid account number")]
        R04 = 04,

        [Description("Unauthorized debit to consumer account")]
        R05 = 05,

        [Description("Returned at request of ODFI")]
        R06 = 06,

        [Description("Authorization revoked by Customer")]
        R07 = 07,
        [Description("Payment stopped")] R08 = 08,

        [Description("Insufficient collected funds in account being charged")]
        R09 = 09,

        [Description(
            "Customer advises not Authorized, notice not provided, improper source document, or amount of entry not accurately obtained from source document")]
        R10 = 10,

        [Description("Check truncation return")]
        R11 = 11,

        [Description("Account sold to another financial institution")]
        R12 = 12,

        [Description("Invalid ACH routing number")]
        R13 = 13,

        [Description("Representative payee is deceased or cannot continue in that capacity")]
        R14 = 14,

        [Description("Beneficiary or account holder other than representative payee deceased")]
        R15 = 15,

        [Description("Account funds have been frozen")]
        R16 = 16,

        [Description("Item returned because of invalid data; refer to addenda fro information")]
        R17 = 17,

        [Description("Improper effective date")]
        R18 = 18,
        [Description("Amount error")] R19 = 19,

        [Description("Account does not allow ACH transactions or limit for transactions has been exceeded")]
        R20 = 20,

        [Description("Invalid company identification")]
        R21 = 21,
        [Description("Invalid individual ID")] R22 = 22,

        [Description("Credit entry refused by receiver")]
        R23 = 23,
        [Description("Duplicate entry")] R24 = 24,
        [Description("Addenda record error")] R25 = 25,
        [Description("Mandatory field error")] R26 = 26,
        [Description("Trace number error")] R27 = 27,

        [Description("Routing/transit number check digit error")]
        R28 = 28,

        [Description("Corporate Customer advised not authorized")]
        R29 = 29,

        [Description("RDFI not participant in check truncation program")]
        R30 = 30,

        [Description("Permissible return entry")]
        R31 = 31,
        [Description("RDFI non-settlement")] R32 = 32,
        [Description("Return of item")] R33 = 33,

        [Description("Limited participation ODFI")]
        R34 = 34,

        [Description("Return of improper debit entry")]
        R35 = 35,

        [Description("Return of improper credit entry")]
        R36 = 36,

        [Description("Source document presented for payment")]
        R37 = 37,

        [Description("Stop payment on source document")]
        R38 = 38,

        [Description("Improper source document")]
        R39 = 39,

        [Description("Return of item by government agency")]
        R40 = 40,

        [Description("invalid Transaction Code")]
        R41 = 41,

        [Description("Routing/transit number check digit error")]
        R42 = 42,

        [Description("Invalid account number")]
        R43 = 43,
        [Description("Invalid individual ID")] R44 = 44,

        [Description("Invalid individual name or company name")]
        R45 = 45,

        [Description("Invalid representative payee indicator code")]
        R46 = 46,
        [Description("Duplicate enrollment")] R47 = 47,

        [Description("State law affecting RCK acceptance")]
        R50 = 50,

        [Description(
            "Item is ineligible, notice not provided, signature not genuine, or original item altered for adjustment entry")]
        R51 = 51,
        [Description("Stop payment on item")] R52 = 52,

        [Description("Item and ACH entry presented for payment")]
        R53 = 53,

        [Description(
            "Misrouted return - RDFI for original entry has placed incorrect routing/transit number in RDFI identification field")]
        R61 = 61,
        [Description("Duplicate return")] R67 = 67,

        [Description("Untimely return - return was not sent within the established time frame")]
        R68 = 68,
        [Description("Field errors")] R69 = 69,

        [Description("Permissible return entry not accepted")]
        R70 = 70,

        [Description("Misrouted dishonored return -incorrect routing/transit number in RDFI identification field")]
        R71 = 71,

        [Description("Untimely return - dishonored return was not sent within the established time frame")]
        R72 = 72,

        [Description(
            "Timely original return - RDFI certifies the original return entry was sent within established timeframe for original returns")]
        R73 = 73,

        [Description(
            "Corrected return - RDFI is correcting a previous return entry that was dishonored because it contained incomplete or incorrect information")]
        R74 = 74,

        [Description("Original return not a duplicate")]
        R75 = 75,
        [Description("No errors found")] R76 = 76,

        [Description("Cross-border payment coding error")]
        R80 = 80,

        [Description("Non-participant in cross-border program")]
        R81 = 81,

        [Description("Invalid foreign RDFI identification")]
        R82 = 82,

        [Description("Foreign RDFI unable to settle")]
        R83 = 83,

        [Description("Cross-border entry not processed by originating gateway operator")]
        R84 = 84,

        [Description("Administrative return item was processed and resubmitted as a photocopy")]
        R94 = 94,

        [Description("Administrative return item was processed and resubmitted as a MICR-Split")]
        R95 = 95,

        [Description("Administrative return item was processed and resubmitted with corrected dollar amount")]
        R97 = 97,

        [Description(
            "Indicates a return PAC (pre-authorized check); RDFI provides a text reason and indicated a new account number on the PAC itself")]
        R98 = 98,

        [Description(
            "Indicates a return PAC (pre-authorized check); RDFI provides a text reason on the PAC itself for which there is no equivalent return reason code")]
        R99 = 99,
    }
}