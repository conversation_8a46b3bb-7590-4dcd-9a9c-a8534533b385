using System;
using FlexCharge.Stripe.Models;
using FlexCharge.Payments.Entities;
using FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels;

namespace FlexCharge.Payments.Services.PaymentServices.Models;

public class SubscriptionGetRequest
{
    public Guid? SubscriptionId { get; set; }
    public string ProviderSubscriptionId { get; set; }
    public Gateway Gateway { get; set; }
    public SupportedGateway SupportedGateway { get; set; }
    public string? ExternalAccountId { get; set; }
}