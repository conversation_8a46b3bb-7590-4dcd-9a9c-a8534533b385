using System;

namespace FlexCharge.Payments.Services.PaymentServices.Models;

public class SubscriptionStatus
{
    public bool? IsActive { get; set; }
    public bool? LastPaymentSuccess { get; set; }

    public string ProviderName { get; set; }
    public Guid ProviderId { get; set; }
    public string ProviderSubscriptionId { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? CancelAt { get; set; }
}