using System;
using FlexCharge.Payments.Entities;
using FlexCharge.Payments.Services.PaymentServices.Models;
using FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels;

namespace FlexCharge.Payments.Services.PaymentServices.Interfaces;

public interface IMerchantRequest
{
    public Guid Mid { get; set; }
    public Guid OrderId { get; set; }
    public Guid? PayerId { get; set; }
    public Guid? SiteId { get; set; }

    public int Amount { get; set; }
    public int FeeAmount { get; set; }

    public string CurrencyCode { get; set; }

    //public bool UseDynamicDescriptor { get; set; }
    public DescriptorDTO Descriptor { get; set; }
    public DeviceDetailsDTO Device { get; set; }
    public Gateway Gateway { get; set; }
    public SupportedGateway SupportedGateway { get; set; }
}