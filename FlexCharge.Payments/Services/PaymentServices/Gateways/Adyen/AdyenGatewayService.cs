using System.Collections.Generic;
using System.Text.Json;
using FlexCharge.Common.Shared.Payments;
using FlexCharge.Payments.Entities;
using FlexCharge.Payments.Services.PaymentServices.Providers.Adyen;
using FlexCharge.PaymentsUtils;

namespace FlexCharge.Payments.Services.PaymentServices.Gateways.Adyen;

public class AdyenGatewayService : BaseGatewayService<AdyenConfigDTO>
{
    public AdyenGatewayService(PostgreSQLDbContext context) : base(context)
    {
    }

    protected override string NameIdentifier => GatewayTypesConstants.Adyen;

    protected override void MapConfigToGateway(AdyenConfigDTO dto, SupportedGateway gateway)
    {
        gateway.SecretKey = dto.ApiKey;
        AdyenConfiguration configuration = new()
        {
            ApiKey = dto.ApiKey,
            MerchantAccount = dto.MerchantAccount,
            MCC = dto.MCC,
            WebhookHmacKey = dto.WebhookHmacKey,
        };

        gateway.Configuration = JsonSerializer.Serialize(configuration, _jsonOptions);
    }

    protected override void MapGatewayToConfig(SupportedGateway gateway, AdyenConfigDTO dto)
    {
        dto.ApiKey = gateway.SecretKey;
        var configuration =
            JsonSerializer.Deserialize<AdyenConfiguration>(gateway.Configuration, _jsonOptions);
        dto.MerchantAccount = configuration.MerchantAccount;
        dto.MCC = configuration.MCC;
        dto.WebhookHmacKey = configuration.WebhookHmacKey;
    }

    protected override List<ListLookup> GetListsInternal()
    {
        return null;
    }
}