using System;
using System.Collections.Generic;
using AutoMapper;
using FlexCharge.Common.Shared.Payments;
using FlexCharge.Payments.Entities;
using FlexCharge.PaymentsUtils;

namespace FlexCharge.Payments.Services.PaymentServices.Gateways.Nmi;

public class NmiGatewayService : BaseGatewayService<NmiConfigDTO>
{
    public NmiGatewayService(PostgreSQLDbContext context) : base(context)
    {
    }

    protected override string NameIdentifier => GatewayTypesConstants.Nmi_v2;

    protected override void MapConfigToGateway(NmiConfigDTO dto, SupportedGateway gateway)
    {
        gateway.SecretKey = dto.SecretKey;
    }

    protected override void MapGatewayToConfig(SupportedGateway gateway, NmiConfigDTO dto)
    {
        dto.SecretKey = gateway.SecretKey;
    }

    protected override List<ListLookup> GetListsInternal()
    {
        return null;
    }
}