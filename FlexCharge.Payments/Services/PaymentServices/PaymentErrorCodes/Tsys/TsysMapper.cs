using System;
using System.Collections.Generic;
using System.Linq;

namespace FlexCharge.Payments.Services.PaymentServices.PaymentErrorCodes.Tsys;

public class TsysMapper
{
    private static Dictionary<string, string> _messageToCode;
    private static Dictionary<string, string> mapDict;

    static TsysMapper()
    {
        _messageToCode = new(StringComparer.OrdinalIgnoreCase);

        var properties = typeof(WorldpayResponseCodes).GetProperties();
        foreach (var prop in properties)
        {
            _messageToCode.TryAdd(prop.Name, prop.GetValue(null).ToString());
        }

        mapDict = Map();
    }

    public static string Map(string message)
    {
        message = string.Concat(message.Where(char.IsLetterOrDigit));
        if (mapDict.TryGetValue(message, out var code))
        {
            return code;
        }

        return "-1";
    }

    static Dictionary<string, string> Map()
    {
        var tsysToIsoMap = new Dictionary<string, string>
        {
            { TsysCodes.Approval, ISO8583.SUCCESS },
            { TsysCodes.CallIssuer, ISO8583.ISSUER_DECLINED },
            { TsysCodes.CallIssuerSpecialCondition, ISO8583.ISSUER_DECLINED_SPECIAL_CONDITION },
            { TsysCodes.TermIdError, ISO8583.HTTP_400_INVALID_MERCHANT_OR_SERVICE_PROVIDER },
            { TsysCodes.HoldCallNoFraud, ISO8583.CARD_LOST_OR_STOLEN },
            { TsysCodes.DeclineDoNotHonor, ISO8583.DO_NOT_HONOR },
            { TsysCodes.ErrorGeneral, ISO8583.HTTP_500_GENERAL_ERROR },
            { TsysCodes.HoldCallFraudAccount, ISO8583.CARD_LOST_OR_STOLEN_SPECIAL_CONDITION },
            { TsysCodes.ApprovalHonorMastercardWithId, ISO8583.SUCCESS_WITH_IDENTIFICATION },
            { TsysCodes.PartialApproval, ISO8583.PARTIAL_APPROVAL },
            { TsysCodes.ApprovalVipApproval, ISO8583.SUCCESS_VIP },
            { TsysCodes.InvalidTrans, ISO8583.TRANSACTION_INVALID },
            { TsysCodes.AmountError, ISO8583.TRANSACTION_EXCEEDS_AMOUNT_LIMIT },
            { TsysCodes.CardNoError, ISO8583.CARD_NUMBER_INVALID },
            { TsysCodes.NoSuchIssuer, ISO8583.TRANSACTION_COULD_NOT_BE_ROUTED_NO_DUCH_ISSUER },
            { TsysCodes.ReEnter, ISO8583.HTTP_503_REENTER_TRANSACTION },
            { TsysCodes.NoActionTaken, ISO8583.HTTP_500_INVALID_RESPONSE },
            { TsysCodes.NoCardNumber, ISO8583.CARD_NUMBER_INVALID_NO_SUCH_NUMBER },
            { TsysCodes.NoReply, ISO8583.HTTP_503_TEMPORARILY_UNAVAILABLE },
            { TsysCodes.MsgFormatError, ISO8583.HTTP_500_FORMAT_ERROR },
            { TsysCodes.NoCreditAcct, ISO8583.CARD_NUMBER_INVALID_NO_CHECKING_ACCOUNT },
            { TsysCodes.HoldCallLostCardPickUpFraudAccount, ISO8583.CARD_LOST },
            { TsysCodes.HoldCallStolenCardPickUpFraudAccount, ISO8583.CARD_STOLEN },
            { TsysCodes.ClosedAccount, ISO8583.CARD_NUMBER_INVALID_NO_SAVINGS_ACCOUNT },
            { TsysCodes.DeclineInsufficientFunds, ISO8583.INSUFFICIENT_FUNDS },
            { TsysCodes.NoCheckAccount, ISO8583.CARD_NUMBER_INVALID_NO_CHECKING_ACCOUNT },
            { TsysCodes.NoSaveAccount, ISO8583.CARD_NUMBER_INVALID_NO_SAVINGS_ACCOUNT },
            { TsysCodes.ExpiredCard, ISO8583.CARD_EXPIRED },
            { TsysCodes.WrongPin, ISO8583.CVN_MISMATCH },
            { TsysCodes.ServNotAllowedCard, ISO8583.TRANSACTION_NOT_ALLOWED_BY_CARDHOLDER },
            { TsysCodes.ServNotAllowedTerminal, ISO8583.TRANSACTION_NOT_ALLOWED_AT_TERMINAL },
            { TsysCodes.SuspectedFraud, ISO8583.SUSPECTED_FRAUD },
            { TsysCodes.ExcApprAmtLim, ISO8583.CARD_ACTIVITY_EXCEEDS_AMOUNT_LIMIT },
            { TsysCodes.DeclineInvalidServiceCodeRestricted, ISO8583.TRANSACTION_NOT_ALLOWED_RESTRICTED_CARD },
            { TsysCodes.SecViolation, ISO8583.CVN_MISMATCH_SECURITY_VIOLATION },
            { TsysCodes.ExcWDFreqLim, ISO8583.CARD_ACTIVITY_EXCEEDS_COUNT_LIMIT },
            { TsysCodes.VerifDataFaild, ISO8583.CVN_MISMATCH },
            { TsysCodes.PinExceeded, ISO8583.RESPONSE_RECEIVED_TOO_LATE },
            { TsysCodes.UnsolicReversal, ISO8583.HTTP_503_REQUEST_IN_PROGRESS },
            { TsysCodes.NoActionTakenInconsistentReversedOrRepeatData, ISO8583.HTTP_500_INVALID_RESPONSE },
            {
                TsysCodes.NoAccountBlockedFirstUsedTransactionFromNewCardholderAndCardNotProperlyUnblocked,
                ISO8583.CARD_NOT_ACTIVATED
            },
            { TsysCodes.AlreadyReversedAtSwitch, ISO8583.HTTP_503_VISA_CREDIT_ISSUER_UNAVAILBLE },
            { TsysCodes.NoImpact, ISO8583.CARD_EXPIRATION_DATE_INVALID },
            { TsysCodes.EncryptionError, ISO8583.HTTP_500_SYSTEM_MALFUNCTION },
            { TsysCodes.IncorrectCvv, ISO8583.CVN_MISMATCH_CVV2_FAILURE },
            { TsysCodes.CantVerifyPin, ISO8583.CVN_MISMATCH },
            { TsysCodes.CardOk, ISO8583.SUCCESS_ALL_VERIFICATIONS_PASSED },
            { TsysCodes.CantVerifyPin2, ISO8583.CVN_MISMATCH },
            { TsysCodes.NoReply2, ISO8583.HTTP_503_ISSUER_UNAVAILABLE },
            { TsysCodes.InvalidRouting, ISO8583.TRANSACTION_COULD_NOT_BE_ROUTED },
            { TsysCodes.DeclineViolationCannotComplete, ISO8583.TRANSACTION_NOT_ALLOWED_VIOLATION_OF_LAW },
            { TsysCodes.DuplicateTrans, ISO8583.HTTP_50_DUPLICATE_TRANSMISSIONS },
            { TsysCodes.SystemError, ISO8583.HTTP_500_SYSTEM_MALFUNCTION },
            { TsysCodes.Activated, ISO8583.SUCCESS },
            { TsysCodes.NotActivated, ISO8583.CARD_NOT_ACTIVATED },
            { TsysCodes.Deactivated, ISO8583.CARD_NOT_ACTIVATED },
            { TsysCodes.InvalidRegionCode, ISO8583.TRANSACTION_NOT_ALLOWED_VIOLATION_OF_LAW },
            { TsysCodes.InvalidCountryCode, ISO8583.TRANSACTION_NOT_ALLOWED_VIOLATION_OF_LAW },
            { TsysCodes.SrchgNotAllowed, ISO8583.TRANSACTION_NOT_ALLOWED_VIOLATION_OF_LAW },
            { TsysCodes.SrchgNotAllowed2, ISO8583.TRANSACTION_NOT_ALLOWED_VIOLATION_OF_LAW },
            { TsysCodes.FailureCv, ISO8583.CVN_MISMATCH_CVV2_FAILURE },
            { TsysCodes.SecurCryptFail, ISO8583.CARD_AUTHENTICATION_FAILED },
            { TsysCodes.EncrNotConfigd, ISO8583.HTTP_500_SYSTEM_MALFUNCTION },
            { TsysCodes.TermNotAuth, ISO8583.HTTP_500_SYSTEM_MALFUNCTION },
            { TsysCodes.DecryptFailure, ISO8583.HTTP_500_SYSTEM_MALFUNCTION },
            { TsysCodes.AcctLengthErr, ISO8583.CARD_NUMBER_INVALID },
            { TsysCodes.CheckDigitErr, ISO8583.CARD_NUMBER_INVALID },
            { TsysCodes.CidFormatError, ISO8583.CARD_NUMBER_INVALID },
            { TsysCodes.ServNotAllowed2, ISO8583.TRANSACTION_NOT_ALLOWED_AT_TERMINAL },
            { TsysCodes.StopRecurring, ISO8583.STOP_PAYMENT },
            { TsysCodes.StopAllRecur, ISO8583.REVOCATION_OF_ALL_AUTHORIZATIONS },
            { TsysCodes.InactiveCard, ISO8583.CARD_NOT_ACTIVATED },
            { TsysCodes.Mod10Fail, ISO8583.CARD_NUMBER_INVALID },
            { TsysCodes.DclnNoPreAuth, ISO8583.ISSUER_DECLINED },
            { TsysCodes.MaxBalance, ISO8583.CARD_ACTIVITY_EXCEEDS_AMOUNT_LIMIT },
            { TsysCodes.ShutDown, ISO8583.HTTP_500_SYSTEM_MALFUNCTION },
            { TsysCodes.InvalidStatus, ISO8583.CARD_NOT_ACTIVATED },
            { TsysCodes.UnknownStore, ISO8583.TRANSACTION_COULD_NOT_BE_ROUTED },
            { TsysCodes.TooManyRchrgs, ISO8583.CARD_ACTIVITY_EXCEEDS_COUNT_LIMIT },
            { TsysCodes.AlreadyUsed, ISO8583.CARD_NUMBER_INVALID },
            { TsysCodes.NotManual, ISO8583.TRANSACTION_NOT_ALLOWED_AT_TERMINAL },
            { TsysCodes.TypeUnknown, ISO8583.TRANSACTION_INVALID },
            { TsysCodes.InvalidTender, ISO8583.TRANSACTION_INVALID },
            { TsysCodes.CustomerType, ISO8583.CUSTOMER_INFO_INVALID },
            { TsysCodes.PinLocked, ISO8583.CVN_MISMATCH },
            { TsysCodes.MaxRedempts, ISO8583.CARD_ACTIVITY_EXCEEDS_COUNT_LIMIT },
            { TsysCodes.MaxPanTries, ISO8583.CARD_ACTIVITY_EXCEEDS_COUNT_LIMIT },
            { TsysCodes.AlreadyIssued, ISO8583.CARD_NUMBER_INVALID },
            { TsysCodes.NotIssued, ISO8583.CARD_NUMBER_INVALID },
            { TsysCodes.Approval2, ISO8583.SUCCESS },
            { TsysCodes.CannotConvert, ISO8583.TRANSACTION_INVALID },
            { TsysCodes.InvalidAba, ISO8583.CARD_NUMBER_INVALID },
            { TsysCodes.FailureVm, ISO8583.CARD_ACTIVITY_EXCEEDS_COUNT_LIMIT },
            { TsysCodes.FixInvalidMcc, ISO8583.TRANSACTION_INVALID },
            { TsysCodes.AmountLimitError, ISO8583.TRANSACTION_EXCEEDS_AMOUNT_LIMIT }
        };
        return tsysToIsoMap;
    }
}