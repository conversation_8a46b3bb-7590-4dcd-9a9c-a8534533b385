using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Response;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.CardBrands;
using FlexCharge.Payments.Entities;
using FlexCharge.Payments.Services.PaymentServices.Interfaces;
using FlexCharge.Payments.Services.PaymentServices.Models;
using FlexCharge.Payments.Services.PaymentServices.Models.ExternalTokenPayments;
using FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Serialization;

namespace FlexCharge.Payments.Services.PaymentServices;

public abstract class PaymentProviderBase : IPaymentProvider
{
    public virtual Task<AuthResult> AuthorizeAsync(AuthorizationRequest authRequest, CancellationToken token = default)
    {
        throw new NotImplementedException();
    }

    public virtual Task<CapturePaymentResult> CaptureAsync(CapturePaymentRequest request,
        CancellationToken token = default)
    {
        throw new NotImplementedException();
    }

    public virtual Task<SaleResult> SaleAsync(SaleRequest request, CancellationToken token = default)
    {
        throw new NotImplementedException();
    }

    public virtual Task<AchTransferResult> AchCreditAsync(IAchTransferRequest request,
        CancellationToken token = default)
    {
        throw new NotImplementedException();
    }

    public virtual Task<AchTransferResult> AchDebitAsync(IAchTransferRequest request, CancellationToken token = default)
    {
        throw new NotImplementedException();
    }

    public virtual Task<AchCancelResult> AchCancelAsync(IAchCancelPaymentRequest payload,
        CancellationToken token = default)
    {
        throw new NotImplementedException();
    }

    public virtual Task<ICreditPaymentResult> CreditAsync(ICreditPaymentRequest request,
        CancellationToken token = default)
    {
        throw new NotImplementedException();
    }

    public virtual Task<ICreditPaymentResult> StandaloneCreditAsync(ICreditPaymentRequest request,
        SupportedGateway supportedGateway,
        CancellationToken token = default)
    {
        throw new NotImplementedException();
    }

    public virtual Task<IVoidPaymentResult> VoidAsync(IVoidPaymentRequest voidPaymentRequest,
        CancellationToken token = default)
    {
        throw new NotImplementedException();
    }

    // public virtual ProcessPaymentResult ProcessRecurringPayment(ProcessPaymentRequest processPaymentRequest)
    // {
    //     throw new NotImplementedException();
    // }
    //
    // public virtual CancelRecurringPaymentResult CancelRecurringPayment(
    //     CancelRecurringPaymentRequest cancelPaymentRequest)
    // {
    //     throw new NotImplementedException();
    // }

    public virtual Task<IVerifyInstrumentResult> VerifyAsync(IVerifyInstrumentRequest payload,
        CancellationToken token = default)
    {
        throw new NotImplementedException();
    }

    public virtual Task<ITokenResult> RegisterTokenAsync(ITokenRequest tokenRequest, CancellationToken token = default)
    {
        throw new NotImplementedException();
    }

    public virtual Task<CreateSubscriptionResult> CreateSubscriptionAsync(CreateSubscriptionRequest request,
        CancellationToken token)
    {
        throw new NotImplementedException();
    }

    public virtual Task<RequestResult<CancelExternalSubscriptionResult>> CancelSubscriptionAsync(
        SubscriptionGetRequest getRequest,
        CancellationToken token)
    {
        throw new NotImplementedException();
    }

    public virtual Task<bool> UpdateCardAsync(Guid externalSubId, (string number, string year, string month) card)
    {
        throw new NotImplementedException();
    }

    public virtual Task<bool> SetSubscriptionStatusAsync(Guid externalSubId, bool isSuccess, bool isActive)
    {
        throw new NotImplementedException();
    }

    public virtual Task<SubscriptionAndLastInvoiceStatus> GetSubscriptionStatusAsync(
        SubscriptionGetRequest subscriptionGetRequest, CancellationToken token)
    {
        throw new NotImplementedException();
    }

    public virtual Task<SubscriptionAndLastInvoiceStatus> GetAndSyncSubscriptionAsync(SubscriptionGetRequest request,
        CancellationToken token)
    {
        throw new NotImplementedException();
    }

    public virtual async Task<(bool CanHandle, Guid? SupportedGatewayId)> CanHandleWebhookEventAsync(string body,
        IHeaderDictionary headers, CancellationToken token)
    {
        return (false, null);
    }

    public virtual Task<IWebHookResult> HandleWebhookEventAsync(string body, Guid gatewayId, CancellationToken token)
    {
        throw new NotImplementedException();
    }

    public virtual Task<IRefundCancelResult> CancelRefundAsync(ICancelRefundRequest payload, CancellationToken token)
    {
        throw new NotImplementedException();
    }

    #region EXTERNAL TOKEN PAYMENTS

    public virtual Task<RequestResult<ChargePaymentResult>> ChargeAsync(ChargePaymentRequest request,
        CancellationToken token = default)
    {
        throw new NotImplementedException();
    }

    public virtual Task<RequestResult<CanChargeExternalTokenResult>> CanChargeExternalTokenAsync(
        CanChargeExternalTokenRequest request,
        CancellationToken token)
    {
        throw new NotImplementedException();
    }

    public virtual Task<RequestResult<MarkInvoiceAsPaidOutOfBandResult>> MarkInvoiceAsPaidOutOfBandAsync(
        MarkInvoiceAsPaidOutOfBandRequest request, CancellationToken token = default)
    {
        throw new NotImplementedException();
    }

    #endregion


    protected void LogResponse(object response, Workspan workspan,
        [System.Runtime.CompilerServices.CallerMemberName]
        string memberName = "")
    {
        JsonSerializerSettings jsonSerializeSettings = new()
        {
            ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore,
            ContractResolver = new CamelCasePropertyNamesContractResolver(),
            Converters = new List<JsonConverter>() {new StringEnumConverter()}
        };
        if (response is not null)
        {
            try
            {
                workspan.Log.Information("{Provider} {Response} in {Method} ",
                    CurrentPaymentProvider,
                    JsonConvert.SerializeObject(response, jsonSerializeSettings),
                    memberName);
            }
            catch (Exception e)
            {
                workspan.Log.Information("{Provider}  response log exception {Message}", CurrentPaymentProvider,
                    e.Message);
            }
        }
    }


    public abstract bool SupportCapture { get; }


    public abstract bool SupportPartiallyRefund { get; }

    public abstract bool SupportRefund { get; }
    public abstract bool SupportVoid { get; }

    public abstract bool SupportTokenization { get; }

    public abstract bool RequirePostProcessPayment { get; }

    public abstract bool SupportPayouts { get; }

    public abstract string CurrentPaymentProvider { get; }

    public abstract bool SupportsAch { get; }

    public abstract bool SupportsCreditCards { get; }

    public abstract bool SupportsCreditCardVerification { get; }

    public abstract bool SupportsExternalThreeDS { get; }

    public abstract bool SupportsSubscription { get; }

    public abstract bool SupportsStandaloneCredit { get; }

    public abstract CardBrand[] NetworkTokenCardBrands { get; }
}