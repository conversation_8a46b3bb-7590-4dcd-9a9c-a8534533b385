using System.Collections.Generic;
using FlexCharge.Contracts.Common;
using FluentValidation.Results;
using Wrappers_Compile;

namespace FlexCharge.Payments.Services.AccountUpdater.Models;

public class AccountUpdaterResponse
{
    //result from account updater, can be null in case of Errors
    public AccountUpdaterResult Result { get; set; }

    public List<Error> ProviderErrors { get; set; }

    public List<ValidationFailure> FlexValidationErrors { get; set; }

    public string ProviderTraceId { get; set; }
}