using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Elasticsearch.Net;
using FlexCharge.Common.Emails;
using FlexCharge.Common.Telemetry;
using FlexCharge.Payments;
using FlexCharge.Utils;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

public interface INotificationReportService
{
    Task NotifyNewQueueItem(Guid queueItemId);
    Task NotifyUnprocessedQueueItems();
}

public class NotificationReportService : INotificationReportService
{
    private readonly PostgreSQLDbContext _dbContext;
    private readonly IEmailSender _emailSender;
    private readonly IConfiguration _configuration;

    public NotificationReportService(
        PostgreSQLDbContext dbContext,
        IEmailSender emailSender,
        IConfiguration configuration)
    {
        _dbContext = dbContext;
        _emailSender = emailSender;
        _configuration = configuration;
    }
    
    public async Task NotifyNewQueueItem(Guid queueItemId)
    {
        using var workspan = Workspan.Start<ReportsService>().LogEnterAndExit();

        try
        {
            var queueItem = await _dbContext.DisputeQueueItems
                .FirstOrDefaultAsync(x => x.Id == queueItemId);
            
            var queue = await _dbContext.DisputeQueues
                .FirstOrDefaultAsync(x => x.Id == queueItem.DisputeQueueId);

            if (queueItem != null && queue != null && queue.NewItemsAlert && queue.EmailRecipient != null)
            {
                try
                {
                    // https://flex-charge.atlassian.net/wiki/spaces/PF/pages/483852290/Portal+Communication+and+Notifications#New-dispute-was-queued
                    var templateId = _configuration.GetValue<string>("email:newDisputeItemsTemplateId");
                    await _emailSender.SendEmailAsync(queue.EmailRecipient,
                        $"New dispute in {queue.Name} queue", "Content", new
                        {
                            subject = $"New dispute in {queue.Name} queue",
                            // partner = "FlexFactor",
                            base_url = _configuration.GetValue<string>("email:portalBaseUrl"),
                            path = $"/disputes/queue-item",
                            slug = "",
                            email = queue.EmailRecipient,
                            distribution = "external",
                            template_id = templateId,
                            queue_name = queue.Name,
                            item_id = queueItem.Id,
                            item_creation_date = queueItem.CreatedOn,
                            item_auth_date = queueItem.TransactionDate,
                            item_amount = Formatters.IntToDecimal(queueItem.Amount),
                            item_bin = queueItem.Bin,
                            item_lastfour = queueItem.Last4,
                            item_arn = queueItem.Arn,
                            item_source = queueItem.DisputeManagementSystem,
                            item_stage = queueItem.Stage,
                        },
                        templateId);
                }
                catch (Exception e)
                {
                    workspan.Log.Fatal("Error checking new items: queue {queueId}: message: {message}", queue.Id, e.Message);
                }
            }
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e, "Error occurred while checking new queue items");
            throw;
        }
    }
    
    public async Task NotifyUnprocessedQueueItems()
    {
        using var workspan = Workspan.Start<ReportsService>().LogEnterAndExit();

        try
        {
            var queuesGroupedByEmail = await _dbContext.DisputeQueues
                .Include(x => x.DisputeQueueItems)
                .Where(x => x.OpenItemsAlert & x.EmailRecipient != null)
                .GroupBy(x => x.EmailRecipient)
                .ToListAsync();

            if (queuesGroupedByEmail != null)
            {
                // foreach (var queue in queues)
                // {
                //     try
                //     {
                //         var unprocessedItemsCount = queue.DisputeQueueItems
                //             .Count(x => x.IsArchived == null && !x.IsDeleted);
                //
                //         if (queue.OpenItemsAlert && unprocessedItemsCount > 0 && queue.EmailRecipient != null)
                //         {
                //             workspan.Log.Information(
                //                 $"Unprocessed items for queue: {queue.Name}; unprocessed items count: {unprocessedItemsCount} ");
                //             
                //         }
                //     }
                //     catch (Exception e)
                //     {
                //         workspan.Log.Fatal("Error checking unprocessed items: queue {queueId}: message: {message}", queue.Id, e.Message);
                //     }
                // }
                
                // var templateId = _configuration.GetValue<string>("email:unprocessedDisputeItemsTemplateId");
                // await _emailSender.SendEmailAsync(queue.EmailRecipient,
                //     $"Reminder: you still have {unprocessedItemsCount} in {queue.Name}", "Content", new
                //     {
                //         queue_name = queue.Name,
                //         queue_open_items = unprocessedItemsCount,
                //         base_url = _configuration.GetValue<string>("email:portalBaseUrl"),
                //         path = $"/disputes/queue/{queue.Id}",
                //     },
                //     templateId)

                foreach (var queueGroup in queuesGroupedByEmail)
                {
                    int totalQueuesWithUnprocessedItems = 0;
                    int totalUnprocessedItemsCount = 0;
                    var queueDetailsList = new List<(string queue_name, int queue_open_items, Guid id)>();

                    foreach (var queue in queueGroup)
                    {
                        try
                        {
                            var unprocessedItemsCount = queue.LoadedRecordsCount - queue.ProcessedRecordsCount - queue.ErrorCount;

                            if (queue.OpenItemsAlert && unprocessedItemsCount > 0 && queue.EmailRecipient != null)
                            {
                                totalQueuesWithUnprocessedItems++;
                                totalUnprocessedItemsCount += unprocessedItemsCount;
                                queueDetailsList.Add((queue.Name, unprocessedItemsCount, queue.Id));
                            }
                        }
                        catch (Exception e)
                        {
                            workspan.Log.Fatal("Error checking unprocessed items: queue {queueId}: message: {message}", queue.Id, e.Message);
                        }
                    }

                    var topQueues = queueDetailsList
                        .OrderByDescending(q => q.queue_open_items)
                        .Take(5)
                        .ToList();

                    workspan.Log.Information($"Total queues with unprocessed items: {totalQueuesWithUnprocessedItems}");
                    workspan.Log.Information($"Total unprocessed items count: {totalUnprocessedItemsCount}");
                
                //  https://flex-charge.atlassian.net/wiki/spaces/PF/pages/483852290/Portal+Communication+and+Notifications#Reminder%3A-disputes-still-pending
                    var templateId = _configuration.GetValue<string>("email:unprocessedDisputeItemsTemplateId");
                    var email = queueGroup.Key;
                    var queueWord = totalQueuesWithUnprocessedItems == 1 ? "queue" : "queues";
                    
                    await _emailSender.SendEmailAsync(email,
                        $"Reminder: You have {totalUnprocessedItemsCount} in {totalQueuesWithUnprocessedItems} {queueWord}", "Content", new
                        {
                            partner_color = "3F3F48",
                            email = email,
                            base_url = _configuration.GetValue<string>("email:portalBaseUrl"),
                            path = $"/disputes/queue",
                            slug = "",
                            total_open_items = totalUnprocessedItemsCount,
                            total_queues = totalQueuesWithUnprocessedItems,
                            template_id = templateId,
                            queues = topQueues.Select(q => new
                            {
                                queue_name = q.queue_name,
                                queue_open_items = q.queue_open_items,
                                id = q.id
                            }).ToList()
                        },
                        templateId);
                }
            }
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e, "Error occurred while checking unprocessed queue items");
            throw;
        }
    }
}