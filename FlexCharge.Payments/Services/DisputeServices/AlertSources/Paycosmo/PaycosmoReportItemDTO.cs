using CsvHelper.Configuration.Attributes;
using FlexCharge.Payments.Services.DisputeServices;

namespace FlexCharge.Payments.DTO.reports;

public class PaycosmoReportItemDTO : IDisputeReportRecord
{
    [Name("Date In")] public string DateIn { get; set; }
    [Name("Record Code")] public string RecordCode { get; set; }
    [Name("Card No")] public string CardNumber { get; set; }
    [Name("LocCode")] public string LocCode { get; set; }
    [Name("Tran Date")] public string TranDate { get; set; }
    [Name("Tran Time")] public string TranTime { get; set; }
    [Name("CB Amt")] public string Amount { get; set; }
    [Name("Reason")] public string Reason { get; set; }
    [Name("Status")] public string Status { get; set; }
    [Name("MCC")] public string MCC { get; set; }
    [Name("Doc Indit")] public string DocIndit { get; set; }
    [Name("ARN")] public string ARN { get; set; }
    [Name("Message")] public string Message { get; set; }
    [Name("Record Type")] public string RecordType { get; set; }
    [Name("Order Number")] public string OrderNumber { get; set; }
    [Name("Terminal")] public string Terminal { get; set; }
}