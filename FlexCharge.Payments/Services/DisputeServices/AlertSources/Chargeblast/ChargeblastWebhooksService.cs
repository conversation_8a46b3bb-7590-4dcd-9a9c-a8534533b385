using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using AutoMapper;
using FlexCharge.Common.Activities;
using FlexCharge.Common.Telemetry;
using FlexCharge.Payments.Activities;
using FlexCharge.Payments.DTO;
using FlexCharge.Payments.Entities;
using FlexCharge.Payments.Enums;
using FlexCharge.Payments.Services.DisputeServices.Models;
using FlexCharge.Utils;
using FlexCharge.Utils.JsonConverters;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;

namespace FlexCharge.Payments.Services.DisputeServices.Chargeblast;

public class ChargeblastWebhooksService : IWebhookService
{
    private readonly PostgreSQLDbContext _dbContext;
    private readonly IActivityService _activityService;
    private readonly INotificationReportService _notificationReportService;
    private readonly IReportMatchService _reportMatchService;
    private readonly IMapper _mapper;

    public ChargeblastWebhooksService(PostgreSQLDbContext dbContext, IActivityService activityService,
        INotificationReportService notificationReportService, IMapper mapper, IReportMatchService reportMatchService)
    {
        _dbContext = dbContext;
        _activityService = activityService;
        _notificationReportService = notificationReportService;
        _mapper = mapper;
        _reportMatchService = reportMatchService;
    }

    public async Task ProcessAsync(string requestBody, Guid? partnerId)
    {
        using var workspan = Workspan.Start<ChargeblastWebhooksService>()
            .Baggage("RequestBody", requestBody)
            .LogEnterAndExit();

        try
        {
            //2024-10-31 21:57:23.601000Z
            //"yyyy-MM-dd HH:mm:ss.ffffff'Z'"

            DateTimeOffsetFormatConverter.SetFormat("yyyy-MM-dd HH:mm:ss.ffffff'Z'");
            var data = System.Text.Json.JsonSerializer.Deserialize<AlertResponse>(requestBody,
                new JsonSerializerOptions()
                {
                    Converters = {new DateTimeOffsetFormatConverter()}
                });

            workspan
                .Baggage("AlertId", data.AlertId)
                .Baggage("Arn", data.Arn)
                .Log.Information("Ingestion Incoming MyRcvr Webhook");

            await _activityService.CreateActivityAsync(
                PaymentsIncomingWebhooksActivities.Payments_IncomingWebhooks_Webhook_Recieved,
                set => set
                    .Data(data)
                    .Meta(meta => meta
                        .ServiceProvider(AlertProviders.Chargeblast)
                        .SetValue("ExternalReferenceID", data.Id)
                        .SetValue("MyRcvrAuthorizationCode", data.AuthCode)
                        .SetValue("Arn", data.Arn)));

            var alertProvider = await _dbContext.AlertProviders
                .FirstOrDefaultAsync(x => x.Name == AlertProviders.Chargeblast && x.PartnerId == partnerId);

            if (alertProvider == null)
            {
                workspan.Log.Fatal(
                    "Ingestion Incoming MyRcvr Webhook - AlertProvider not found: partnerId - {PartnerId}", partnerId);
            }

            //check for existing item
            var existingItem = await _dbContext.DisputeQueueItems
                .FirstOrDefaultAsync(x =>
                    x.Arn == data.Arn && x.PartnerId == partnerId &&
                    x.DisputeManagementSystem == DisputeManagementSystem.Chargeback);

            if (existingItem != null)
            {
                workspan
                    .Tag("ExistingQueueItemId", existingItem.Id)
                    .Log.Warning("Ingestion Incoming Webhook - Duplicate record found");

                await _activityService.CreateActivityAsync(
                    PaymentsIncomingWebhooksErrorActivities.Payments_IncomingWebhooks_DuplicateError,
                    set => set
                        .Data(requestBody)
                        .Meta(meta => meta
                            .SetValue("DisputeQueueId", existingItem.DisputeQueueId)
                            .SetValue("ExistingItemId", existingItem.Id)
                            .SetValue("ExternalReferenceID", data.ExternalOrder)
                            .SetValue("AuthorizationCode", data.AuthCode)
                            .SetValue("Arn", data.Arn)
                            .SetValue("Provider", AlertProviders.Chargeblast)));
            }
            else
            {
                var queue = await GetOrCreateQueueAsync(AlertProviders.Chargeblast, AlertProviders.Chargeblast, partnerId);

            var disputeQueueItem = new DisputeQueueItem
            {
                IsWebhook = true,
                IsManualInserted = false,
                IsFileImported = false,
                IsSftp = false,

                ProviderName = AlertProviders.Chargeblast,
                DisputeManagementSystem = DisputeManagementSystem.Chargeback,

                DisputeType = data?.AlertType,
                Descriptor = data?.Descriptor,
                Currency = data?.Currency,
                TransactionDate = data.TransactionDate,
                AuthorizationDate = data.TransactionDate,
                RequestDate = data.CreatedAt,

                Amount = Formatters.DecimalToInt(data.Amount),

                Last4 = data?.Card.GetLast(4),
                Bin = data?.Card.GetFirst(6),
                MatchedTransactionCount = 0,
                Arn = data.Arn,
                AuthorizationCode = data.AuthCode,
                AuthorizationId = data.AuthCode,
                EventType = data?.AlertType,
                ExternalReferenceID = data.AlertId,
                OrderId = data?.ExternalOrder,
                PartnerId = partnerId,
                Issuer = data.Issuer,
                AlertProviderId = alertProvider?.Id,

                RequestId = data.Id,

                Stage = data?.Card.GetFirst(1) == "4" ? DisputeStage.RDR : DisputeStage.PRE_DISPUTE,

                Meta = requestBody,
            };

            await Enqueue(queue, disputeQueueItem);

            workspan.Baggage("DisputeQueueItem", JsonConvert.SerializeObject(disputeQueueItem));

            try
            {
                var potentialMatches =
                    await _reportMatchService.GetPotentialMatches(
                        _mapper.Map<DisputeQueueItem, DisputeQueueItemDTO>(disputeQueueItem));

                disputeQueueItem.DisputeQueueId = queue.Id;
                disputeQueueItem.PotentialMatches = _mapper.Map<List<PotentialMatch>>(potentialMatches);

                _dbContext.DisputeQueueItems.Update(disputeQueueItem);
                await _dbContext.SaveChangesAsync();
            }
            catch (Exception e)
            {
                workspan.RecordFatalException(e);
                disputeQueueItem.PotentialMatches = new List<PotentialMatch>();
            }

            await _activityService.CreateActivityAsync(
                PaymentsIncomingWebhooksActivities.Payments_IncomingWebhooks_Webhook_Succeeded,
                set => set
                    .Meta(meta => meta
                        .SetValue("DisputeQueueId", queue.Id)
                        .SetValue("DisputeQueueItemId", disputeQueueItem.Id)
                        .SetValue("ExternalReferenceID", data.ExternalOrder)
                        .SetValue("AuthorizationCode", data.AuthCode)
                        .SetValue("Arn", data.Arn)
                        .SetValue("Provider", AlertProviders.Chargeblast)));
            }
        }
        catch (Exception e)
        {
            workspan.RecordException(e, "Ingestion Incoming Webhook error: {Error}", e.Message);

            await _activityService.CreateActivityAsync(
                PaymentsIncomingWebhooksErrorActivities.Payments_IncomingWebhooks_Error,
                set => set
                    .Data(e)
                    .Meta(meta => meta.SetValue("Provider", AlertProviders.Chargeblast)));

            throw;
        }
    }

    private async Task<DisputeQueue> GetOrCreateQueueAsync(string provider, string queueName, Guid? partnerId)
    {
        var queue = await _dbContext.DisputeQueues
            .Include(x => x.DisputeQueueItems)
            .FirstOrDefaultAsync(x =>
                x.Name == queueName && x.PartnerId == partnerId && x.Source == provider);

        if (queue != null) return queue;

        Workspan.Current?.Log.Information("Queue not found: {QueueName}, creating new queue",
            queueName);

        queue = new DisputeQueue
        {
            Name = queueName,
            CreatedOn = DateTime.UtcNow,
            DisputeQueueItems = [],
            TotalCount = 0,
            LoadedRecordsCount = 0,
            Source = provider,
            PartnerId = partnerId
        };

        return queue;
    }


    private async Task<DisputeQueue> Enqueue(DisputeQueue queue, DisputeQueueItem item)
    {
        var existingItem = queue.DisputeQueueItems?.FirstOrDefault(x =>
            !string.IsNullOrEmpty(x.Arn) && !string.IsNullOrEmpty(item.Arn) &&
            x.Arn == item.Arn && x.Stage == item.Stage);

        if (existingItem != null) return queue;

        queue.DisputeQueueItems?.Add(item);

        queue.TotalCount += 1;
        queue.LoadedRecordsCount += 1;

        _dbContext.DisputeQueues.Update(queue);

        await _dbContext.SaveChangesAsync();

        return queue;
    }
}