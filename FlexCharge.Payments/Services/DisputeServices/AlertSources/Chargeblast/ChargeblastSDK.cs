using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Net.Mime;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Common.Telemetry;
using Microsoft.AspNetCore.WebUtilities;

namespace FlexCharge.Payments.Services.DisputeServices
{
    public class ChargeblastSDK
    {
        private readonly PostgreSQLDbContext _dbContext;
        private HttpClient _httpClient;

        private readonly INotificationReportService _notificationReportService;
        private readonly IActivityService _activityService;

        private JsonSerializerOptions _serializeOptions;

        public string ApiKey { get; set; } //"pkey_4D12xDjcLUzmurq5PHd0Ds"


        public ChargeblastSDK(HttpClient httpClient)
        {
            _httpClient = httpClient;
            _serializeOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = true,
                PropertyNameCaseInsensitive = true
            };

            _httpClient.BaseAddress = new Uri("https://api.chargeblast.com");
        }

        //set api key
        public void SetApiKey(string apiKey)
        {
            ApiKey = apiKey;
        }

        public async Task<AlertResponse> FetchAlert(string id)
        {
            using var workspan = Workspan.Start<ChargeblastSDK>()
                .Tag(nameof(id), id)
                .LogEnterAndExit();

            var qparams = new Dictionary<string, string>
            {
                {"api_key", ApiKey},
                {"id", id}
            };

            return await GetRequest<AlertResponse>("/", qparams, CancellationToken.None);
        }

        public async Task<List<AlertResponse>> FetchAlerts()
        {
            throw new NotImplementedException();
        }

        public async Task<int> UpdateAlert(string id, AlertUpdateRequest request)
        {
            var url = $"api/v2/alerts/update/{id}?api_key={ApiKey}";
            return await PostOrPutRequestAsync<AlertUpdateRequest, int>(url, request, CancellationToken.None);
        }

        public async Task CreateCreditRequest()
        {
            throw new NotImplementedException();
        }


        private async Task<TRes> PostOrPutRequestAsync<TReq, TRes>(string url, TReq request,
            CancellationToken token,
            bool isPost = true)
        {
            using var workspan = Workspan.Start<ChargeblastSDK>()
                .Tag(nameof(url), url)
                .LogEnterAndExit();

            string stringResult = null;
            try
            {
                var data = JsonSerializer.Serialize(request, _serializeOptions);
                var requestJson = new StringContent(data, Encoding.UTF8, MediaTypeNames.Application.Json);

                requestJson.Headers.ContentType = new MediaTypeHeaderValue("application/json");
                using var httpResponseMessage =
                    await (isPost
                        ? _httpClient.PostAsync(url, requestJson, token)
                        : _httpClient.PutAsync(url, requestJson, token));

                stringResult = await httpResponseMessage.Content.ReadAsStringAsync();
                workspan.Log.Information("SendRequestAsync > Response: {stringResult}", stringResult);

                // Check if the response indicates an error
                if (!httpResponseMessage.IsSuccessStatusCode)
                {
                    throw new InvalidOperationException($"Request failed with status code {httpResponseMessage.StatusCode}: {stringResult}");
                }

                
                // Attempt to deserialize the response
                // var result = JsonSerializer.Deserialize<TRes>(stringResult, _serializeOptions);
                // return result;
                
                // If status code 200 response is empty or some integer, return default
                return default;
            }
            catch (JsonException e)
            {
                workspan.RecordFatalException(e, "SendRequestAsync > JsonException Response: {stringResult}",
                    stringResult);
                throw new InvalidOperationException($"Failed to deserialize response: {stringResult}", e);
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                throw;
            }
        }

        private async Task<TRes> GetRequest<TRes>(string url, Dictionary<string, string> qparams,
            CancellationToken token)
        {
            using var workspan = Workspan.Start<ChargeblastSDK>().LogEnterAndExit()
                .Tag(nameof(url), url)
                .LogEnterAndExit();

            if (qparams?.Count > 0) url = QueryHelpers.AddQueryString(url, qparams);
            try
            {
                var response =
                    await _httpClient.GetAsync(url, token);
                return await response.Content.ReadFromJsonAsync<TRes>(_serializeOptions, cancellationToken: token);
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                return default;
            }
        }
    }
}