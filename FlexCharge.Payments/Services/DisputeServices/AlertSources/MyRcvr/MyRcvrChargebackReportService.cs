using System;
using System.Threading.Tasks;
using FlexCharge.Payments.DTO;
using FlexCharge.Payments.Enums;
using FlexCharge.Payments.Services.DisputeServices.Models;
using FlexCharge.Utils;
using Newtonsoft.Json;

namespace FlexCharge.Payments.Services.DisputeServices;

public class MyRcvrChargebackReportService : MyRcvrBaseReportService<MyrcvrChargebackItemDTO>, IDisputeReportService
{
    public MyRcvrChargebackReportService(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    protected override async Task<DisputeQueueItemDTO> CreateDisputeQueueItemDto(MyrcvrChargebackItemDTO record,
        int? parsedAmount, int? parsedDisputeAmount)
    {
        var disputeQueueItem = new DisputeQueueItemDTO
        {
            IsWebhook = false,
            IsManualInserted = false,
            IsFileImported = true,
            IsSftp = false,

            RequestDate = DateTime.Parse(record?.ChargebackDate).ToUtcDate(),
            AuthorizationDate = DateTime.Parse(record?.CreatedUtc).ToUtcDate(),
            TransactionDate = DateTime.Parse(record?.CreatedUtc).ToUtcDate(),
            CreateDateTime = DateTime.Parse(record?.CreatedUtc).ToUtcDate(),

            Currency = record?.Currency,
            Bin = record?.Bin,
            Last4 = record?.Last4,
            CardBrand = record?.PaymentMethod?.ToUpper(),
            DisputeType = record?.ReasonCode,
            Arn = record?.Arn,
            AuthorizationId = record?.AuthCode,
            ReferenceNumber = record.ReferenceNumber,
            ExternalReferenceID = record.TransactionId,

            DisputeManagementSystem = DisputeManagementSystem.MyRCVR,
            ProviderName = AlertProviders.MyRCVR,

            Stage = DisputeStage.CHARGEBACK,
            EarlyFraudWarning = false,
            HasMatch = false,
            Reason = record?.ReasonCode,
            DisputeReasonCode = record?.ReasonCode,

            Meta = JsonConvert.SerializeObject(record),
        };

        if (parsedAmount.HasValue)
        {
            disputeQueueItem.Amount = parsedAmount.Value;
        }

        if (parsedDisputeAmount.HasValue)
        {
            disputeQueueItem.DisputeAmount = parsedDisputeAmount.Value;
        }

        return disputeQueueItem;
    }
}