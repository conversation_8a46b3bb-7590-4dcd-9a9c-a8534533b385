using FlexCharge.Common;
using FlexCharge.Common.Authentication;
using FlexCharge.Payments.DTO;
using FlexCharge.Payments.Entities;
using FlexCharge.Payments.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Payments.Activities;
using FlexCharge.Payments.Entities.JsonbModels;
using FlexCharge.Payments.Services.Merchants;
using FlexCharge.Payments.Services.PaymentServices;
using FlexCharge.Payments.Services.SpreedlyService;
using MassTransit;

namespace FlexCharge.Payments.Controllers
{
    [Route("[controller]")]
    [ApiController]
    [JwtAuth]
    public class GatewayController : BaseController
    {
        private readonly PostgreSQLDbContext _context;
        private readonly ITransactionService _transactionService;
        private readonly IMerchantServices _merchantServices;
        private readonly ISpreedlyService _spreedlyService;
        private readonly IPublishEndpoint _publishEndpoint;
        private readonly AppOptions _globalData;
        private readonly IActivityService _activityService;

        private readonly SupportedGatewayRegistry _supportedGatewayRegistry;


        public GatewayController(PostgreSQLDbContext context,
            ITransactionService transactionService,
            IOptions<AppOptions> globalData,
            IMerchantServices merchantServices,
            ISpreedlyService spreedlyService,
            IPublishEndpoint publishEndpoint,
            IActivityService activityService,
            SupportedGatewayRegistry supportedGatewayRegistry)
        {
            _context = context;
            _transactionService = transactionService;
            _merchantServices = merchantServices;
            _spreedlyService = spreedlyService;
            _publishEndpoint = publishEndpoint;
            _globalData = globalData.Value;
            _activityService = activityService;

            _supportedGatewayRegistry = supportedGatewayRegistry;
        }

        // [HttpPost]
        // [Authorize(policy: MyPolicies.SUPER_ADMINS_ONLY)]
        // [ProducesResponseType(typeof(Guid), StatusCodes.Status200OK)]
        // [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        // [ProducesResponseType(StatusCodes.Status404NotFound)]
        // [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        // public async Task<IActionResult> Post(GatewayDTO payload, CancellationToken token)
        // {
        //     using var workspan = Workspan.Start<GatewayController>();
        //     try
        //     {
        //         workspan.Log.Information(
        //             "ENTERED: {GlobalDataName} => {HttpMethod} {RequestQueryString} | Request: {Payload}",
        //             _globalData.Name, HttpContext.Request.Method,
        //             HttpContext.Request.Path + HttpContext.Request.QueryString, JsonConvert.SerializeObject(payload));
        //
        //         if (!ModelState.IsValid) return ValidationProblem();
        //
        //         if (payload.GatewayType.ToUpper() == "NMI")
        //         {
        //             await _context.SupportedGateways.AddAsync(new SupportedGateway
        //             {
        //                 Name = payload.Name,
        //                 User = payload.User,
        //                 Password = payload.Password,
        //                 Sandbox = payload.IsSandbox,
        //                 AuthenticationType = AuthenticationType.UserPassword.ToString()
        //             }, token);
        //         }
        //         else
        //         {
        //             await _context.SupportedGateways.AddAsync(new SupportedGateway
        //             {
        //                 Name = payload.Name,
        //                 SecretKey = payload.SecretKey,
        //                 Sandbox = payload.IsSandbox,
        //                 AuthenticationType = AuthenticationType.ApiKey.ToString()
        //             }, token);
        //         }
        //         // switch (payload.AuthenticationType)
        //         // {
        //         //     case AuthenticationType.Basic:
        //         //         await _context.SupportedGateways.AddAsync(new SupportedGateway
        //         //         {
        //         //             Name = payload.Name,
        //         //             User = payload.User,
        //         //             Password = payload.Password,
        //         //             Sandbox = payload.IsSandbox,
        //         //             AuthenticationType = payload.AuthenticationType.ToString()
        //         //         }, token);
        //         //         break;
        //         //     case AuthenticationType.Oauth2:
        //         //         await _context.SupportedGateways.AddAsync(new SupportedGateway
        //         //         {
        //         //             Name = payload.Name,
        //         //             User = payload.User,
        //         //             Password = payload.Password,
        //         //             SecretKey = payload.SecretKey,
        //         //             Sandbox = payload.IsSandbox,
        //         //             AuthenticationType = payload.AuthenticationType.ToString()
        //         //         }, token);
        //         //         break;
        //         //     case AuthenticationType.UserPassword:
        //         //         await _context.SupportedGateways.AddAsync(new SupportedGateway
        //         //         {
        //         //             Name = payload.Name,
        //         //             User = payload.User,
        //         //             Password = payload.Password,
        //         //             Sandbox = payload.IsSandbox,
        //         //             AuthenticationType = payload.AuthenticationType.ToString()
        //         //         }, token);
        //         //         break;
        //         //     case AuthenticationType.ApiKey:
        //         //         await _context.SupportedGateways.AddAsync(new SupportedGateway
        //         //         {
        //         //             Name = payload.Name,
        //         //             SecretKey = payload.SecretKey,
        //         //             Sandbox = payload.IsSandbox,
        //         //             AuthenticationType = payload.AuthenticationType.ToString()
        //         //         }, token);
        //         //         break;
        //         //     case AuthenticationType.UnKnown:
        //         //     default:
        //         //         throw new ArgumentOutOfRangeException();
        //         // }
        //
        //
        //         await _context.SaveChangesAsync(token);
        //
        //         return Ok();
        //     }
        //     catch (Exception e)
        //     {
        //         workspan.Log.Error(e,
        //             "EXCEPTION: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
        //             _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
        //             HttpContext.Request.QueryString);
        //
        //         return StatusCode(StatusCodes.Status500InternalServerError, "Failed creating a record");
        //     }
        // }


        [HttpPost]
        [Authorize(policy: MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(Guid), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> Post(Guid gatewayId, Guid mid, CancellationToken token)
        {
            using var workspan = Workspan.Start<GatewayController>()
                .Baggage("gatewayId", gatewayId)
                .Baggage("mid", mid)
                .LogEnterAndExit();

            try
            {
                if (!ModelState.IsValid)
                    return ValidationProblem();

                var gatewayInformation =
                    await _context.SupportedGateways.SingleOrDefaultAsync(x => x.Id == gatewayId, token);
                if (gatewayInformation == null)
                    throw new FlexValidationException("Gateway not found");

                var merchant = await _context.Merchants
                    .FirstOrDefaultAsync(x => x.Mid == mid, token);
                if (merchant == null)
                    throw new FlexValidationException("Merchant not found");

                var merchantGateways = await _context.Gateways
                    .OrderByDescending(x => x.Order)
                    .Where(x => x.MerchantId == merchant.Id)
                    .ToListAsync(token);

                if (merchantGateways.Any() && merchantGateways.Any(x => x.SupportedGatewayId == gatewayInformation.Id))
                    throw new FlexValidationException(
                        "Gateway already exists for this merchant");

                var gatewayRequest = MapGatewayRequest(gatewayInformation);
                Gateway newGateway;
                if (gatewayInformation.IsOrchestratorGateway)
                {
                    if (merchant.SpreedlyEnvironmentKey == null || merchant.SpreedlySecretKey == null)
                    {
                        var envResponse = await _spreedlyService.AddEnvironmentAsync(new NewEnvironmentRequest()
                        {
                            environment = new SpreedlyEnvironment()
                            {
                                allow_direct_api_payment_method_creation = true,
                                smart_routing_enabled = true,
                                name =
                                    $"{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")}_{merchant.Dba}_{merchant.Mid}",
                            }
                        }, merchant.Mid, token);

                        merchant.SpreedlyEnvironmentKey = envResponse.environment.key;

                        var secretResponse = await _spreedlyService.AddEnvironmentSecretAsync(
                            envResponse.environment.key,
                            new EnvironmentSecretRequest()
                            {
                                access_secret = new SpreedlyAccessSecret()
                                {
                                    name = "default"
                                }
                            }, merchant.Mid, token);

                        merchant.SpreedlySecretKey = secretResponse.access_secret.access_secret;
                    }

                    _context.Merchants.Update(merchant);

                    var addGatewayResponse = new AddGatewayResponse();
                    addGatewayResponse = await _spreedlyService.AddGatewayAsync(merchant.SpreedlyEnvironmentKey,
                        merchant.SpreedlySecretKey,
                        merchant.Mid, gatewayRequest, token);

                    if (!addGatewayResponse.Success)
                    {
                        await _publishEndpoint.Publish(new GatewayAddingFailedEvent
                        {
                            MerchantId = merchant.Mid
                        }, token);
                        return BadRequest("Failed creating a record");
                    }

                    newGateway = new Gateway()
                    {
                        Name = addGatewayResponse.gateway.gateway_type,
                        NameIdentifier = addGatewayResponse.gateway.gateway_type,
                        Identifier = addGatewayResponse.gateway.token,
                        ProcessorId = gatewayInformation.ProcessorId,
                        SupportedGatewayId = gatewayInformation.Id,
                        MCC = gatewayInformation.MCC,
                        Order = merchantGateways.Any() ? merchantGateways.Max(x => x.Order) + 1 : 0,
                        Default = false,
                        MerchantId = merchant.Id
                    };
                    await _context.AddAsync(newGateway, token);
                }
                else
                {
                    newGateway = new Gateway()
                    {
                        Name = gatewayInformation.Name,
                        Identifier = gatewayInformation.Identifier,
                        NameIdentifier = gatewayInformation.NameIdentifier,
                        ProcessorId = gatewayInformation.ProcessorId,
                        SupportedGatewayId = gatewayInformation.Id,
                        MCC = gatewayInformation.MCC,
                        Order = merchantGateways.Any() ? merchantGateways.Max(x => x.Order) + 1 : 0,
                        Default = false,
                        MerchantId = merchant.Id
                    };
                    await _context.AddAsync(newGateway, token);
                }

                await _context.SaveChangesAsync(token);
                await _publishEndpoint.Publish(new GatewayCreatedEvent
                {
                    MerchantId = merchant.Mid,
                    GatewayId = newGateway.Id,
                    TokenizationPublicKey = merchant.SpreedlyEnvironmentKey,
                }, token);

                return Ok(newGateway.Id);
            }
            catch (FlexValidationException vex)
            {
                ModelState.AddModelError(vex.Property, vex.Message);
                return ValidationProblem(ModelState);
            }
            catch (Exception e)
            {
                workspan.Log.Error(e,
                    "EXCEPTION: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);

                return StatusCode(StatusCodes.Status500InternalServerError, "Failed creating a record");
            }
        }

        private static AddGatewayRequest MapGatewayRequest(SupportedGateway gatewayInformation)
        {
            var gatewayRequest = new AddGatewayRequest();
            gatewayRequest.gateway = gatewayInformation.NameIdentifier switch
            {
                "test" => new SpreedlyGateway
                {
                    sandbox = gatewayInformation.Sandbox
                },
                "nmi" => new NmiGateway()
                {
                    sandbox = gatewayInformation.Sandbox,
                    login = gatewayInformation.User,
                    password = gatewayInformation.Password
                },
                "nmi_v2" => new NmiV2Gateway()
                {
                    sandbox = gatewayInformation.Sandbox,
                },
                "stripe_payment_intents" => new StripeGateway()
                {
                    sandbox = gatewayInformation.Sandbox,
                    login = gatewayInformation.SecretKey
                },
                "safe_charge" => new SafeChargeGateway()
                {
                    sandbox = gatewayInformation.Sandbox,
                    client_login_id = gatewayInformation.User,
                    client_password = gatewayInformation.Password
                },
                "nuvei" => new NuveiGateway()
                {
                    sandbox = gatewayInformation.Sandbox,
                    merchantId = gatewayInformation.User,
                    siteId = gatewayInformation.Configuration,
                    secretKey = gatewayInformation.SecretKey
                },
                "flexcharge_dummy" => new FlexChargeDummyGateway()
                {
                    sandbox = gatewayInformation.Sandbox,
                    merchantId = gatewayInformation.User,
                    siteId = gatewayInformation.Configuration,
                    secretKey = gatewayInformation.SecretKey
                },
                "flexcharge_dummy2" => new FlexChargeDummyGateway2()
                {
                    sandbox = gatewayInformation.Sandbox,
                    merchantId = gatewayInformation.User,
                    siteId = gatewayInformation.Configuration,
                    secretKey = gatewayInformation.SecretKey
                },
                "svb" => new SVBGateway()
                {
                    sandbox = gatewayInformation.Sandbox,
                    merchantId = gatewayInformation.User,
                    siteId = gatewayInformation.Configuration,
                },
                "checkout" => new CheckoutGateway()
                {
                    sandbox = gatewayInformation.Sandbox,
                    secretKey = gatewayInformation.SecretKey,
                    ///todo
                },

                _ => gatewayRequest.gateway
            };
            return gatewayRequest;
        }

        [HttpPut]
        [Authorize(policy: MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> Put(GatewayUpdateDTO payload, CancellationToken token)
        {
            using var workspan = Workspan.Start<GatewayController>()
                .Baggage("gatewayId", payload.Id)
                .Baggage("mid", payload.MerchantId)
                .LogEnterAndExit();

            try
            {
                if (!ModelState.IsValid)
                    return ValidationProblem();

                var updateCandidates = await _context.Gateways.Where(
                        x => x.MerchantId == payload.MerchantId)
                    .Include(x => x.SupportedGateway)
                    .ToListAsync(token);

                if (payload.Order > updateCandidates.Count)
                    payload.Order = updateCandidates.Count;

                var updateCandidate = updateCandidates.FirstOrDefault(x => x.Id == payload.Id);

                var updateCandidateOrder = updateCandidate.Order;

                updateCandidates = updateCandidates
                    .Select(x =>
                    {
                        if (x.Id == payload.Id)
                        {
                            x.Default = payload.IsDefault;

                            if (x.SupportedGateway.IsActive == false && payload.IsActive == true)
                                throw new FlexValidationException("isActive",
                                    "Cannot activate a gateway when supported gateway is inactive");

                            x.IsActive = payload.IsActive;

                            x.Order = payload.Order;

                            if (x.Capabilities == null)
                                x.Capabilities = new CapabilitiesModel();

                            if (payload.Capabilities?.CardBrands != null)
                                x.Capabilities.CardBrands = payload.Capabilities.CardBrands;

                            if (payload.Capabilities?.SupportCIT != null)
                                x.Capabilities.SupportCIT = payload.Capabilities.SupportCIT;

                            if (payload.Capabilities?.SupportMIT != null)
                                x.Capabilities.SupportMIT = payload.Capabilities.SupportMIT;
                        }
                        else if (updateCandidateOrder > payload.Order && x.Order < updateCandidateOrder &&
                                 x.Order >= payload.Order)
                        {
                            x.Order += 1;
                        }
                        else if (updateCandidateOrder < payload.Order && x.Order > updateCandidateOrder &&
                                 x.Order <= payload.Order)
                        {
                            x.Order -= 1;
                        }

                        return x;
                    }).ToList();

                _context.Gateways.UpdateRange(updateCandidates);
                await _context.SaveChangesAsync(token);

                return Ok();
            }
            catch (FlexValidationException vex)
            {
                ModelState.AddModelError(vex.Property, vex.Message);
                return ValidationProblem(ModelState);
            }
            catch (Exception e)
            {
                workspan.Log.Error(e,
                    $"EXCEPTION: POST {_globalData.Name} => API ERROR {HttpContext.Request.Path + HttpContext.Request.QueryString} ");
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed creating a record");
            }
        }

        [HttpGet()] // GET ALL
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(QueryMerchantGatewaysDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> Get(Guid Mid, CancellationToken token)
        {
            using var workspan = Workspan.Start<GatewayController>()
                .Baggage("mid", Mid)
                .LogEnterAndExit();

            try
            {
                return Ok(await _context.Gateways
                    .Include(x => x.SupportedGateway)
                    .AsSplitQuery()
                    .OrderBy(x => x.Order)
                    .Where(x => x.Merchant.Mid == Mid).Select(x =>
                        new QueryMerchantGatewaysDTO
                        {
                            Id = x.Id,
                            Name = x.SupportedGateway.Name,
                            MCC = x.SupportedGateway.MCC,
                            MidNumber = x.SupportedGateway.MidNumber,
                            ProcessorId = x.SupportedGateway.ProcessorId,
                            MerchantId = x.MerchantId,
                            ProviderId = x.SupportedGatewayId,
                            Order = x.Order,
                            IsActive = x.IsActive,
                            IsSandbox = x.SupportedGateway.Sandbox,
                            IsDefault = x.Default,
                            Capabilities = new CapabilitiesModel
                            {
                                CardBrands =
                                    x.Capabilities != null &&
                                    x.Capabilities.CardBrands != null
                                        ? x.Capabilities.CardBrands
                                        : x.SupportedGateway.Capabilities.CardBrands,

                                PaymentTypes = x.Capabilities != null &&
                                               !string.IsNullOrEmpty(x.Capabilities.PaymentTypes)
                                    ? x.Capabilities.PaymentTypes
                                    : x.SupportedGateway.Capabilities.PaymentTypes,

                                SupportedCurrencies = x.Capabilities != null &&
                                                      !string.IsNullOrEmpty(x.Capabilities.SupportedCurrencies)
                                    ? x.Capabilities.SupportedCurrencies
                                    : x.SupportedGateway.Capabilities.SupportedCurrencies,

                                SupportedCountries = x.Capabilities != null &&
                                                     !string.IsNullOrEmpty(x.Capabilities.SupportedCountries)
                                    ? x.Capabilities.SupportedCountries
                                    : x.SupportedGateway.Capabilities.SupportedCountries,

                                SupportMIT = x.Capabilities != null && x.Capabilities.SupportMIT != null
                                    ? x.Capabilities.SupportMIT
                                    : x.SupportedGateway.Capabilities.SupportMIT,

                                SupportCIT = x.Capabilities != null && x.Capabilities.SupportCIT != null
                                    ? x.Capabilities.SupportCIT
                                    : x.SupportedGateway.Capabilities.SupportCIT,

                                Support3DS = x.Capabilities != null && x.Capabilities.Support3DS != null
                                    ? x.Capabilities.Support3DS
                                    : x.SupportedGateway.Capabilities.Support3DS,

                                Support3DS2 = x.Capabilities != null && x.Capabilities.Support3DS2 != null
                                    ? x.Capabilities.Support3DS2
                                    : x.SupportedGateway.Capabilities.Support3DS2,

                                SupportTokenization =
                                    x.Capabilities != null && x.Capabilities.SupportTokenization != null
                                        ? x.Capabilities.SupportTokenization
                                        : x.SupportedGateway.Capabilities.SupportTokenization,

                                SupportNetworkTokenization = x.Capabilities != null &&
                                                             x.Capabilities.SupportNetworkTokenization != null
                                    ? x.Capabilities.SupportNetworkTokenization
                                    : x.SupportedGateway.Capabilities.SupportNetworkTokenization,

                                SupportNetworkTokenization3DS = x.Capabilities != null &&
                                                                x.Capabilities.SupportNetworkTokenization3DS != null
                                    ? x.Capabilities.SupportNetworkTokenization3DS
                                    : x.SupportedGateway.Capabilities.SupportNetworkTokenization3DS,

                                MasterCard_MonthlyMaxTransactionsCount = x.Capabilities != null &&
                                                                         x.Capabilities
                                                                             .MasterCard_MonthlyMaxTransactionsCount !=
                                                                         null
                                    ? x.Capabilities.MasterCard_MonthlyMaxTransactionsCount
                                    : x.SupportedGateway.Capabilities.MasterCard_MonthlyMaxTransactionsCount,

                                MasterCard_MonthlyMaxValue = x.Capabilities != null &&
                                                             x.Capabilities.MasterCard_MonthlyMaxValue != null
                                    ? x.Capabilities.MasterCard_MonthlyMaxValue
                                    : x.SupportedGateway.Capabilities.MasterCard_MonthlyMaxValue,


                                Visa_MonthlyMaxTransactionsCount = x.Capabilities != null &&
                                                                   x.Capabilities
                                                                       .Visa_MonthlyMaxTransactionsCount !=
                                                                   null
                                    ? x.Capabilities.Visa_MonthlyMaxTransactionsCount
                                    : x.SupportedGateway.Capabilities.Visa_MonthlyMaxTransactionsCount,

                                Visa_MonthlyMaxValue = x.Capabilities != null &&
                                                       x.Capabilities.Visa_MonthlyMaxValue != null
                                    ? x.Capabilities.Visa_MonthlyMaxValue
                                    : x.SupportedGateway.Capabilities.Visa_MonthlyMaxValue,


                                TotalMonthlyMaxTransactionsValue = x.Capabilities != null &&
                                                                   x.Capabilities.TotalMonthlyMaxTransactionsValue !=
                                                                   null
                                    ? x.Capabilities.TotalMonthlyMaxTransactionsValue
                                    : x.SupportedGateway.Capabilities.TotalMonthlyMaxTransactionsValue,

                                TotalMonthlyMaxTransactionsCount = x.Capabilities != null &&
                                                                   x.Capabilities.TotalMonthlyMaxTransactionsCount !=
                                                                   null
                                    ? x.Capabilities.TotalMonthlyMaxTransactionsCount
                                    : x.SupportedGateway.Capabilities.TotalMonthlyMaxTransactionsCount,

                                TotalDailyMaxTransactionsCount = x.Capabilities != null &&
                                                                 x.Capabilities.TotalDailyMaxTransactionsCount !=
                                                                 null
                                    ? x.Capabilities.TotalDailyMaxTransactionsCount
                                    : x.SupportedGateway.Capabilities.TotalDailyMaxTransactionsCount,

                                TotalDailyMaxTransactionsValue = x.Capabilities != null &&
                                                                 x.Capabilities.TotalDailyMaxTransactionsValue !=
                                                                 null
                                    ? x.Capabilities.TotalDailyMaxTransactionsValue
                                    : x.SupportedGateway.Capabilities.TotalDailyMaxTransactionsValue,
                            },
                        }).ToListAsync(cancellationToken: token));
            }
            catch (Exception e)
            {
                workspan.Log.Error(e,
                    "EXCEPTION: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);

                return StatusCode(StatusCodes.Status500InternalServerError, "Failed fetching records");
            }
        }

        [HttpGet("GetSupportedGatewayTypes")]
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(string), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetSupportedGatewayTypes(Guid partnerId, bool? isActive,
            CancellationToken token)
        {
            using var workspan = Workspan.Start<GatewayController>()
                .LogEnterAndExit();
            try
            {
                var providers = _context.SupportedGateways
                    .OrderBy(x => x.Name)
                    .Include(x => x.Meta)
                    .AsQueryable();

                if (isActive != null)
                    providers = providers.Where(x => x.IsActive == isActive);

                if (partnerId != Guid.Empty)
                    providers = providers.Where(x => x.PartnerId == partnerId);

                var response = await providers.Select(x =>
                    new
                    {
                        Name = $"{x.Name} {x.ProcessorId} (MCC:{x.MCC}) {(x.Sandbox ? "(Sandbox)" : string.Empty)}",
                        NameIdentifier = x.NameIdentifier,
                        Id = x.Id
                    }
                ).ToListAsync(cancellationToken: token);

                return Ok(response);
            }
            catch (Exception e)
            {
                workspan.Log.Error(e,
                    "EXCEPTION: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);

                return StatusCode(StatusCodes.Status500InternalServerError, "Failed Get SupportedGatewayTypes");
            }
        }

        [HttpGet("GetEnvironmentKey")]
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS_AND_MERCHANT_ADMINS_AND_MERCHANT_DEVELOPER)]
        [ProducesResponseType(typeof(string), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetEnvironmentKey(Guid? mid, CancellationToken token)
        {
            using var workspan = Workspan.Start<GatewayController>()
                .Baggage("mid", mid)
                .LogEnterAndExit();

            try
            {
                string spreedlyEnvironmentKey;

                if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN))
                {
                    if (mid is null)
                        throw new FlexValidationException("Merchant Id is required");

                    spreedlyEnvironmentKey = await _merchantServices.GetSpreedlyEnvironmentKey(mid.Value);
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN) ||
                         HttpContext.IsUserInGroup(SuperAdminGroups.INTEGRATION_PARTNER_ADMIN))
                {
                    if (mid is null)
                        throw new FlexValidationException("Merchant Id is required");

                    var pid = GetPID();
                    if (pid == null || pid == Guid.Empty)
                        return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");

                    spreedlyEnvironmentKey =
                        await _merchantServices.PartnerGetSpreedlyEnvironmentKey(mid.Value, GetPID().Value);
                }
                else
                {
                    var pid = GetPID();

                    if (pid is null)
                        throw new FlexValidationException("Partner Id is required");

                    spreedlyEnvironmentKey = await _merchantServices.GetSpreedlyEnvironmentKey(GetMID());
                }

                return Ok(spreedlyEnvironmentKey);
            }
            catch (FlexValidationException vex)
            {
                ModelState.AddModelError(vex.Property, vex.Message);
                return ValidationProblem();
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                    set => set
                        .TenantId(mid));

                return StatusCode(StatusCodes.Status500InternalServerError, "Failed fetching key");
            }
        }
    }
}