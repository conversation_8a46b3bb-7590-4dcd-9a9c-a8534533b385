using FlexCharge.Common;
using FlexCharge.Common.Authentication;
using FlexCharge.Payments.DTO;
using FlexCharge.Payments.Entities;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Common.Exports;
using FlexCharge.Common.Telemetry;
using FlexCharge.Common.Validation.FluentValidation;
using FlexCharge.Payments.Activities;
using FlexCharge.Payments.Entities.JsonbModels;
using FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels;
using FlexCharge.Utils;
using Microsoft.Extensions.DependencyInjection;
using OpenTelemetry;

namespace FlexCharge.Payments.Controllers
{
    [Route("[controller]")]
    [ApiController]
    [JwtAuth]
    public class ProvidersController : BaseController
    {
        public PostgreSQLDbContext _context { get; set; }
        private readonly AppOptions _globalData;
        private readonly IActivityService _activityService;
        private readonly IServiceProvider _serviceProvider;

        public ProvidersController(PostgreSQLDbContext context,
            IOptions<AppOptions> globalData,
            IActivityService activityService, IServiceProvider serviceProvider)
        {
            _context = context;
            _globalData = globalData.Value;
            _activityService = activityService;
            _serviceProvider = serviceProvider;
        }


        [HttpGet]
        [Authorize(MyPolicies.ADMINS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(QueryMerchantGatewaysDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetSupportedProviders(Guid? partnerId, bool? isActive, CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<GatewayController>(this, null, _globalData)
                .Baggage("partnerId", partnerId)
                .Baggage("isActive", isActive)
                .LogEnterAndExit();

            try
            {
                var providers = _context.SupportedGateways
                    .OrderBy(x => x.Name)
                    .Include(x => x.Metrics)
                    .Include(x => x.Meta)
                    .AsQueryable();

                if (isActive != null)
                    providers = providers.Where(x => x.IsActive == isActive);

                if (HttpContext.IsUserInGroup(SuperAdminGroups.SUPER_ADMIN))
                {
                    if (partnerId != null)
                        providers = providers.Where(x => x.PartnerId == partnerId);
                }
                else if (HttpContext.IsUserInGroup(SuperAdminGroups.PARTNER_ADMIN) ||
                         HttpContext.IsUserInGroup(SuperAdminGroups.INTEGRATION_PARTNER_ADMIN))
                {
                    var pid = GetPID();

                    if (pid == null || pid == Guid.Empty)
                    {
                        return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                    }

                    providers = providers.Where(x => x.PartnerId == pid);
                }
                else
                {
                    return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                }

                List<QueryPaymentProvidersDTO> response = new List<QueryPaymentProvidersDTO>();

                var alertProviders = await _context.AlertProviders.ToListAsync(token);

                foreach (var x in await providers.ToListAsync())
                {
                    CycleMetrics providerMetrics = x.Metrics;

                    var gateways = await _context.Gateways
                        .Where(gateway => gateway.SupportedGatewayId == x.Id && gateway.IsActive)
                        .Select(x => x.MerchantId).ToListAsync(cancellationToken: token);
                    var merchants = await _context.Merchants.Where(merchant => gateways.Contains(merchant.Id))
                        .Select(x => new RelatedMerchantsDTO
                        {
                            Id = x.Mid,
                            Dba = x.Dba,
                            Status = x.IsActive ? "Active" : "Inactive"
                        })
                        .ToListAsync(cancellationToken: token);

                    response.Add(
                        new QueryPaymentProvidersDTO()
                        {
                            Name = x.Name,
                            Id = x.Id,
                            AuthenticationType = x.AuthenticationType,
                            SecretKeyMasked = string.IsNullOrEmpty(x.SecretKey)
                                ? string.Empty
                                : $"...{x.SecretKey.GetLast(4)}",
                            User = string.IsNullOrEmpty(x.User) ? string.Empty : x.User,
                            PasswordMasked = string.IsNullOrEmpty(x.Password)
                                ? string.Empty
                                : x.Password.Mask(0, x.Password.Length, '*'),
                            Sandbox = x.Sandbox,
                            IsActive = x.IsActive,
                            Methods = x.Methods ?? x.Methods,
                            Capabilities = x.Capabilities ?? x.Capabilities,
                            CycleMetrics = providerMetrics,
                            MCC = x.MCC,
                            FixedDescriptorPrefix = x.FixedDescriptorPrefix,
                            MasterCardDescriptorPrefix = x.MasterCardDescriptorPrefix,
                            VisaDescriptorPrefix = x.VisaDescriptorPrefix,
                            ProcessorId = x.ProcessorId,
                            ProcessorPlatform = x.ProcessorPlatform,
                            MerchantNumber = x.MidNumber,
                            ProviderLogo = x.Meta != null ? x.Meta.LogoIcon : null,
                            ProviderLogoAlt = x.Meta != null ? x.Meta.Name : null,
                            PartnerId = x.PartnerId,
                            ContactName = x.ContactName,
                            ContactEmail = x.ContactEmail,
                            ContactPhone = x.ContactPhone,
                            SponsorBank = x.SponsorBank,
                            CAID = x.CAID,
                            BIN = x.BIN,
                            EthocaProvider = alertProviders.FirstOrDefault(p => p.Id == x.EthocaProvider)?.Name,
                            RdrProvider = alertProviders.FirstOrDefault(p => p.Id == x.RdrProvider)?.Name,
                            ChargebacksManagementProvider = alertProviders
                                .FirstOrDefault(p => p.Id == x.ChargebacksManagementProvider)?.Name,
                            RelatedMerchants = merchants,
                            Tier = x.Tier
                        });
                }

                return Ok(response);
            }
            catch (Exception e)
            {
                workspan.Log.Error(e,
                    "EXCEPTION: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);
                await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                    set => set
                        .CorrelationId(GetMID()));

                return StatusCode(StatusCodes.Status500InternalServerError, "Failed fetching records");
            }
        }

        [HttpPut]
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(QueryMerchantGatewaysDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> UpdateSupportedProviders(UpdatePaymentProvidersDTO payload,
            CancellationToken token)
        {
            using var workspan = Workspan.Start<GatewayController>().LogEnterAndExit();

            try
            {
                var updateCandidate =
                    await _context.SupportedGateways
                        .SingleOrDefaultAsync(x => x.Id == payload.Id,
                            cancellationToken: token);

                if (payload.IsActive != null && payload.IsActive.Value == false)
                {
                    var gateways = await _context.Gateways.Where(x => x.SupportedGatewayId == payload.Id && x.IsActive)
                        .Select(x => x.MerchantId).ToListAsync();
                    var merchants = await _context.Merchants.Where(x => gateways.Contains(x.Id))
                        .Select(x => x.Mid)
                        .ToListAsync();

                    if (merchants.Any())
                        return BadRequest(new
                            {message = "This provider is already in use by one or more merchants", merchants});
                }

                if (updateCandidate == null)
                    return NotFound();

                if (payload.Name != null)
                {
                    if (updateCandidate.Name != payload.Name)
                    {
                        var gatewaysExists = await _context.Gateways.AnyAsync(x => x.Name == payload.Name);
                        if (gatewaysExists)
                            return BadRequest("Gateway name already exists");
                    }

                    updateCandidate.Name = payload.Name;
                }

                if (payload.ProcessorId != null)
                    updateCandidate.ProcessorId = payload.ProcessorId;

                if (payload.ProcessorPlatform != null)
                    updateCandidate.ProcessorPlatform = payload.ProcessorPlatform;

                if (payload.SponsorBank != null)
                    updateCandidate.SponsorBank = payload.SponsorBank;

                if (payload.BIN != null)
                    updateCandidate.BIN = payload.BIN;

                if (payload.CAID != null)
                    updateCandidate.CAID = payload.CAID;

                if (payload.ContactName != null)
                    updateCandidate.ContactName = payload.ContactName;

                if (payload.ContactEmail != null)
                    updateCandidate.ContactEmail = payload.ContactEmail;

                if (payload.ContactPhone != null)
                    updateCandidate.ContactPhone = payload.ContactPhone;

                if (payload.MerchantNumber != null)
                    updateCandidate.MidNumber = payload.MerchantNumber;

                if (payload.MCC != null)
                    updateCandidate.MCC = payload.MCC.Length == 0 ? null : payload.MCC;

                if (payload.Tier != null)
                    updateCandidate.Tier = payload.Tier;

                if (payload.MasterCardDescriptorPrefix != null)
                    updateCandidate.MasterCardDescriptorPrefix = payload.MasterCardDescriptorPrefix.Length == 0
                        ? null
                        : payload.MasterCardDescriptorPrefix;

                if (payload.VisaDescriptorPrefix != null)
                    updateCandidate.VisaDescriptorPrefix = payload.VisaDescriptorPrefix.Length == 0
                        ? null
                        : payload.VisaDescriptorPrefix;

                if (payload.IsActive != null)
                    updateCandidate.IsActive = payload.IsActive.Value;

                if (payload.Sandbox != null)
                    updateCandidate.Sandbox = payload.Sandbox.Value;

                // var validator = new CapabilitiesModelValidator();
                // var result = await validator.ValidateAsync(payload.Capabilities, token);
                //
                // if (!result.IsValid)
                // {
                //     foreach (var error in result.Errors)
                //         ModelState.AddModelError(error.PropertyName, error.ErrorMessage);
                //     
                //     return ValidationProblem(ModelState);
                // }

                if (payload.Capabilities?.SupportedCountries != null)
                {
                    if (payload.Capabilities.SupportedCountries.Length == 0)
                    {
                        return BadRequest("At least one country should be supported");
                    }

                    updateCandidate.Capabilities.SupportedCountries =
                        payload.Capabilities.SupportedCountries;
                }


                if (payload.Capabilities?.SupportedCurrencies != null)
                {
                    if (payload.Capabilities.SupportedCurrencies.Length == 0)
                    {
                        return BadRequest("At least one currency should be supported");
                    }

                    updateCandidate.Capabilities.SupportedCurrencies =
                        payload.Capabilities.SupportedCurrencies;
                }

                if (payload.Capabilities?.UseDynamicDescriptor != null)
                    updateCandidate.Capabilities.UseDynamicDescriptor = payload.Capabilities.UseDynamicDescriptor;

                if (payload.Capabilities?.SupportDynamicDescriptorAddress != null)
                {
                    if (!updateCandidate.Capabilities.UseDynamicDescriptor.HasValue ||
                        !updateCandidate.Capabilities.UseDynamicDescriptor.Value)
                    {
                        ModelState.AddModelError("Capabilities.SupportDynamicDescriptorAddress",
                            "Dynamic descriptor must be enabled to support dynamic descriptor address");
                        return ValidationProblem(ModelState);
                    }

                    updateCandidate.Capabilities.SupportDynamicDescriptorAddress =
                        payload.Capabilities.SupportDynamicDescriptorAddress;
                }

                if (payload.Capabilities?.SupportDynamicDescriptorUrl != null)
                {
                    if (!updateCandidate.Capabilities.UseDynamicDescriptor.HasValue ||
                        !updateCandidate.Capabilities.UseDynamicDescriptor.Value)
                    {
                        ModelState.AddModelError("Capabilities.SupportDynamicDescriptorUrl",
                            "Dynamic descriptor must be enabled to support dynamic descriptor url");
                        return ValidationProblem(ModelState);
                    }

                    updateCandidate.Capabilities.SupportDynamicDescriptorUrl =
                        payload.Capabilities.SupportDynamicDescriptorUrl;
                }

                if (payload.Capabilities?.UseDynamicDescriptorSuffix != null)
                {
                    if (!updateCandidate.Capabilities.UseDynamicDescriptor.HasValue ||
                        !updateCandidate.Capabilities.UseDynamicDescriptor.Value)
                    {
                        return BadRequest("Dynamic descriptor must be enabled to support dynamic descriptor suffix");
                    }

                    updateCandidate.Capabilities.UseDynamicDescriptorSuffix =
                        payload.Capabilities.UseDynamicDescriptorSuffix;
                }

                if (payload.Capabilities?.SupportUDF != null)
                    updateCandidate.Capabilities.SupportUDF = payload.Capabilities.SupportUDF;

                if (payload.Capabilities?.SupportCIT != null)
                    updateCandidate.Capabilities.SupportCIT = payload.Capabilities.SupportCIT;

                if (payload.Capabilities?.SupportMIT != null)
                    updateCandidate.Capabilities.SupportMIT = payload.Capabilities.SupportMIT;


                if (payload.Capabilities?.CardBrands != null)
                    updateCandidate.Capabilities.CardBrands = payload.Capabilities.CardBrands.Length == 0
                        ? null
                        : payload.Capabilities.CardBrands;


                if (payload.Capabilities?.MasterCard_MonthlyMaxTransactionsCount != null)
                    updateCandidate.Capabilities.MasterCard_MonthlyMaxTransactionsCount =
                        payload.Capabilities.MasterCard_MonthlyMaxTransactionsCount < 0
                            ? null
                            : payload.Capabilities.MasterCard_MonthlyMaxTransactionsCount;

                if (payload.Capabilities?.Visa_MonthlyMaxTransactionsCount != null)
                    updateCandidate.Capabilities.Visa_MonthlyMaxTransactionsCount =
                        payload.Capabilities.Visa_MonthlyMaxTransactionsCount < 0
                            ? null
                            : payload.Capabilities.Visa_MonthlyMaxTransactionsCount;


                if (payload.Capabilities?.MasterCard_MonthlyMaxValue != null)
                    updateCandidate.Capabilities.MasterCard_MonthlyMaxValue =
                        payload.Capabilities?.MasterCard_MonthlyMaxValue < 0
                            ? null
                            : payload.Capabilities?.MasterCard_MonthlyMaxValue;


                if (payload.Capabilities?.Visa_MonthlyMaxValue != null)
                    updateCandidate.Capabilities.Visa_MonthlyMaxValue =
                        payload.Capabilities?.Visa_MonthlyMaxValue < 0
                            ? null
                            : payload.Capabilities?.Visa_MonthlyMaxValue;


                if (payload.Capabilities?.TotalMonthlyMaxTransactionsValue != null)
                    updateCandidate.Capabilities.TotalMonthlyMaxTransactionsValue =
                        payload.Capabilities?.TotalMonthlyMaxTransactionsValue < 0
                            ? null
                            : payload.Capabilities?.TotalMonthlyMaxTransactionsValue;

                if (payload.Capabilities?.TotalMonthlyMaxTransactionsCount != null)
                    updateCandidate.Capabilities.TotalMonthlyMaxTransactionsCount =
                        payload.Capabilities?.TotalMonthlyMaxTransactionsCount < 0
                            ? null
                            : payload.Capabilities?.TotalMonthlyMaxTransactionsCount;

                if (payload.Capabilities?.TotalDailyMaxTransactionsCount != null)
                    updateCandidate.Capabilities.TotalDailyMaxTransactionsCount =
                        payload.Capabilities?.TotalDailyMaxTransactionsCount < 0
                            ? null
                            : payload.Capabilities?.TotalDailyMaxTransactionsCount;

                if (payload.Capabilities?.TotalDailyMaxTransactionsValue != null)
                    updateCandidate.Capabilities.TotalDailyMaxTransactionsValue =
                        payload.Capabilities?.TotalDailyMaxTransactionsValue < 0
                            ? null
                            : payload.Capabilities?.TotalDailyMaxTransactionsValue;


                _context.SupportedGateways.Update(updateCandidate);
                var res = await _context.SaveChangesAsync(token);

                if (res > 0)
                {
                    return Ok(payload);
                }

                return BadRequest("Failed updating record");
            }
            catch (Exception e)
            {
                workspan.Log.Error(e,
                    "EXCEPTION: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);

                await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                    set => set
                        .CorrelationId(GetMID())
                        .Data(payload));

                return StatusCode(StatusCodes.Status500InternalServerError, "Failed fetching records");
            }
        }

        [HttpGet("export")] // GET ALL
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(QueryMerchantGatewaysDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> ExportSupportedProviders(CancellationToken token, bool? isActive)
        {
            using var workspan = Workspan.Start<GatewayController>().LogEnterAndExit();

            try
            {
                var providers = _context
                    .SupportedGateways
                    .Where(x => x.IsActive == isActive)
                    .OrderBy(x => x.CreatedOn)
                    .Include(x => x.Metrics)
                    .AsQueryable();

                if (isActive != null)
                    providers = providers.Where(x => x.IsActive == isActive);

                var filteredProviders = await providers
                    .Select(x => new ExportQueryPaymentProvidersDTO()
                    {
                        Id = x.Id,
                        Name = x.Name,
                        ProcessorId = x.ProcessorId,
                        MCC = x.MCC,
                        Sandbox = x.Sandbox,
                        IsActive = x.IsActive,
                        Methods = x.Methods ?? x.Methods,
                        Capabilities = x.Capabilities ?? x.Capabilities,
                        FixedDescriptorPrefix = x.FixedDescriptorPrefix,
                        MasterCardDescriptorPrefix = x.MasterCardDescriptorPrefix,
                        VisaDescriptorPrefix = x.VisaDescriptorPrefix,

                        MasterCardCurrentTransactionsCount = x.Metrics.MasterCardCurrentTransactionsCount,
                        MasterCardCurrentTransactionsValue = x.Metrics.MasterCardCurrentTransactionsValue,
                        CycleStartDate = x.Metrics != null ? x.Metrics.CycleStartDate : null,
                        CycleEndDate = x.Metrics != null ? x.Metrics.CycleEndDate : null,
                        VisaCurrentTransactionsCount =
                            x.Metrics != null ? x.Metrics.VisaCurrentTransactionsCount : null,
                        VisaCurrentTransactionsValue =
                            x.Metrics != null ? x.Metrics.VisaCurrentTransactionsValue : null,
                        TotalTransactionAmount = x.Metrics != null ? x.Metrics.TotalTransactionAmount : null,
                        TotalTransactionCount = x.Metrics != null ? x.Metrics.TotalTransactionCount : null
                    }).ToListAsync(cancellationToken: token);

                var csv = CSVExport.GenerateCSVFromRows(filteredProviders);

                return Ok(csv);
            }
            catch (Exception e)
            {
                workspan.Log.Error(e,
                    "EXCEPTION: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);

                await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                    set => set
                        .CorrelationId(GetMID()));

                return StatusCode(StatusCodes.Status500InternalServerError, "Failed fetching records");
            }
        }


        [HttpPost("verify-descriptor")]
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> VerifyDescriptor(Guid supportedGatewayId, CreditCardDTO card,
            CancellationToken token)
        {
            try
            {
                var supportedGateway = await _context.SupportedGateways
                    .Where(x => x.Id == supportedGatewayId)
                    .SingleOrDefaultAsync(token);

                if (supportedGateway == null)
                    return NotFound();

                //payments service invoke sale
                var paymentProviderResolver = GetRequiredService<ServiceCollectionExtensions.PaymentProviderResolver>();
                var paymentProvider = paymentProviderResolver(supportedGateway.NameIdentifier);

                var orderId = Guid.NewGuid();
                var payerId = Guid.NewGuid();

                NameHelpers.TrySplitToFirstAndLastName(card.NameOnCard, out var firstName, out var lastName);

                var saleResult = await paymentProvider.SaleAsync(new SaleRequest
                {
                    Gateway = new Gateway()
                    {
                        SupportedGatewayId = supportedGateway.Id,
                        Name = supportedGateway.Name,
                        NameIdentifier = supportedGateway.NameIdentifier,
                        ProcessorId = supportedGateway.ProcessorId,
                        //IsSandbox = supportedGateway.Sandbox,
                        SupportedGateway = supportedGateway,
                        Capabilities = supportedGateway.Capabilities
                    },
                    Amount = 100,
                    CurrencyCode = "840",
                    OrderId = orderId,
                    PayerId = payerId,
                    Descriptor = new DescriptorDTO()
                    {
                        Name = "FLFX* valid descriptor",
                        Url = "https://www.flexfactor.io",
                        Country = "840"
                    },
                    BillingAddress = new BillingAddress()
                    {
                        Zip = "95035",
                        Address1 = "691S Blvd, Ste 212",
                        Country = "USA",
                        City = "Milpitas",
                        State = "CA"
                    },
                    CreditCard = new SaleRequestCreditCard
                    {
                        FirstName = firstName,
                        LastName = lastName,
                        Number = card.Number,
                        VerificationValue = card.VerificationValue,
                        Month = int.Parse(card.Month),
                        Year = int.Parse(card.Year)
                    }
                });


                return Ok(saleResult);
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        [HttpPost("{supportedGatewayId:guid}/quarantine-release")]
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> VerifyDescriptor(Guid supportedGatewayId, CancellationToken token)
        {
            using var workspan = Workspan.Start<GatewayController>()
                .Baggage("supportedGatewayId", supportedGatewayId)
                .LogEnterAndExit();

            try
            {
                var supportedGateway = await _context.SupportedGateways.Include(x => x.Metrics)
                    .Where(x => x.Id == supportedGatewayId)
                    .SingleOrDefaultAsync(token);

                if (supportedGateway == null)
                    return NotFound();

                if (supportedGateway.Metrics.Quarantine_AddedOn.HasValue)
                    supportedGateway.Metrics.Quarantine_AddedOn = null;

                _context.SupportedGateways.Update(supportedGateway);
                var res = await _context.SaveChangesAsync(token);

                if (res > 0)
                {
                    return Ok();
                }

                return BadRequest("Failed releasing quarantine");
            }
            catch (Exception e)
            {
                workspan.Log.Error(e,
                    "EXCEPTION: {GlobalDataName} => {RequestMethod} {RequestPath} {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Method, HttpContext.Request.Path,
                    HttpContext.Request.QueryString);

                throw;
            }
        }

        protected T GetRequiredService<T>()
        {
            return _serviceProvider.GetRequiredService<T>();
        }
    }
}