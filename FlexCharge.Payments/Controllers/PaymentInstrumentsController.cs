using AutoMapper;
using FlexCharge.Common;
using FlexCharge.Common.Authentication;
using FlexCharge.Payments.DTO;
using FlexCharge.Payments.Services.PaymentInstrumentsServices;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Common.SensitiveData.Guards;
using FlexCharge.Common.Telemetry;
using FlexCharge.Common.Telemetry.HttpRequests;
using FlexCharge.Contracts.Commands;
using FlexCharge.Contracts.Commands.Vault;
using FlexCharge.Payments.Activities;
using FlexCharge.Payments.Entities;
using FlexCharge.Payments.Services.SpreedlyService;
using MassTransit;
using Microsoft.EntityFrameworkCore;

namespace FlexCharge.Payments.Controllers
{
    [Route("[controller]")]
    [ApiController]
    [JwtAuth]
    public class PaymentInstrumentsController : BaseController
    {
        private readonly AppOptions _globalData;
        private readonly IMapper _mapper;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IPaymentInstrumentsService _paymentInstrumentService;
        private readonly IRequestClient<TokenizeInstrumentCommand> _tokenizeInstrumentRequestClient;
        private readonly IRequestClient<RecacheVerificationValueCommand> _recacheVerificationValueRequestClient;
        private readonly PostgreSQLDbContext _dbContext;
        private readonly IRequestClient<BinCheckCommand> _bincheckCommandClient;
        private readonly IActivityService _activityService;

        public PaymentInstrumentsController(
            IPaymentInstrumentsService paymentInstrumentService,
            IMapper mapper,
            IOptions<AppOptions> globalData,
            IHttpContextAccessor httpContextAccessor,
            IRequestClient<TokenizeInstrumentCommand> tokenizeInstrumentRequestClient,
            IRequestClient<RecacheVerificationValueCommand> recacheVerificationValueRequestClient,
            PostgreSQLDbContext dbContext, IRequestClient<BinCheckCommand> bincheckCommandClient,
            IActivityService activityService)
        {
            _paymentInstrumentService = paymentInstrumentService;
            _mapper = mapper;
            _httpContextAccessor = httpContextAccessor;
            _tokenizeInstrumentRequestClient = tokenizeInstrumentRequestClient;
            _recacheVerificationValueRequestClient = recacheVerificationValueRequestClient;
            _globalData = globalData.Value;
            _dbContext = dbContext;
            _bincheckCommandClient = bincheckCommandClient;
            _activityService = activityService;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <param name="token"></param>
        /// <returns></returns>
        [HttpPost("tokenize")]
        [AllowAnonymous]
        [SensitiveDataPublicApiTelemetry(null, typeof(CreatePaymentInstrumentResponse),
            LogRequest = false)]
        [ProducesResponseType(typeof(CreatePaymentInstrumentResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> Tokenize(Guid? mid, string environment, CreatePaymentInstrumentRequest payload,
            CancellationToken token)
        {
            using var workspan = Workspan.Start<PaymentInstrumentsController>()
                .Baggage("Mid", mid)
                .Baggage("Environment", environment)
                .LogEnterAndExit();

            _httpContextAccessor.AddCorrelation(mid, null);

            try
            {
                SensitiveDataGuard.IsTestCreditCardGuard(payload?.PaymentMethod?.CreditCard?.Number);
            }
            catch (SecurityException e)
            {
                workspan.Log.Error(e, e.Message);
                ModelState.AddModelError("number", e.Message);
            }
            catch (Exception e)
            {
                workspan.Log.Error(e, e.Message);
                ModelState.AddModelError("number", e.Message);
            }

            //if (Guid.Empty == mid) ModelState.AddModelError("mid", "Merchant ID is required");
            //if (environment == null) ModelState.AddModelError("environment_key", "Environment key is required");

            if (!ModelState.IsValid)
            {
                await _activityService.CreateActivityAsync(ApiErrorActivities.ModelStateError, ModelState,
                    set => set
                        .TenantId(mid));
                return ValidationProblem();
            }

            try
            {
                Merchant merchant = null;
                if (mid.HasValue)
                {
                    merchant = await _dbContext.Merchants.SingleAsync(m => m.Mid == mid.Value);
                }


                var tokenizeTask = _tokenizeInstrumentRequestClient.GetResponse<TokenizeInstrumentCommandResponse>(
                    new TokenizeInstrumentCommand
                    {
                        Mid = mid,
                        SenseKey = payload.PaymentMethod.SenseKey,
                        FirstName = payload.PaymentMethod.CreditCard.FirstName,
                        LastName = payload.PaymentMethod.CreditCard.LastName,
                        Number = payload.PaymentMethod.CreditCard.Number,
                        VerificationValue = payload.PaymentMethod.CreditCard.VerificationValue,
                        Month = int.Parse(payload.PaymentMethod.CreditCard.Month),
                        Year = int.Parse(payload.PaymentMethod.CreditCard.Year),
                        Company = payload.PaymentMethod.CreditCard.Company,
                        Address1 = payload.PaymentMethod.CreditCard.Address1,
                        Address2 = payload.PaymentMethod.CreditCard.Address2,
                        City = payload.PaymentMethod.CreditCard.City,
                        State = payload.PaymentMethod.CreditCard.State,
                        Zip = payload.PaymentMethod.CreditCard.Zip,
                        Country = payload.PaymentMethod.CreditCard.Country,
                        PhoneNumber = payload.PaymentMethod.CreditCard.PhoneNumber,
                        ShippingAddress1 = payload.PaymentMethod.CreditCard.ShippingAddress1,
                        ShippingAddress2 = payload.PaymentMethod.CreditCard.ShippingAddress2,
                        ShippingCity = payload.PaymentMethod.CreditCard.ShippingCity,
                        ShippingState = payload.PaymentMethod.CreditCard.ShippingState,
                        ShippingZip = payload.PaymentMethod.CreditCard.ShippingZip,
                        ShippingCountry = payload.PaymentMethod.CreditCard.ShippingCountry,
                        ShippingPhoneNumber = payload.PaymentMethod.CreditCard.ShippingPhoneNumber,
                        Email = payload.PaymentMethod.Email,
                        EnableGlobalNetworkTokenization = merchant is null
                            ? false
                            : merchant.EnableGlobalNetworkTokenization
                    }, token);

                var tasks = new List<Task>();
                tasks.Add(tokenizeTask);

                Task<Response<BinCheckCommandResponse>> binCheckTask = null;
                if (merchant != null)
                {
                    //var merchant = await _dbContext.Merchants.SingleAsync(m => m.Mid == mid.Value);

                    if (merchant.AllowBinCheckOnTokenization)
                    {
                        binCheckTask = _bincheckCommandClient.GetResponse<BinCheckCommandResponse>(new BinCheckCommand()
                            {
                                BinNumber = payload.PaymentMethod.CreditCard.Number.Substring(0, 8),
                                LocalOnly = false
                            }
                        );

                        tasks.Add(binCheckTask);
                    }
                }

                await Task.WhenAll(tasks.ToArray());

                var res = tokenizeTask.Result;

                var tokenizeInstrumentCommandResponse = res.Message;

                workspan
                    .Baggage("PaymentInstrumentToken", tokenizeInstrumentCommandResponse.Id);

                var result = new CreatePaymentInstrumentResponse
                {
                    Transaction = new TransactionResponse
                    {
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow,
                        Succeeded = true,
                        OnTestGateway = !EnvironmentHelper.IsInProduction,
                        PaymentMethod = new PaymentMethod()
                        {
                            Token = tokenizeInstrumentCommandResponse.Id.ToString(),
                            Fingerprint = tokenizeInstrumentCommandResponse.Fingerprint,
                            LastFourDigits = tokenizeInstrumentCommandResponse.Last4,
                            FirstSixDigits = tokenizeInstrumentCommandResponse.Bin,
                            Month = tokenizeInstrumentCommandResponse.ExpirationMonth,
                            Year = tokenizeInstrumentCommandResponse.ExpirationYear,
                            CreatedAt = DateTime.UtcNow,
                            UpdatedAt = DateTime.UtcNow,
                            Address1 = tokenizeInstrumentCommandResponse.BillingAddress?.Address1,
                            Address2 = tokenizeInstrumentCommandResponse.BillingAddress?.Address2,
                            City = tokenizeInstrumentCommandResponse.BillingAddress?.City,
                            State = tokenizeInstrumentCommandResponse.BillingAddress?.StateCode,
                            Country = tokenizeInstrumentCommandResponse.BillingAddress?.CountryCode,
                            Zip = tokenizeInstrumentCommandResponse.BillingAddress?.Zip,
                            FirstName = tokenizeInstrumentCommandResponse.CardHolderFirstName,
                            LastName = tokenizeInstrumentCommandResponse.CardHolderLastName,
                            Email = tokenizeInstrumentCommandResponse.Email,
                            PhoneNumber = tokenizeInstrumentCommandResponse.PhoneNumber
                        }
                    }
                };

                if (binCheckTask != null)
                {
                    var binResponse = binCheckTask.Result;
                    var binResult = binResponse.Message;
                    if (binResult is not null)
                        result.BinInfo = new BinInfo()
                        {
                            Country = binResult.Country,
                            BankName = binResult.BankName,
                            BankPhone = binResult.BankPhone,
                            BankWebsite = binResult.BankWebsite,
                            CardIssuer = binResult.CardIssuer,
                            CardLevel = binResult.CardLevel,
                            CardNetwork = binResult.CardNetwork,
                            CardType = binResult.CardType,
                            CountryCode = binResult.CountryCode
                        };
                }

                return Ok(result);
            }
            catch (Exception e)
            {
                workspan.Log.Error(e, "EXCEPTION: POST {GlobalDataName} => LOCAL VAULT => ERROR {RequestQueryString} ",
                    _globalData.Name, HttpContext.Request.Path + HttpContext.Request.QueryString);
                await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                    set => set
                        .TenantId(mid));

                return StatusCode(StatusCodes.Status500InternalServerError, "Failed tokenizing a record");
            }
        }

        [HttpPost("recache-cvv")]
        [AllowAnonymous]
        [SensitiveDataPublicApiTelemetry(null, typeof(RecacheVerificationValueCommandResponse),
            LogRequest = false)]
        [ProducesResponseType(typeof(RecacheVerificationValueCommandResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> RecacheCvv(Guid? mid,
            string environment,
            string paymentInstrumentToken,
            RecachePaymentMethodRequest payload,
            CancellationToken token)
        {
            using var workspan = Workspan.Start<PaymentInstrumentsController>()
                .Baggage("Mid", mid)
                .Baggage("Environment", environment)
                .Baggage("PaymentInstrumentToken", paymentInstrumentToken)
                .LogEnterAndExit();

            try
            {
                //if (Guid.Empty == mid) ModelState.AddModelError("mid", "Merchant ID is required");
                // if (environment == null) ModelState.AddModelError("environment_key", "Environment key is required");

                if (paymentInstrumentToken == null)
                    ModelState.AddModelError("paymentInstrumentToken", "paymentInstrumentToken is required");

                if (payload == null) ModelState.AddModelError("cvv", "cvv is required");

                if (payload.payment_method.credit_card.verification_value is {Length: > 4})
                    ModelState.AddModelError("cvv", "cvv is must be 3 or 4 digits");

                if (!payload.payment_method.credit_card.verification_value.All(char.IsDigit))
                    ModelState.AddModelError("cvv", "cvv is must be digits");

                if (!ModelState.IsValid)
                {
                    await _activityService.CreateActivityAsync(ApiErrorActivities.ModelStateError, ModelState,
                        set => set
                            .TenantId(mid));
                    return ValidationProblem();
                }

                var response =
                    await _recacheVerificationValueRequestClient.GetResponse<RecacheVerificationValueCommandResponse>(
                        new RecacheVerificationValueCommand
                        {
                            VaultId = Guid.Parse(paymentInstrumentToken),
                            VerificationValue = payload.payment_method.credit_card.verification_value
                        }, token);

                return Ok(response);
            }
            catch (Exception e)
            {
                workspan.Log.Error(e, "EXCEPTION: POST {GlobalDataName} => API ERROR {RequestQueryString} ",
                    _globalData.Name, HttpContext.Request.Path + HttpContext.Request.QueryString);
                await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                    set => set
                        .TenantId(mid));
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed Recaching CVV");
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <param name="token"></param>
        /// <returns></returns>
        [HttpPost]
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(CreatePaymentInstrumentDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> Post(Guid mid, CreateNewPaymentInstrumentDTO request, CancellationToken token)
        {
            using var workspan = Workspan.Start<PaymentInstrumentsController>();

            try
            {
                workspan.Log.Information("ENTERED: {GlobalDataName} => POST {RequestQueryString} | {SerializeObject}",
                    _globalData?.Name, HttpContext.Request.Path + HttpContext.Request.QueryString,
                    JsonConvert.SerializeObject(request));

                if (!ModelState.IsValid)
                {
                    await _activityService.CreateActivityAsync(ApiErrorActivities.ModelStateError, ModelState,
                        set => set
                            .TenantId(mid));
                    return ValidationProblem();
                }

                var result =
                    await _paymentInstrumentService.SavePaymentMethodAsync(mid, request, request.Tokenized, true,
                        token);

                workspan.Log.Information("EXIT: {GlobalDataName} => POST {RequestQueryString}  {SerializeObject}",
                    _globalData?.Name, HttpContext.Request.Path + HttpContext.Request.QueryString,
                    JsonConvert.SerializeObject(request));

                return ReturnResponse(result);
            }
            catch (Exception e)
            {
                workspan.Log.Error(e, "EXCEPTION: POST {GlobalDataName} => API ERROR {RequestQueryString} ",
                    _globalData.Name, HttpContext.Request.Path + HttpContext.Request.QueryString);
                await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                    set => set
                        .TenantId(mid));

                ModelState.AddModelError("general", "Failed creating a record");
                return StatusCode(StatusCodes.Status500InternalServerError, ModelState);
            }
        }

        [HttpGet()] // GET ALL
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(IEnumerable<QueryPaymentInstrumentDTO>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> Get(CancellationToken token)
        {
            using var workspan = Workspan.Start<PaymentInstrumentsController>();

            try
            {
                workspan.Log.Information("ENTERED: {GlobalDataName} => GET {RequestQueryString}", _globalData.Name,
                    HttpContext.Request.Path + HttpContext.Request.QueryString);

                var paymentInstruments =
                    await _paymentInstrumentService.GetPaymentMethodsAsync(GetMID(), GetUID(), token);

                workspan.Log.Information("EXIT: {GlobalDataName} => GET {RequestQueryString}", _globalData.Name,
                    HttpContext.Request.Path + HttpContext.Request.QueryString);

                return Ok(_mapper.Map<List<QueryPaymentInstrumentDTO>>(paymentInstruments));
            }
            catch (Exception e)
            {
                workspan.Log.Error(e, "EXCEPTION: {GlobalDataName} => GET API ERROR {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Path + HttpContext.Request.QueryString);
                await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                    set => set
                        .TenantId(GetMID()));

                ModelState.AddModelError("general", "Failed fetching records");
                return StatusCode(StatusCodes.Status500InternalServerError, ModelState);
            }
        }


        [HttpGet()] // GET BY ID
        [Route("{id:guid}")]
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(QueryPaymentInstrumentDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> Get([FromRoute] Guid id, CancellationToken token)
        {
            using var workspan = Workspan.Start<PaymentInstrumentsController>();

            try
            {
                workspan.Log.Information("ENTERED: {GlobalDataName} => GET {RequestQueryString} ", _globalData.Name,
                    HttpContext.Request.Path + HttpContext.Request.QueryString);

                var instrument = _paymentInstrumentService.GetMethodByIdAsync(GetMID(), GetUID(), id, token);

                workspan.Log.Information("EXIT: {GlobalDataName} => GET {RequestQueryString} ", _globalData.Name,
                    HttpContext.Request.Path + HttpContext.Request.QueryString);

                return Ok(_mapper.Map<QueryPaymentInstrumentDTO>(instrument));
            }
            catch (Exception e)
            {
                workspan.Log.Error(e, "EXCEPTION: GET contacts API ERROR {RequestQueryString} ",
                    HttpContext.Request.Path + HttpContext.Request.QueryString);
                await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                    set => set
                        .TenantId(GetMID()));

                ModelState.AddModelError("general", "Failed fetching a record");
                return StatusCode(StatusCodes.Status500InternalServerError, ModelState);
            }
        }

        [HttpGet()] // GET BY TOKEN
        [Route("{token}/token")]
        [Authorize(MyPolicies.ADMINS_AND_ALL_MERCHANTS_AND_PARTNER_ADMINS)]
        [ProducesResponseType(typeof(PaymentInstrumentByTokenDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetByToken([FromRoute] string token, CancellationToken cancellationToken)
        {
            using var workspan = Workspan.Start<PaymentInstrumentsController>();

            try
            {
                workspan.Log.Information("ENTERED: {GlobalDataName} => GET {RequestQueryString} ", _globalData.Name,
                    HttpContext.Request.Path + HttpContext.Request.QueryString);

                PaymentInstrument paymentInstrument;
                if (UserInRole(SuperAdminGroups.SUPER_ADMIN))
                {
                    paymentInstrument = await _paymentInstrumentService.AdminGetByTokenAsync(token, cancellationToken);
                }
                else if (UserInRole(SuperAdminGroups.PARTNER_ADMIN) ||
                         UserInRole(SuperAdminGroups.INTEGRATION_PARTNER_ADMIN))
                {
                    var pid = GetPID();

                    if (pid == null || pid == Guid.Empty)
                    {
                        return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                    }

                    paymentInstrument =
                        await _paymentInstrumentService.GetByTokenAsync(token, null, pid, cancellationToken);
                }
                else if (UserInRole(SuperAdminGroups.MERCHANT_ADMIN) ||
                         UserInRole(MerchantGroups.MERCHANT_SUPPORT) ||
                         UserInRole(MerchantGroups.MERCHANT_SUPPORT_ADMIN) ||
                         UserInRole(MerchantGroups.MERCHANT_FINANCE) ||
                         UserInRole(MerchantGroups.MERCHANT_DEVELOPER))
                {
                    var mid = GetMID();

                    if (mid == null || mid == Guid.Empty)
                    {
                        return StatusCode(StatusCodes.Status403Forbidden, "Forbidden");
                    }

                    paymentInstrument =
                        await _paymentInstrumentService.GetByTokenAsync(token, mid, null, cancellationToken);
                }
                else
                {
                    return Unauthorized();
                }

                if (paymentInstrument == null)
                {
                    return NotFound();
                }

                workspan.Log.Information("EXIT: {GlobalDataName} => GET {RequestQueryString} ", _globalData.Name,
                    HttpContext.Request.Path + HttpContext.Request.QueryString);

                return Ok(_mapper.Map<PaymentInstrument, PaymentInstrumentByTokenDTO>(paymentInstrument));
            }
            catch (Exception e)
            {
                workspan.Log.Error(e, "EXCEPTION: GET contacts API ERROR {RequestQueryString} ",
                    HttpContext.Request.Path + HttpContext.Request.QueryString);
                await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                    set => set
                        .TenantId(GetMID()));

                ModelState.AddModelError("general", "Failed fetching a record");
                return StatusCode(StatusCodes.Status500InternalServerError, ModelState);
            }
        }


        [HttpPut] // UPDATE
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(UpdatePaymentInstrumentResultDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> Put(UpdatePaymentInstrumentDTO request, CancellationToken token)
        {
            using var workspan = Workspan.Start<PaymentInstrumentsController>();

            try
            {
                workspan.Log.Information("ENTERED: {GlobalDataName} => PUT {RequestQueryString} ", _globalData.Name,
                    HttpContext.Request.Path + HttpContext.Request.QueryString);

                if (!ModelState.IsValid)
                {
                    await _activityService.CreateActivityAsync(ApiErrorActivities.ModelStateError, ModelState,
                        set => set
                            .TenantId(GetMID()));
                    return ValidationProblem();
                }

                workspan.Log.Information("EXIT: {GlobalDataName} => PUT {RequestQueryString} ", _globalData.Name,
                    HttpContext.Request.Path + HttpContext.Request.QueryString);

                return Ok();
            }
            catch (Exception e)
            {
                workspan.Log.Error(e, "EXCEPTION: PUT contacts API ERROR {RequestQueryString} ",
                    HttpContext.Request.Path + HttpContext.Request.QueryString);
                await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                    set => set
                        .TenantId(GetMID()));

                ModelState.AddModelError("general", "Failed updating record");
                return StatusCode(StatusCodes.Status500InternalServerError, ModelState);
            }
        }


        [HttpDelete("{id}")]
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> Delete([FromRoute] Guid id, CancellationToken token)
        {
            using var workspan = Workspan.Start<PaymentInstrumentsController>();

            try
            {
                workspan.Log.Information("ENTERED: {GlobalDataName} => => DELETE {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Path + HttpContext.Request.QueryString);

                await _paymentInstrumentService.DeleteAsync(GetMID(), id, token);

                workspan.Log.Information("EXIT: {GlobalDataName} => DELETE {RequestQueryString}", _globalData.Name,
                    HttpContext.Request.Path + HttpContext.Request.QueryString);

                return Ok();
            }
            catch (Exception e)
            {
                workspan.Log.Error(e, "EXCEPTION: {GlobalDataName} => DELETE API ERROR {RequestQueryString}",
                    _globalData.Name, HttpContext.Request.Path + HttpContext.Request.QueryString);
                await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                    set => set
                        .TenantId(GetMID()));

                ModelState.AddModelError("general", "Failed deleting record");
                return StatusCode(StatusCodes.Status500InternalServerError, ModelState);
            }
        }

        protected bool UserInRole(string group)
        {
            return HttpContext.User.Claims.Any(x => x.Type == MyClaimTypes.COGNITO_GROUP && x.Value == group);
        }
    }
}