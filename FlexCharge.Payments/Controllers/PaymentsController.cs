using AutoMapper;
using FlexCharge.Common;
using FlexCharge.Common.Authentication;
using FlexCharge.Common.Authentication.Filters;
using FlexCharge.Common.Dependencies;
using FlexCharge.Common.Emails;
using FlexCharge.Common.Sms;
using FlexCharge.Payments.Authorization;
using FlexCharge.Payments.DTO;
using FlexCharge.Payments.Entities;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Common.Authentication.BasicAuthentication;
using FlexCharge.Common.Telemetry;
using FlexCharge.Payments.Activities;
using FlexCharge.Payments.Services.PaymentServices;
using FlexCharge.Payments.Services.PaymentServices.Interfaces;
using FlexCharge.Payments.Services.PaymentServices.Models;
using FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels;

namespace FlexCharge.Payments.Controllers
{
    [Route("[controller]")]
    [ApiController]
    [Authorize(AuthenticationSchemes = AuthenticationSchemas.MixSchemaNames)]
    // [JwtAuth]
    public class PaymentsController : BaseController
    {
        private readonly PostgreSQLDbContext _context;
        private readonly AppOptions _globalData;
        private readonly IPaymentOrchestrator _paymentOrchestrator;
        private readonly IMapper _mapper;
        private readonly ILogger<PaymentsController> _logger;
        private readonly IActivityService _activityService;

        public PaymentsController(
            IMapper mapper,
            ILogger<PaymentsController> logger,
            IOptions<AppOptions> globalData,
            PostgreSQLDbContext context,
            IPaymentOrchestrator paymentOrchestrator,
            IActivityService activityService)
        {
            _mapper = mapper;
            _logger = logger;
            _context = context;
            _paymentOrchestrator = paymentOrchestrator;
            _globalData = globalData.Value;
            _activityService = activityService;
        }

        [HttpPost("sale")]
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(TransactionCreateDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> Post(SaleRequest request, Guid mid, CancellationToken token)
        {
            try
            {
                _logger.LogInformation("ENTERED: {GlobalDataName} => POST {RequestQueryString} | {SerializeObject}",
                    _globalData?.Name, HttpContext.Request.Path + HttpContext.Request.QueryString,
                    JsonConvert.SerializeObject(request));

                if (!ModelState.IsValid)
                {
                    await _activityService.CreateActivityAsync(ApiErrorActivities.ModelStateError, ModelState,
                        set => set
                            .TenantId(mid)
                            .CorrelationId(request.OrderId)
                            .Data(request));
                    return ValidationProblem();
                }

                request.Mid = mid;

                var response = await _paymentOrchestrator.SaleAsync(request, token: token);

                _logger.LogInformation("EXIT: {GlobalDataName} => POST {RequestQueryString}  {SerializeObject}",
                    _globalData?.Name, HttpContext.Request.Path + HttpContext.Request.QueryString,
                    JsonConvert.SerializeObject(request));

                if (response.Success)
                    return Ok(response);

                return BadRequest(response);
            }
            catch (Exception e)
            {
                _logger.LogError(e,
                    $"EXCEPTION: POST {_globalData.Name} => API ERROR {HttpContext.Request.Path + HttpContext.Request.QueryString} ");
                await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                    set => set
                        .TenantId(mid)
                        .CorrelationId(request.OrderId)
                        .Data(request));

                return StatusCode(StatusCodes.Status500InternalServerError, "Failed SaleAsync");
            }
        }

        [HttpPost("authorize")]
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(AuthResult), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> PostAuthorize(AuthorizationRequest request, Guid mid, CancellationToken token)
        {
            try
            {
                _logger.LogInformation(
                    $"ENTERED: {_globalData?.Name} => POST {HttpContext.Request.Path + HttpContext.Request.QueryString} | {JsonConvert.SerializeObject(request)}");

                if (!ModelState.IsValid)
                {
                    await _activityService.CreateActivityAsync(ApiErrorActivities.ModelStateError, ModelState,
                        set => set
                            .TenantId(mid)
                            .CorrelationId(request.OrderId)
                            .Data(request));
                    return ValidationProblem();
                }

                var response = await _paymentOrchestrator.AuthorizeAsync(request, token: token);

                _logger.LogInformation(
                    $"EXIT: {_globalData?.Name} => POST {HttpContext.Request.Path + HttpContext.Request.QueryString}  {JsonConvert.SerializeObject(request)}");

                if (response.Success)
                    return Ok(response);

                return BadRequest(response);
            }
            catch (Exception e)
            {
                _logger.LogError(e,
                    $"EXCEPTION: POST {_globalData.Name} => API ERROR {HttpContext.Request.Path + HttpContext.Request.QueryString} ");
                await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                    set => set
                        .TenantId(mid)
                        .CorrelationId(request.OrderId)
                        .Data(request));

                return StatusCode(StatusCodes.Status500InternalServerError, "Failed SaleAsync");
            }
        }

        [HttpPost("capture")]
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(CapturePaymentRequest), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> PostCapture(Guid transactionId, Guid mid, CancellationToken token)
        {
            _logger.LogInformation(
                $"ENTERED: PaymentsService > CapturePaymentCommandConsumer > Mid: {mid}");
            try
            {
                var relatedTransaction =
                    await _context.Transactions.SingleOrDefaultAsync(x => x.Id == transactionId);

                var gateway =
                    await _paymentOrchestrator.GetGatewayAsync(mid, relatedTransaction.ProviderId, null, null,
                        token: token);

                var captureResponse = await _paymentOrchestrator.CaptureAsync(new CapturePaymentRequest()
                {
                    Mid = mid,
                    TransactionId = transactionId
                }, token);

                return Ok(captureResponse);
            }
            catch (Exception e)
            {
                _logger.LogError(e, $"EXCEPTION: Failed CapturePaymentCommandConsumer payment method");
                await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                    set => set
                        .TenantId(mid)
                        .Meta(meta => meta.SetValue("TransactionId", transactionId)));

                return BadRequest();
            }
        }

        [HttpPost("verify")]
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(VerifyInstrumentResult), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> PostVerify(VerifyInstrumentRequest request, Guid mid, CancellationToken token)
        {
            try
            {
                _logger.LogInformation(
                    $"ENTERED: {_globalData?.Name} => POST {HttpContext.Request.Path + HttpContext.Request.QueryString} | {JsonConvert.SerializeObject(request)}");

                if (!ModelState.IsValid)
                {
                    await _activityService.CreateActivityAsync(ApiErrorActivities.ModelStateError, ModelState,
                        set => set
                            .TenantId(mid)
                            .CorrelationId(request.OrderId)
                            .Data(request));
                    return ValidationProblem();
                }

                request.Mid = mid;

                var response = await _paymentOrchestrator.VerifyAsync(request, token: token);

                _logger.LogInformation(
                    $"EXIT: {_globalData?.Name} => POST {HttpContext.Request.Path + HttpContext.Request.QueryString}  {JsonConvert.SerializeObject(request)}");

                if (response.Success)
                    return Ok(response);

                return BadRequest(response);
            }
            catch (Exception e)
            {
                _logger.LogError(e,
                    $"EXCEPTION: POST {_globalData.Name} => API ERROR {HttpContext.Request.Path + HttpContext.Request.QueryString} ");
                await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                    set => set
                        .TenantId(mid)
                        .CorrelationId(request.OrderId)
                        .Data(request));

                return StatusCode(StatusCodes.Status500InternalServerError, "Failed SaleAsync");
            }
        }

        [HttpGet()] // GET ALL
        [ProducesResponseType(typeof(TransactionQueryDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> Get(TransactionQueryDTO payload, CancellationToken token)
        {
            try
            {
                _logger.LogInformation(
                    $"ENTERED: {_globalData.Name} => GET {HttpContext.Request.Path + HttpContext.Request.QueryString}");

                var transactions = await _context.Transactions.ToPagedListAsync(payload.PageNumber, payload.PageSize);

                _logger.LogInformation(
                    $"EXIT: {_globalData.Name} => GET {HttpContext.Request.Path + HttpContext.Request.QueryString}");

                return Ok(_mapper.Map<List<TransactionQueryDTO>>(transactions));
            }
            catch (Exception e)
            {
                _logger.LogError(e,
                    $"EXCEPTION: {_globalData.Name} => GET API ERROR {HttpContext.Request.Path + HttpContext.Request.QueryString}");
                await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                    set => set
                        .TenantId(GetMID())
                        .Data(payload));

                return StatusCode(StatusCodes.Status500InternalServerError, "Failed fetching records");
            }
        }

        [HttpPost("refund")]
        [Authorize(MyPolicies.ADMINS_ONLY)]
        [ProducesResponseType(typeof(CreditPaymentResult), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> RefundPost(CreditPaymentRequest request, Guid mid, CancellationToken token)
        {
            try
            {
                _logger.LogInformation(
                    $"ENTERED: {_globalData?.Name} => POST {HttpContext.Request.Path + HttpContext.Request.QueryString} | {JsonConvert.SerializeObject(request)}");

                if (!ModelState.IsValid)
                {
                    await _activityService.CreateActivityAsync(ApiErrorActivities.ModelStateError, ModelState,
                        set => set
                            .TenantId(mid)
                            .CorrelationId(request.OrderId)
                            .Data(request));
                    return ValidationProblem();
                }

                ICreditPaymentResult response;
                if (HttpContext.User.Claims.Any(x =>
                        x.Type == MyClaimTypes.COGNITO_GROUP && x.Value == SuperAdminGroups.SUPER_ADMIN))
                    request.Mid = mid;
                else if (HttpContext.User.Claims.Any(x =>
                             x.Type == MyClaimTypes.COGNITO_GROUP && x.Value == SuperAdminGroups.PARTNER_ADMIN))
                    request.Mid = GetMID();
                else
                    request.Mid = GetMID();

                response = await _paymentOrchestrator.CreditAsync(request, token: token);


                _logger.LogInformation(
                    $"EXIT: {_globalData?.Name} => POST {HttpContext.Request.Path + HttpContext.Request.QueryString}  {JsonConvert.SerializeObject(request)}");

                if (response.Success)
                    return Ok(response);

                return BadRequest(response);
            }
            catch (Exception e)
            {
                _logger.LogError(e,
                    $"EXCEPTION: POST {_globalData.Name} => API ERROR {HttpContext.Request.Path + HttpContext.Request.QueryString} ");
                await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                    set => set
                        .TenantId(mid)
                        .CorrelationId(request.OrderId)
                        .Data(request));

                return StatusCode(StatusCodes.Status500InternalServerError, "Failed Refund");
            }
        }

        [HttpPost("cancelrefund")]
        [Authorize(MyPolicies.ADMINS_ONLY)]
        [ProducesResponseType(typeof(CreditPaymentResult), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> CancelRefundPost(Guid refundTransactionId, CancellationToken token)
        {
            try
            {
                var transaction =
                    await _context.Transactions.SingleOrDefaultAsync(x => x.Id == refundTransactionId, token);

                ///if (!transaction.Status) cjeck complteted -todo

                CancelRefundRequest request = new()
                {
                    Mid = transaction.Merchant.Id,
                    OrderId = transaction.OrderId,
                    RefundTransactionId = refundTransactionId,
                    GatewayId = transaction.ProviderId,
                    ProviderToken = transaction.ProviderTransactionToken,
                    IsStandaloneRefund = transaction.ParentId == default,
                    Amount = transaction.Amount
                };

                _logger.LogInformation(
                    $"ENTERED: {_globalData?.Name} => POST {HttpContext.Request.Path + HttpContext.Request.QueryString} | {JsonConvert.SerializeObject(request)}");

                if (!ModelState.IsValid)
                {
                    await _activityService.CreateActivityAsync(ApiErrorActivities.ModelStateError, ModelState,
                        set => set
                            .TenantId(request.Mid)
                            .CorrelationId(request.OrderId)
                            .Data(request));
                    return ValidationProblem();
                }

                IRefundCancelResult response;


                response = await _paymentOrchestrator.CancelRefundAsync(request, token);

                _logger.LogInformation(
                    $"EXIT: {_globalData?.Name} => POST {HttpContext.Request.Path + HttpContext.Request.QueryString}  {JsonConvert.SerializeObject(request)}");

                if (response.Success)
                    return Ok(response);

                return BadRequest(response);
            }
            catch (Exception e)
            {
                _logger.LogError(e,
                    $"EXCEPTION: POST {_globalData.Name} => API ERROR {HttpContext.Request.Path + HttpContext.Request.QueryString} ");
                // await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                //     set => set
                //         .TenantId(mid)
                //         .CorrelationId(request.OrderId)
                //         .Data(request));

                return StatusCode(StatusCodes.Status500InternalServerError, "Failed Cancel Refund");
            }
        }


        [HttpPost("void")]
        [Authorize(MyPolicies.ADMINS_ONLY)]
        [ProducesResponseType(typeof(VoidPaymentResult), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> VoidPost(VoidPaymentRequest request, Guid mid, CancellationToken token)
        {
            try
            {
                _logger.LogInformation(
                    $"ENTERED: {_globalData?.Name} => POST {HttpContext.Request.Path + HttpContext.Request.QueryString} | {JsonConvert.SerializeObject(request)}");

                if (!ModelState.IsValid)
                {
                    await _activityService.CreateActivityAsync(ApiErrorActivities.ModelStateError, ModelState,
                        set => set
                            .TenantId(mid)
                            .CorrelationId(request.OrderId)
                            .Data(request));
                    return ValidationProblem();
                }

                request.Mid = mid;

                var response = await _paymentOrchestrator.VoidAsync(request, token: token);

                _logger.LogInformation(
                    $"EXIT: {_globalData?.Name} => POST {HttpContext.Request.Path + HttpContext.Request.QueryString}  {JsonConvert.SerializeObject(request)}");

                if (response.Success)
                    return Ok(response);

                return BadRequest(response);
            }
            catch (Exception e)
            {
                _logger.LogError(e,
                    $"EXCEPTION: POST {_globalData.Name} => API ERROR {HttpContext.Request.Path + HttpContext.Request.QueryString} ");
                await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                    set => set
                        .TenantId(mid)
                        .CorrelationId(request.OrderId)
                        .Data(request));

                return StatusCode(StatusCodes.Status500InternalServerError, "Failed Void");
            }
        }

        // [HttpGet()] // GET BY ID
        // [Route("{id:guid}")]
        // // [ClaimRequirement(MyClaimTypes.PERMISSION, ContactClaims.CONTACT_READ_ONE)]
        // [ProducesResponseType(typeof(TransactionQueryDTO), StatusCodes.Status200OK)]
        // [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        // [ProducesResponseType(StatusCodes.Status404NotFound)]
        // public async Task<IActionResult> Get([FromRoute] Guid id, CancellationToken token)
        // {
        //     try
        //     {
        //         _logger.LogInformation($"ENTERED: {_globalData.Name} => GET {HttpContext.Request.Path + HttpContext.Request.QueryString} ");
        //
        //         var record = default(Transaction);
        //
        //         if (HttpContext.User.Claims.Any(x => x.Type == MyClaimTypes.PERMISSION && x.Value == TransactionsClaims.TRANSACTION_MANAGE))
        //             record = await _context.Transactions.SingleOrDefaultAsync(x => x.Id == id, cancellationToken: token);
        //         else
        //             record = await _context.Transactions.SingleOrDefaultAsync(x => x.Id == GetCID(), cancellationToken: token);
        //
        //         _logger.LogInformation($"EXIT: {_globalData.Name} => GET {HttpContext.Request.Path + HttpContext.Request.QueryString} ");
        //
        //         return Ok(_mapper.Map<TransactionQueryDTO>(record));
        //     }
        //     catch (Exception e)
        //     {
        //         _logger.LogError(e, $"EXCEPTION: GET contacts API ERROR {HttpContext.Request.Path + HttpContext.Request.QueryString} ");
        //         return StatusCode(StatusCodes.Status500InternalServerError, "Failed fetching record");
        //     }
        // }
    }
}