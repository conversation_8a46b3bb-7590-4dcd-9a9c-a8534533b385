using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Payments.Migrations
{
    /// <inheritdoc />
    public partial class commentexpiredintransctionstatus : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // migrationBuilder.AlterDatabase()
            //     .Annotation("Npgsql:Enum:payment_method_type", "un_known,credit,debit,e_check,check,money_order,cash,google_pay,apple_pay,samsung_pay,bnpl,other")
            //     .Annotation("Npgsql:Enum:transaction_status", "initialized,in_process,completed,failed,canceled,held")
            //     .OldAnnotation("Npgsql:Enum:payment_method_type", "un_known,credit,debit,e_check,check,money_order,cash,google_pay,apple_pay,samsung_pay,bnpl,other")
            //     .OldAnnotation("Npgsql:Enum:transaction_status", "initialized,in_process,completed,failed,canceled,held");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // migrationBuilder.AlterDatabase()
            //     .Annotation("Npgsql:Enum:payment_method_type", "un_known,credit,debit,e_check,check,money_order,cash,google_pay,apple_pay,samsung_pay,bnpl,other")
            //     .Annotation("Npgsql:Enum:transaction_status", "initialized,in_process,completed,failed,canceled,held,expired")
            //     .OldAnnotation("Npgsql:Enum:payment_method_type", "un_known,credit,debit,e_check,check,money_order,cash,google_pay,apple_pay,samsung_pay,bnpl,other")
            //     .OldAnnotation("Npgsql:Enum:transaction_status", "initialized,in_process,completed,failed,canceled,held");
        }
    }
}
