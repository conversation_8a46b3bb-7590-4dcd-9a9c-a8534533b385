using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Payments.Migrations
{
    /// <inheritdoc />
    public partial class renameCardTypetoCardBrandindisputesandqueueitemstables : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "CardType",
                table: "Disputes",
                newName: "CardBrand");

            migrationBuilder.RenameColumn(
                name: "CardType",
                table: "DisputeQueueItems",
                newName: "CardBrand");

            migrationBuilder.RenameColumn(
                name: "CardType",
                table: "DisputeActivities",
                newName: "CardBrand");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "CardBrand",
                table: "Disputes",
                newName: "CardType");

            migrationBuilder.RenameColumn(
                name: "<PERSON><PERSON><PERSON>",
                table: "DisputeQueueItems",
                newName: "CardType");

            migrationBuilder.RenameColumn(
                name: "CardB<PERSON>",
                table: "DisputeActivities",
                newName: "CardType");
        }
    }
}
