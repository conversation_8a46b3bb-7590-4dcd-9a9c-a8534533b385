using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Payments.Migrations
{
    /// <inheritdoc />
    public partial class altersupportedgatewaysaddtotalerrorscount : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "Quarantine_AddedOn",
                table: "Metrics",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "Quarantine_ErrorsCount",
                table: "Metrics",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "TotalErrorAmount",
                table: "Metrics",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "TotalErrorCount",
                table: "Metrics",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "TotalFailureAmount",
                table: "Metrics",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "TotalFailureCount",
                table: "Metrics",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "TotalRefundAmount",
                table: "Metrics",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "TotalRefundCount",
                table: "Metrics",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "TotalSuccessAmount",
                table: "Metrics",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "TotalSuccessCount",
                table: "Metrics",
                type: "integer",
                nullable: false,
                defaultValue: 0);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Quarantine_AddedOn",
                table: "Metrics");

            migrationBuilder.DropColumn(
                name: "Quarantine_ErrorsCount",
                table: "Metrics");

            migrationBuilder.DropColumn(
                name: "TotalErrorAmount",
                table: "Metrics");

            migrationBuilder.DropColumn(
                name: "TotalErrorCount",
                table: "Metrics");

            migrationBuilder.DropColumn(
                name: "TotalFailureAmount",
                table: "Metrics");

            migrationBuilder.DropColumn(
                name: "TotalFailureCount",
                table: "Metrics");

            migrationBuilder.DropColumn(
                name: "TotalRefundAmount",
                table: "Metrics");

            migrationBuilder.DropColumn(
                name: "TotalRefundCount",
                table: "Metrics");

            migrationBuilder.DropColumn(
                name: "TotalSuccessAmount",
                table: "Metrics");

            migrationBuilder.DropColumn(
                name: "TotalSuccessCount",
                table: "Metrics");
        }
    }
}
