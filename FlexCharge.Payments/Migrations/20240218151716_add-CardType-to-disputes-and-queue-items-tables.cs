using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Payments.Migrations
{
    /// <inheritdoc />
    public partial class addCardTypetodisputesandqueueitemstables : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "CardType",
                table: "Disputes",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CardType",
                table: "DisputeQueueItems",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CardType",
                table: "DisputeActivities",
                type: "text",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CardType",
                table: "Disputes");

            migrationBuilder.DropColumn(
                name: "CardType",
                table: "DisputeQueueItems");

            migrationBuilder.DropColumn(
                name: "CardType",
                table: "DisputeActivities");
        }
    }
}
