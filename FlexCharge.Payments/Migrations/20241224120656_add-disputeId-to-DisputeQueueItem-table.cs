using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Payments.Migrations
{
    /// <inheritdoc />
    public partial class adddisputeIdtoDisputeQueueItemtable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "DisputeQueueItemId",
                table: "Disputes",
                type: "uuid",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "DisputeId",
                table: "DisputeQueueItems",
                type: "uuid",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "DisputeQueueItemId",
                table: "Disputes");

            migrationBuilder.DropColumn(
                name: "DisputeId",
                table: "DisputeQueueItems");
        }
    }
}
