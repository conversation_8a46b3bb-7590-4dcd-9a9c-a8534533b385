// <auto-generated />
using System;
using FlexCharge.Payments;
using FlexCharge.Payments.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace FlexCharge.Payments.Migrations
{
    [DbContext(typeof(PostgreSQLDbContext))]
    [Migration("20220220124237_initial")]
    partial class initial
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "6.0.1")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.HasPostgresEnum(modelBuilder, "payment_method_type", new[] { "un_known", "credit", "debit", "e_check", "check", "money_order", "cash", "google_pay", "apple_pay", "samsung_pay", "bnpl", "other" });
            NpgsqlModelBuilderExtensions.HasPostgresEnum(modelBuilder, "transaction_status", new[] { "initialized", "in_process", "completed", "failed", "canceled" });
            NpgsqlModelBuilderExtensions.HasPostgresEnum(modelBuilder, "transaction_type", new[] { "debit", "credit" });
            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("FlexCharge.Payments.Entities.Batch", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOd")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.ToTable("Batches");
                });

            modelBuilder.Entity("FlexCharge.Payments.Entities.Merchant", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("ExternalId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOd")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("SpreedlyEnvironmentKey")
                        .HasColumnType("text");

                    b.Property<string>("SpreedlySecretKey")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("Merchants");
                });

            modelBuilder.Entity("FlexCharge.Payments.Entities.PaymentInstrument", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Bin")
                        .HasColumnType("text");

                    b.Property<string>("CardCountry")
                        .HasColumnType("text");

                    b.Property<string>("CardHolderName")
                        .HasColumnType("text");

                    b.Property<string>("CardNumberMasked")
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ExpirationMonth")
                        .HasColumnType("text");

                    b.Property<string>("ExpirationYear")
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool?>("IsVerified")
                        .HasColumnType("boolean");

                    b.Property<string>("Issuer")
                        .HasColumnType("text");

                    b.Property<string>("Last4")
                        .HasColumnType("text");

                    b.Property<Guid?>("MerchantId")
                        .HasColumnType("uuid");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOd")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Number")
                        .HasColumnType("text");

                    b.Property<Guid>("OwnerId")
                        .HasColumnType("uuid");

                    b.Property<string>("Token")
                        .HasColumnType("text");

                    b.Property<PaymentMethodType>("Type")
                        .HasColumnType("payment_method_type");

                    b.HasKey("Id");

                    b.HasIndex("MerchantId");

                    b.ToTable("PaymentInstruments");
                });

            modelBuilder.Entity("FlexCharge.Payments.Entities.Transaction", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("Amount")
                        .HasColumnType("integer");

                    b.Property<string>("AuthorizationId")
                        .HasColumnType("text");

                    b.Property<string>("AvsResultCode")
                        .HasColumnType("text");

                    b.Property<Guid?>("BatchId")
                        .HasColumnType("uuid");

                    b.Property<string>("CavvResultCode")
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Currency")
                        .HasColumnType("text");

                    b.Property<string>("CvvResultCode")
                        .HasColumnType("text");

                    b.Property<string>("DynamicDescriptor")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsRecurring")
                        .HasColumnType("boolean");

                    b.Property<Guid>("MerchantId")
                        .HasColumnType("uuid");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedOd")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("OrderId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("ParentId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("PayerId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("PaymentMethodId")
                        .HasColumnType("uuid");

                    b.Property<string>("ResponseCode")
                        .HasColumnType("text");

                    b.Property<TransactionStatus>("Status")
                        .HasColumnType("transaction_status");

                    b.Property<TransactionType>("Type")
                        .HasColumnType("transaction_type");

                    b.HasKey("Id");

                    b.HasIndex("BatchId");

                    b.ToTable("Transactions");
                });

            modelBuilder.Entity("FlexCharge.Payments.Entities.PaymentInstrument", b =>
                {
                    b.HasOne("FlexCharge.Payments.Entities.Merchant", "Merchant")
                        .WithMany()
                        .HasForeignKey("MerchantId");

                    b.Navigation("Merchant");
                });

            modelBuilder.Entity("FlexCharge.Payments.Entities.Transaction", b =>
                {
                    b.HasOne("FlexCharge.Payments.Entities.Batch", null)
                        .WithMany("Trasactions")
                        .HasForeignKey("BatchId");
                });

            modelBuilder.Entity("FlexCharge.Payments.Entities.Batch", b =>
                {
                    b.Navigation("Trasactions");
                });
#pragma warning restore 612, 618
        }
    }
}
