using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Payments.Migrations
{
    /// <inheritdoc />
    public partial class TransactionsUpgrade : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // migrationBuilder.Sql(@"CREATE OR REPLACE  FUNCTION toDate(a timestamptz) RETURNS date
            // LANGUAGE SQL
            // IMMUTABLE
            //     RETURNS NULL ON NULL INPUT
            // RETURN Date(a);");
            
            /// postgres 13 version
          //   migrationBuilder.Sql(@"CREATE OR REPLACE  FUNCTION toDate(a timestamptz) RETURNS date
          //   AS 'select Date($1);'
          //   LANGUAGE SQL
          //   IMMUTABLE
          //   RETURNS NULL ON NULL INPUT;
          // ");


            migrationBuilder.AddColumn<DateOnly>(
                name: "CreatedOnDate",
                table: "Transactions",
                type: "date",
                nullable: true);
                //computedColumnSql: "toDate(\"CreatedOn\")", stored:true);

            migrationBuilder.CreateIndex(
                name: "IX_Transactions_CreatedOnDate",
                table: "Transactions",
                column: "CreatedOnDate");

            migrationBuilder.AddForeignKey(
                name: "FK_Transactions_PaymentInstruments_PaymentMethodId",
                table: "Transactions",
                column: "PaymentMethodId",
                principalTable: "PaymentInstruments",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Transactions_PaymentInstruments_PaymentMethodId",
                table: "Transactions");

            migrationBuilder.DropIndex(
                name: "IX_Transactions_CreatedOnDate",
                table: "Transactions");

            migrationBuilder.DropColumn(
                name: "CreatedOnDate",
                table: "Transactions");
        }
    }
}
