using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Payments.Migrations
{
    /// <inheritdoc />
    public partial class altersupportedgatewaysaddcontactinformationandmore : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "AuthFee",
                table: "SupportedGateways",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "ContactEmail",
                table: "SupportedGateways",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ContactName",
                table: "SupportedGateways",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ContactPhone",
                table: "SupportedGateways",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DynamicDescriptorValidationDateTime",
                table: "SupportedGateways",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "EthocaProvider",
                table: "SupportedGateways",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PricingModel",
                table: "SupportedGateways",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ProcessorPlatform",
                table: "SupportedGateways",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "RdrProvider",
                table: "SupportedGateways",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SponsorBank",
                table: "SupportedGateways",
                type: "text",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AuthFee",
                table: "SupportedGateways");

            migrationBuilder.DropColumn(
                name: "ContactEmail",
                table: "SupportedGateways");

            migrationBuilder.DropColumn(
                name: "ContactName",
                table: "SupportedGateways");

            migrationBuilder.DropColumn(
                name: "ContactPhone",
                table: "SupportedGateways");

            migrationBuilder.DropColumn(
                name: "DynamicDescriptorValidationDateTime",
                table: "SupportedGateways");

            migrationBuilder.DropColumn(
                name: "EthocaProvider",
                table: "SupportedGateways");

            migrationBuilder.DropColumn(
                name: "PricingModel",
                table: "SupportedGateways");

            migrationBuilder.DropColumn(
                name: "ProcessorPlatform",
                table: "SupportedGateways");

            migrationBuilder.DropColumn(
                name: "RdrProvider",
                table: "SupportedGateways");

            migrationBuilder.DropColumn(
                name: "SponsorBank",
                table: "SupportedGateways");
        }
    }
}
