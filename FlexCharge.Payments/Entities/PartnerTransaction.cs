using System;
using System.ComponentModel.DataAnnotations.Schema;
using System.Diagnostics.Eventing.Reader;
using System.Text.Json;
using MassTransit.Futures.Contracts;
using Microsoft.EntityFrameworkCore;

namespace FlexCharge.Payments.Entities
{
    [Index(nameof(ProviderTransactionToken))]
    [Index(nameof(CreatedOn))]
    [Index(nameof(ParentId))]
    [Index(nameof(CreatedOnDate))]
    public class PartnerTransaction : AuditableEntity
    {
        public Guid Pid { get; set; }

        public Guid ParentId { get; set; }

        public string ProviderTransactionToken { get; set; }

        public int Amount { get; set; }
        public string Currency { get; set; }

        public string ResponseCode { get; set; }
        public string ResponseMessage { get; set; }

        public string AuthorizationId { get; set; }

        public string Type { get; set; }

        public TransactionStatus Status { get; set; }
        public string? PaymentType { get; set; }

        public Guid ProviderId { get; set; }
        public string ProviderName { get; set; }
        public string ProcessorName { get; set; }
        public string ProcessorId { get; set; }

        public string InternalResponseCode { get; set; }
        public string InternalResponseMessage { get; set; }
        public string InternalResponseGroup { get; set; }

        public string Note { get; set; }
        [Column(TypeName = "jsonb")] public JsonDocument Meta { get; set; }
        public string ExtraData { get; set; }

        // virtual indexed column for fast search by date
        public DateOnly? CreatedOnDate { get; set; } = DateOnly.FromDateTime(DateTime.UtcNow);
    }
}