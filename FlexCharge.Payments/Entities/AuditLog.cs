using System;

namespace FlexCharge.Payments.Entities;

public class AuditLog : AuditableEntity
{
    public string Microservice { get; set; }
    public string Entity { get; set; }
    public string TableName { get; set; }
    public string PrimaryKey { get; set; }
    public string Operation { get; set; }
    public string OldValues { get; set; }
    public string NewValues { get; set; }
    public string AffectedColumns { get; set; }

    public string UserId { get; set; }
    public string UserFirstName { get; set; }
    public string UserLastName { get; set; }
    public string UserRole { get; set; }
    public string UserEmail { get; set; }
    public string UserIp { get; set; }
}