using System;

namespace FlexCharge.Payments.Entities;

public class CycleMetrics : VersionedEntity
{
    public Guid? SupportGatewayId { get; set; }
    public DateTime CycleStartDate { get; set; }
    public DateTime CycleEndDate { get; set; }
    public int MasterCardCurrentTransactionsCount { get; set; }
    public int MasterCardCurrentTransactionsValue { get; set; }

    public int VisaCurrentTransactionsCount { get; set; }
    public int VisaCurrentTransactionsValue { get; set; }

    public int TotalDailyTransactionCount { get; set; }
    public int TotalDailyTransactionAmount { get; set; }

    public int TotalTransactionCount { get; set; }
    public int TotalTransactionAmount { get; set; }

    public int TotalRefundCount { get; set; }
    public int TotalRefundAmount { get; set; }

    public int TotalSuccessCount { get; set; }
    public int TotalSuccessAmount { get; set; }

    public int TotalFailureCount { get; set; }
    public int TotalFailureAmount { get; set; }

    public int TotalErrorCount { get; set; }
    public int TotalErrorAmount { get; set; }

    public int Quarantine_ErrorsCount { get; set; }
    public DateTime? Quarantine_AddedOn { get; set; }

    public DateTime? LastTransactionTimeStamp { get; set; }
}