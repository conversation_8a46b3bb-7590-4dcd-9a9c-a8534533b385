{"app": {"name": "payments-service", "version": "0.0.1"}, "grpc": {"VaultEndpoint": "http://vault.staging:5000"}, "jwt": {"Provider": "Cognito", "secretKey": "JLBMU2VbJZmt42sUwByUpJJF6Y5mG2gPNU9sQFUpJFcGFJdyKxskR3bxh527kax2UcXHvB", "expiryMinutes": 30, "issuer": "identity-service", "validateLifetime": true, "ValidateAudience": true, "ValidAudience": "4n0i8a4i9o1vk3g3tf64o6blg7", "Region": "us-east-1", "UserPoolId": "us-east-1_rCUpTgXY4", "AppClientId": "4n0i8a4i9o1vk3g3tf64o6blg7"}, "basicOauth": {"expiryMinutes": 60, "issuer": "Api-Client-Service", "SecretKey": "TAKEN FROM SECRETS", "Provider": "Api-Client-Service", "ValidateLifetime": true, "ValidateAudience": true, "ValidateIssuer": true, "ValidAudience": "payments"}, "cache": {"connectionString": "cacheservice1.wkhxcg.ng.0001.use1.cache.amazonaws.com:6379", "instance": "", "schemaName": "dbo", "tableName": "<PERSON><PERSON>", "BigPayloadCacheConnectionString": "cacheservice1.wkhxcg.ng.0001.use1.cache.amazonaws.com:6379", "IdempotencyCacheConnectionString": "cacheservice1.wkhxcg.ng.0001.use1.cache.amazonaws.com:6379", "ExternalRequestsCacheConnectionString": "cacheservice1.wkhxcg.ng.0001.use1.cache.amazonaws.com:6379"}, "dataStream": {"provider": "kinesis"}, "spreedly": {"environment": "6n35aiMqDYOZ6IQsVD12H0JmtVx", "secret": "dTH45tucocf68Yi0BW7j1KDIeWTA2Y3HmSm0EMTVLdwulRVnWD2dy3oAJMxDWvtZ", "baseUrl": "https://core.spreedly.com/v1"}, "Plaid": {"Environment": "Sandbox"}, "spreadly3DSMerchantConfiguration": {"MerchantProfileDescription": "Flex-Charge | Development", "Type": "test", "Sandbox": true, "CardSchemas": {"Visa": {"AcquirerBin": "**********", "AcquirerMerchantId": "spreedlys_mid", "MCC": "5978", "MerchantName": "Spreedly", "MerchantCountry": "840", "MerchantUrl": "https://www.flex-charge.com", "MerchantPassword": "optional"}, "Mastercard": {"AcquirerBin": "**********", "AcquirerMerchantId": "spreedlys_mid", "MCC": "5978", "MerchantName": "Spreedly", "MerchantCountry": "840", "MerchantUrl": "https://www.flex-charge.com", "MerchantPassword": "optional"}, "Amex": {"AcquirerBin": "**********", "AcquirerMerchantId": "spreedlys_mid", "MCC": "5978", "MerchantName": "Spreedly", "MerchantCountry": "840", "MerchantUrl": "https://www.flex-charge.com", "MerchantPassword": "optional"}}}, "serilog": {"consoleEnabled": true, "level": "Information", "path": "../logs/payments-{0}.txt"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "swagger": {"enabled": true, "reDocEnabled": false, "name": "v1", "title": "contacts-service", "version": "v1", "routePrefix": "", "includeSecurity": true}, "jaeger": {"agentHost": "**************", "agentPort": 6831}, "backgroundWorkerService": {"executionInterval": 100}, "tokenEx": {"TokenExId": "3491034038754331", "APIKey": "duENgUuwWHw8RLbP1s3d7EpWBiWbtBVeMbnP987K", "TokenScheme": "29", "Tokenize": true, "BaseUrl": "https://test-api.tokenex.com/V2", "CardSchemas": [{"Name": "SANDBOX_DS", "AcquirerBin": "444444", "AcquirerMerchantId": "External_Test_Merchant", "MCC": "0001", "MerchantCountry": "840", "MerchantName": "Merchant Name"}, {"Name": "VISA", "AcquirerBin": "444444", "AcquirerMerchantId": "External_Test_Merchant", "MCC": "0001", "MerchantCountry": "840", "MerchantName": "Merchant Name"}, {"Name": "MASTERCARD", "AcquirerBin": "444444", "AcquirerMerchantId": "External_Test_Merchant", "MCC": "0001", "MerchantCountry": "840", "MerchantName": "Merchant Name"}, {"Name": "AMEX", "AcquirerBin": "444444", "AcquirerMerchantId": "External_Test_Merchant", "MCC": "0001", "MerchantCountry": "840", "MerchantName": "Merchant Name"}]}, "email": {"provider": "sendgrid", "key": "*********************************************************************", "senderEmail": "<EMAIL>", "senderName": "FlexFactor", "rndEmail": "<EMAIL>", "newDisputeItemsTemplateId": "d-3951a42c9d0849e2ac8415cdecb04f46", "unprocessedDisputeItemsTemplateId": "d-c2f5db09ef434eba8cfb157433a61059", "portalBaseUrl": "https://portal-staging.flexfactor.io"}, "TransactionReportOptions": {"ReportName": "Detailed Transactional Report", "ReportId": "8A13FE10-888C-437F-8A98-8F73640C6F19", "EmailTemplateId": "d-d3185eabd44c4469b2f804f55b0fddd2"}, "reportingService": {"ResponseMessage": "Your report is being generated, and email with the report will be sent to your email address once ready."}, "dynamoDb": {"Endpoint": "https://dynamodb.us-east-1.amazonaws.com"}, "disputesSftp": {"Bucket": "external-reports-incoming-staging", "RootFolderToWatch": "", "ProcessingRootFolder": ""}, "Kestrel": {"Endpoints": {"HTTP": {"Url": "http://*:80", "Protocols": "Http1AndHttp2"}, "gRPC": {"Url": "http://*:5000", "Protocols": "Http2"}}}, "AllowedHosts": "*"}