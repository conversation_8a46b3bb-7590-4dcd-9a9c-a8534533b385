using System;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;
using FlexCharge.Payments.Services.PaymentServices.Interfaces;

namespace FlexCharge.Payments.DTO.Webhooks;

public class SubscriptionChargeUpdate : IWebHookResult
{
    public int Amount { get; set; }
    public bool Success { get; set; }
    public string Currency { get; set; }
    public string ProviderName { get; set; }
    public string Type { get; set; }

    public string ProviderTransactionToken { get; set; }

    // public string ProcessorId { get; set; }
    public string ProviderSubscriptionId { get; set; }

    // public Guid? PaymentMethodId { get; set; }
    //public Guid OrderId { get; set; }
    public JsonDocument Meta { get; set; }
}