using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using FlexCharge.Payments.Entities;
using FlexCharge.Payments.Entities.JsonbModels;
using RestEase;

namespace FlexCharge.Payments.DTO;

public class ProviderDto
{
    [Required] public string Name { get; set; }
    [Required] public string GatewayType { get; set; }
    public string User { get; set; }
    public string Password { get; set; }
    public bool IsSandbox { get; set; }
    public string SecretKey { get; set; }
    public string Configuration { get; set; }
    public bool IsActive { get; set; }

    public Dictionary<string, bool> Methods { get; set; }
    public Dictionary<string, string> Capabilities { get; set; }
}

public class GatewayUpdateDTO
{
    public Guid Id { get; set; }
    public Guid MerchantId { get; set; }
    public int Order { get; set; }
    public bool IsActive { get; set; }

    public bool IsDefault { get; set; }

    public CapabilitiesModel Capabilities { get; set; }
    //public bool IsSandbox { get; set; }
}

public class QueryMerchantGatewaysDTO
{
    public string Name { get; set; }
    public string MCC { get; set; }
    public string ProcessorId { get; set; }

    public string MidNumber { get; set; }
    public Guid Id { get; set; }
    public Guid ProviderId { get; set; }
    public Guid MerchantId { get; set; }
    public int? Order { get; set; }
    public bool IsActive { get; set; }
    public bool IsDefault { get; set; }
    public bool IsSandbox { get; set; }

    public CapabilitiesModel Capabilities { get; set; }
}

public class PaymentProvidersBaseDTO
{
    public Guid? PartnerId { get; set; }

    [StringLength(50, MinimumLength = 2, ErrorMessage = "Name must be at least 2 characters.")]
    public string Name { get; set; }

    public Guid Id { get; set; }

    [Required] public AuthenticationType AuthenticationType { get; set; }

    public bool? Sandbox { get; set; }
    public bool? IsActive { get; set; }

    public string ProcessorId { get; set; }
    public string MerchantNumber { get; set; }
    public string ProcessorPlatform { get; set; }

    public string SponsorBank { get; set; }

    public string PricingModel { get; set; }
    public int AuthFee { get; set; }

    public Dictionary<string, bool> Methods { get; set; }
    public CapabilitiesModel Capabilities { get; set; }


    [StringLength(4, ErrorMessage = "MCC cannot be more than 4 characters.")]
    public string MCC { get; set; }

    public string CAID { get; set; }
    public string BIN { get; set; }

    public string FixedDescriptorPrefix { get; set; }

    [StringLength(10, ErrorMessage = "MasterCardDescriptorPrefix cannot be more than 10 characters.")]
    public string MasterCardDescriptorPrefix { get; set; }

    [StringLength(10, ErrorMessage = "VisaDescriptorPrefix cannot be more than 10 characters.")]
    public string VisaDescriptorPrefix { get; set; }

    public string RdrProvider { get; set; }
    public string EthocaProvider { get; set; }
    public string ChargebacksManagementProvider { get; set; }

    public string ContactName { get; set; }
    public string ContactEmail { get; set; }
    public string ContactPhone { get; set; }

    public int? Tier { get; set; }
}

public class QueryPaymentProvidersDTO : PaymentProvidersBaseDTO
{
    public string User { get; set; }
    public string PasswordMasked { get; set; }
    public string Password { get; set; }
    public string SecretKeyMasked { get; set; }
    public string SecretKey { get; set; }

    public CycleMetrics CycleMetrics { get; set; }
    public string AuthenticationType { get; set; }
    public string ProviderLogo { get; set; }
    public string ProviderLogoAlt { get; set; }

    public int RelatedMerchantsCount
    {
        get { return this.RelatedMerchants.Count; }
    }

    public List<RelatedMerchantsDTO> RelatedMerchants { get; set; }
}

public class RelatedMerchantsDTO
{
    public Guid Id { get; set; }
    public string Dba { get; set; }
    public string Status { get; set; }
}

public class ExportQueryPaymentProvidersDTO : PaymentProvidersBaseDTO
{
    public DateTime? CycleStartDate { get; set; }
    public DateTime? CycleEndDate { get; set; }
    public int? MasterCardCurrentTransactionsCount { get; set; }
    public int? MasterCardCurrentTransactionsValue { get; set; }

    public int? VisaCurrentTransactionsCount { get; set; }
    public int? VisaCurrentTransactionsValue { get; set; }

    public int? TotalTransactionCount { get; set; }
    public int? TotalTransactionAmount { get; set; }
}

public class UpdatePaymentProvidersDTO : PaymentProvidersBaseDTO
{
    public string User { get; set; }
    public string Password { get; set; }

    public string SecretKey { get; set; }
}