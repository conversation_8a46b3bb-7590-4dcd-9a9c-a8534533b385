using System;
using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Http;

namespace FlexCharge.Payments.DTO;

public class CreateQueueRequestDTO
{
    public string Name { get; set; }
    // public IFormFile File { get; set; }
    public string Source { get; set; }
    public string Type { get; set; }
    public bool AutoMatch { get; set; }
    public bool? OpenItemsAlert { get; set; }
    public bool? NewItemsAlert { get; set; }

    [RequiredIfAlertsEnabled]
    public string? EmailRecipient { get; set; }
    public Guid? PartnerId { get; set; }
}

public class RequiredIfAlertsEnabledAttribute : ValidationAttribute
{
    protected override ValidationResult IsValid(object value, ValidationContext validationContext)
    {
        var instance = (CreateQueueRequestDTO)validationContext.ObjectInstance;
        if ((instance.OpenItemsAlert == true || instance.NewItemsAlert == true) && string.IsNullOrEmpty(instance.EmailRecipient))
        {
            return new ValidationResult("EmailRecipient is required.");
        }
        return ValidationResult.Success;
    }
}