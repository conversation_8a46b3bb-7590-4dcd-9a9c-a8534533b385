using System;
using System.Collections.Generic;
using FlexCharge.Payments.Entities;
using TransactionStatus = System.Transactions.TransactionStatus;

namespace FlexCharge.Payments.DTO
{
    public class TransactionQueryDTO
    {
        public string? Query { get; set; }
        public DateTime? From { get; set; }
        public DateTime? to { get; set; }
        public string? OrderBy { get; set; }
        public string? Sort { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public List<string>? Provider { get; set; }
        public List<string>? type { get; set; }
        public List<Guid>? Id { get; set; }
        public Guid? MerchantId { get; set; }
        public string? OrderId { get; set; }
        public string? TransactionId { get; set; }
        public string? Amount { get; set; }
        public string? Bin { get; set; }
        public string? Last4 { get; set; }
        public List<string>? Status { get; set; }
        public string? AuthCode { get; set; }
        public string? ExternalReferenceID { get; set; }
        public string? CardBrand { get; set; }
        public string? Arn { get; set; }
    }

    public class TransactionDTO
    {
        public Guid Id { get; set; }
        public DateTime CreatedOn { get; set; }
        public string Amount { get; set; }
        public string Currency { get; set; }
        public string CurrencySymbol { get; set; }
        public string Type { get; set; }
        public string Status { get; set; }
        public string ProviderName { get; set; }
        public string Gateway { get; set; }
        public Guid MerchantId { get; set; }
        public Guid OrderId { get; set; }
        public string MerchantName { get; set; }
        public string Source { get; set; } //last4
        public string SourceType { get; set; } //visa/mc/ach
        public string ResponseCode { get; set; }
        public string Bin { get; set; }
        public string Last4 { get; set; }
        public string? ExternalReferenceID { get; set; }
        public string? AuthorizationID { get; set; }
        public string? Issuer { get; set; }
        public string? CardBrand { get; set; }
        public string? Arn { get; set; }
        public string DynamicDescriptor { get; set; }
    }
}