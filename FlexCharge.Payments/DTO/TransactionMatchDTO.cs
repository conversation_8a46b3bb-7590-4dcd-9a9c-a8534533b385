using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace FlexCharge.Payments.DTO;

public class TransactionMatchDTO
{
    public Guid DisputeId { get; set; }
    public Guid TransactionId { get; set; }
    public string ProviderTransactionToken { get; set; }
    public string DynamicDescriptor { get; set; }
    public decimal Amount { get; set; }
    public string Currency { get; set; }
    public string ResponseCode { get; set; }
    public string AuthorizationId { get; set; }
    public string Status { get; set; }
    public Guid? SiteId { get; set; }
    public Guid MerchantId { get; set; }
    public Guid OrderId { get; set; }
    public string Arn { get; set; }
    public string Note { get; set; }
    [Column(TypeName = "jsonb")] public string Meta { get; set; }
    public string Bin { get; set; }
    public string Last4 { get; set; }
    public string ProviderName { get; set; }
    public string Type { get; set; }
    public DateTime CreatedOn { get; set; }
}