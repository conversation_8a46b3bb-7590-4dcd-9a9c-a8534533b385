// using System;
//
// namespace FlexCharge.Payments.Metrics.Tables;
//
// public static class TimeRangeKeyFactory
// {
//     public enum TimeRange
//     {
//         AllTime,
//         Monthly,
//         Daily,
//         Hourly,
//         Minutely,
//     }
//
//     public static string CreateTimeRangeKey(TimeRange timeRange, DateTime time)
//     {
//         var utcTime = time.ToUniversalTime();
//
//         switch (timeRange)
//         {
//             case TimeRange.AllTime:
//                 return "A"; // A
//             case TimeRange.Monthly:
//                 return $"M|{utcTime.Year}-{utcTime.Month:D2}"; // M|2024-08
//             case TimeRange.Daily:
//                 return $"D|{utcTime.Year}-{utcTime.Month:D2}-{utcTime.Day:D2}"; // M|2024-08-04
//             case TimeRange.Hourly:
//                 return $"h|{utcTime.Year}-{utcTime.Month:D2}-{utcTime.Day:D2}T{utcTime.Hour:D2}"; // h|2024-08-04T00
//             case TimeRange.Minutely:
//                 return
//                     $"m|{utcTime.Year}-{utcTime.Month:D2}-{utcTime.Day:D2}T{utcTime.Hour:D2}:{utcTime.Minute:D2}"; // m|2024-08-04T00:40
//             default:
//                 throw new ArgumentOutOfRangeException(nameof(timeRange), timeRange, null);
//         }
//     }
//
//     public static (DateTime StartTime, DateTime EndTime) GetTimeRangeStartAndEnd(TimeRange timeRange, DateTime time)
//     {
//         var utcTime = time.ToUniversalTime();
//
//         switch (timeRange)
//         {
//             case TimeRange.AllTime:
//                 return (DateTime.MinValue, utcTime);
//             case TimeRange.Monthly:
//                 return (new DateTime(utcTime.Year, utcTime.Month, 1, 0, 0, 0, DateTimeKind.Utc),
//                     new DateTime(utcTime.Year, utcTime.Month, 1, 0, 0, 0, DateTimeKind.Utc)
//                         .AddMonths(1)
//                         .AddMilliseconds(-1));
//             case TimeRange.Daily:
//                 return (new DateTime(utcTime.Year, utcTime.Month, utcTime.Day, 0, 0, 0, DateTimeKind.Utc),
//                     new DateTime(utcTime.Year, utcTime.Month, utcTime.Day, 0, 0, 0, DateTimeKind.Utc)
//                         .AddDays(1)
//                         .AddMilliseconds(-1));
//             case TimeRange.Hourly:
//                 return (new DateTime(utcTime.Year, utcTime.Month, utcTime.Day, utcTime.Hour, 0, 0, DateTimeKind.Utc),
//                     new DateTime(utcTime.Year, utcTime.Month, utcTime.Day, utcTime.Hour, 0, 0, DateTimeKind.Utc)
//                         .AddHours(1)
//                         .AddMilliseconds(-1));
//             case TimeRange.Minutely:
//                 return
//                     (new DateTime(utcTime.Year, utcTime.Month, utcTime.Day, utcTime.Hour, utcTime.Minute, 0, DateTimeKind.Utc),
//                         new DateTime(utcTime.Year, utcTime.Month, utcTime.Day, utcTime.Hour, utcTime.Minute, 0,
//                                 DateTimeKind.Utc)
//                             .AddMinutes(1)
//                             .AddMilliseconds(-1));
//             default:
//                 throw new ArgumentOutOfRangeException(nameof(timeRange), timeRange, null);
//         }
//     }
// }

