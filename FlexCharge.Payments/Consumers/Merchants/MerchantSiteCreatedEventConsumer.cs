using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.MassTransit;
using FlexCharge.Contracts;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Payments.Consumers;

public class MerchantSiteCreatedEventConsumer : ConsumerBase<MerchantSiteCreatedEvent>
{
    private readonly PostgreSQLDbContext _dbContext;

    //private readonly IEmailSender _emailSender;
    private readonly IConfiguration _configuration;

    public MerchantSiteCreatedEventConsumer(
        PostgreSQLDbContext dbContext,
        //IEmailSender emailSender, 
        IConfiguration configuration,
        IServiceScopeFactory serviceScopeFactory) : base(serviceScopeFactory)
    {
        _dbContext = dbContext;
        //_emailSender = emailSender;
        _configuration = configuration;
    }


    protected override async Task ConsumeMessage(MerchantSiteCreatedEvent message, CancellationToken cancellationToken)
    {
        // try
        // {
        //     // Workspan.Log.Information(
        //     //     "ENTERED: Correlation: {CorrelationId} > MerchantActivatedEventConsumer > {ApplicationId}",
        //     //     message.Mid, context.CorrelationId);
        //
        //     //JsonConvert.SerializeObject(context.Message);
        // }
        // catch (Exception e)
        // {
        //     Workspan.RecordException(e,
        //         $"EXCEPTION: Correlation: {context.CorrelationId} > Failed MerchantActivatedEventConsumer > {message.Mid}˚");
        // }

        await Task.CompletedTask;
    }
}