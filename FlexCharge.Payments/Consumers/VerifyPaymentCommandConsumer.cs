using System;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.MassTransit;
using FlexCharge.Contracts.Commands;
using FlexCharge.Payments.Services.PaymentInstrumentsServices;
using FlexCharge.Payments.Services.PaymentServices;
using FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Payments.Consumers;

public class
    VerifyPaymentCommandConsumer : IdempotentCommandConsumer<VerifyPaymentCommand, VerifyPaymentCommandResponse>
{
    private readonly PostgreSQLDbContext _dbContext;
    private IPaymentInstrumentsService _instruments;
    private readonly IPaymentOrchestrator _paymentOrchestrator;

    public VerifyPaymentCommandConsumer(
        PostgreSQLDbContext dbContext,
        IPaymentInstrumentsService instruments,
        IPaymentOrchestrator paymentOrchestrator,
        IServiceScopeFactory serviceScopeFactory) : base(serviceScopeFactory)
    {
        _dbContext = dbContext;
        _instruments = instruments;
        _paymentOrchestrator = paymentOrchestrator;
    }

    protected override async Task<VerifyPaymentCommandResponse> ConsumeCommand(VerifyPaymentCommand command,
        CancellationToken cancellationToken)
    {
        try
        {
            Workspan
                .Baggage("Mid", command.Mid)
                .Baggage("OrderId", command.OrderId)
                .Baggage("PaymentInstrumentId", command.PaymentInstrumentID)
                .LogEnterAndExit();


            var verifyResponse = await _paymentOrchestrator.VerifyAsync(
                new VerifyInstrumentRequest
                {
                    Mid = command.Mid,
                    Token = command.PaymentInstrumentID,
                    CurrencyCode = command.Currency,
                    BillingAddress = new BillingAddress().InitializeFrom(command.BillingAddress),
                    IsCit = command.IsCit,
                    TryUseAccountUpdater = command.TryUseAccountUpdater
                });

            return new VerifyPaymentCommandResponse
            {
                ErrorCode = verifyResponse.ProviderResponseCode,
                ErrorMessage = verifyResponse.ProviderResponseMessage,
                IsVerified = verifyResponse.Success,
                BinNumber = verifyResponse.BinNumber,
                TransactionId = verifyResponse.TransactionId,
                Provider = verifyResponse.Provider,
                ProviderResponseCode = verifyResponse.ProviderResponseCode,

                InternalResponseCode = verifyResponse.InternalResponseCode,
                InternalResponseMessage = verifyResponse.InternalResponseMessage,
                InternalResponseGroup = verifyResponse.InternalResponseGroup,

                CvvCode = verifyResponse.CvvCode,
                AvsCode = verifyResponse.AvsCode
            };
        }
        catch (Exception e)
        {
            Workspan.RecordFatalException(e, $"EXCEPTION: Failed verifying payment method");
            throw;
        }
    }
}