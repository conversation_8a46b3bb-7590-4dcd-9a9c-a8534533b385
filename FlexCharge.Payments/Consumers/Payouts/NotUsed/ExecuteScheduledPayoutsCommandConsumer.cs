// using System;
// using System.Linq;
// using System.Threading;
// using System.Threading.Tasks;
// using FlexCharge.Common.Telemetry;
// using FlexCharge.Contracts;
// using FlexCharge.Contracts.Commands;
// using FlexCharge.Contracts.Payouts;
// using FlexCharge.Payments.Entities;
// using FlexCharge.Payments.Services.PayoutServices;
// using FlexCharge.Payments.Services.SpreedlyService;
// using Hangfire.Server;
// using MassTransit;
// using Microsoft.EntityFrameworkCore;
// using Microsoft.Extensions.Logging;
// using Newtonsoft.Json;
// using Merchant = FlexCharge.Payments.Entities.Merchant;
//
// namespace FlexCharge.Payments.Consumers;
//
// /// <summary>
// /// Initiated the payout process (ach)
// /// </summary>
// public class ExecuteScheduledPayoutsCommandConsumer : IConsumer<ExecuteScheduledPayoutsCommand>
// {
//     private PostgreSQLDbContext _context;
//     private readonly IPublishEndpoint _publishEndpoint;
//     private readonly IPayoutService _payoutService;
//
//
//     public ExecuteScheduledPayoutsCommandConsumer(
//         PostgreSQLDbContext context, IPublishEndpoint publishEndpoint, IPayoutService payoutService)
//     {
//         _context = context;
//         _publishEndpoint = publishEndpoint;
//         _payoutService = payoutService;
//     }
//
//     public async Task Consume(ConsumeContext<ExecuteScheduledPayoutsCommand> context)
//     {
//         using var workspan = Workspan.Start<ExecuteScheduledPayoutsCommandConsumer>()
//             .Request(context.Message);
//         
//         try
//         {   
//             await _payoutService.ExecutePayouts();
//         }
//         catch (Exception e)
//         {
//             workspan.RecordException(e, true);
//         }
//     }
// }