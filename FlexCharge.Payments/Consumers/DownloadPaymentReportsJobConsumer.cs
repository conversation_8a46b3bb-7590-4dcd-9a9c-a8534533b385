using System.Threading.Tasks;
using FlexCharge.Common.Telemetry;
using MassTransit;
using FlexCharge.Contracts.Commands;
using FlexCharge.Payments.Services;
using FlexCharge.Payments.Services.DisputeServices;
using FlexCharge.Common.BackgroundJobs;

namespace FlexCharge.Payments.Consumers
{
    public class DownloadPaymentReportsJobConsumer : IConsumer<ReportsToS3FileTransferCommand>
    {
        private readonly IReportsService _reportsService;
        private readonly IBackgroundWorkerCommandQueue _backgroundWorkerCommandQueue;

        public DownloadPaymentReportsJobConsumer(IReportsService reportsService, IBackgroundWorkerCommandQueue backgroundWorkerCommandQueue)
        {
            _reportsService = reportsService;
            _backgroundWorkerCommandQueue = backgroundWorkerCommandQueue;
        }

        public async Task Consume(ConsumeContext<ReportsToS3FileTransferCommand> context)
        {
            using var workspan = Workspan.Start<DownloadPaymentReportsJobConsumer>()
                .Context(context);
            workspan.Log.Information("Processing DownloadPaymentReportsJobConsumer");

            _backgroundWorkerCommandQueue.Enqueue(new ProcessS3FileTransferCommand());
        }
    }
}