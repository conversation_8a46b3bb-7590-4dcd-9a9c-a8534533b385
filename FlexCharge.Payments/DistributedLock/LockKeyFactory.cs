using FlexCharge.Common.Cache;
using FlexCharge.Common.DistributedLock;
using System;

namespace FlexCharge.Payments.DistributedLock
{
    public class LockKeyFactory : LockKeyFactoryBase
    {
        // public static string CreateCaptureKey(Guid id) =>
        //     CreateScopedKey($"Payments_Capture_{id}");
        public static string CreateRefundLockKey(Guid orderId) =>
            CreateScopedKey($"Payments_Refund_{orderId}");

        public static string CreateStripeBillingPortalDataLockKey(Guid mid) =>
            CreateScopedKey($"Payments_Stripe_PP_Data_{mid}");
    }
}