#if DEBUG
//#define MEASURE_PERFORMANCE
#endif

using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common;
using FlexCharge.Common.Authentication;
using FlexCharge.Common.HTTP;
using FlexCharge.Payments.Entities;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;

public abstract class AuditableDbContext<TDbContext> : DbContext
    where TDbContext : DbContext
{
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IOptions<AppOptions> _appOptions;

    public DbSet<AuditLog> AuditLogs { get; set; }

    public AuditableDbContext()
    {
    }

    public AuditableDbContext(DbContextOptions<TDbContext> options,
        IServiceProvider serviceProvider)
        : base(options)
    {
        _httpContextAccessor = serviceProvider.GetRequiredService<IHttpContextAccessor>();
        _appOptions = serviceProvider.GetRequiredService<IOptions<AppOptions>>();
    }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        ExcludeEntitiesFromAuditLog(_excludedEntities);

        base.OnConfiguring(optionsBuilder);
    }

    /// <summary>
    /// Override this method to exclude entities from audit log
    /// </summary>
    /// <param name="excludedEntities"></param>
    protected virtual void ExcludeEntitiesFromAuditLog(List<Type> excludedEntities)
    {
    }


    List<Type> _excludedEntities = new();

    public override int SaveChanges()
    {
        AuditEntries();
        return base.SaveChanges();
    }

    public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        AuditEntries();
        return base.SaveChangesAsync(cancellationToken);
    }


    private void AuditEntries()
    {
        #if MEASURE_PERFORMANCE
                var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        #endif
        
        if (_httpContextAccessor?.HttpContext?.User == null)
        {
            return;
        }

        List<AuditEntry> auditEntries = new();

        foreach (var entry in ChangeTracker.Entries().Where(e =>
                     e.State == EntityState.Added || e.State == EntityState.Modified || e.State == EntityState.Deleted))
        {
            if (_excludedEntities.Contains(entry.Entity.GetType()))
                continue;

            var auditEntry = new AuditEntry(entry);

            auditEntries.Add(auditEntry);
        }

        if (auditEntries.Any())
        {
            AuditLogs.AddRange(
                auditEntries
                    .Select(x => x.ToAuditLog(_httpContextAccessor, _appOptions.Value, includeOnlyChangedColumns: true))
                    .Where(x => x.AffectedColumns != null || x.OldValues != null || x.NewValues != null)
            );
        }

#if MEASURE_PERFORMANCE
        stopwatch.Stop();
        Console.WriteLine($"AuditEntries took {stopwatch.ElapsedMilliseconds} ms");
#endif
    }

    #region AuditEntry class

    class AuditEntry
    {
        public AuditEntry(EntityEntry entry)
        {
            Entry = entry;
        }

        private EntityEntry Entry { get; }
        private Dictionary<string, object> KeyValues { get; } = new();
        private Dictionary<string, object> OldValues { get; } = new();
        private Dictionary<string, object> NewValues { get; } = new();
        private List<string> ChangedColumns { get; } = new();


        private void ProcessChanges(bool includeOnlyChangedColumns)
        {
            foreach (PropertyEntry property in Entry.Properties)
            {
                string propertyName = property.Metadata.Name;
                string dbColumnName = property.Metadata.GetColumnName();

                if (property.Metadata.IsPrimaryKey())
                {
                    KeyValues[propertyName] = property.CurrentValue;
                    continue;
                }

                // Skip temporary properties
                if (property.IsTemporary)
                    continue;

                switch (Entry.State)
                {
                    case EntityState.Added:
                        NewValues[propertyName] = property.CurrentValue;
                        break;

                    case EntityState.Deleted:
                        OldValues[propertyName] = property.OriginalValue;
                        break;

                    case EntityState.Modified:
                        if (property.IsModified)
                        {
                            if (includeOnlyChangedColumns)
                            {
                                if (property.OriginalValue == null && property.CurrentValue == null)
                                    continue;

                                if (property.OriginalValue?.ToString() == property.CurrentValue?.ToString())
                                    continue;
                            }

                            ChangedColumns.Add(dbColumnName);

                            OldValues[propertyName] = property.OriginalValue;
                            NewValues[propertyName] = property.CurrentValue;
                        }

                        break;
                }
            }
        }

        public AuditLog ToAuditLog(IHttpContextAccessor httpContextAccessor, AppOptions appOptions,
            bool includeOnlyChangedColumns)
        {
            ProcessChanges(includeOnlyChangedColumns);

            var audit = new AuditLog()
            {
                Microservice = appOptions.Name,
                TableName = Entry.Metadata.GetTableName(),
                Entity = Entry.Entity.GetType().Name,
                Operation = Entry.State.ToString(),
                PrimaryKey = JsonSerializer.Serialize(KeyValues),
                OldValues = OldValues.Count == 0 ? null : JsonSerializer.Serialize(OldValues),
                NewValues = NewValues.Count == 0 ? null : JsonSerializer.Serialize(NewValues),
                AffectedColumns = ChangedColumns.Count == 0 ? null : JsonSerializer.Serialize(ChangedColumns)
            };

            EnrichWithUserInformation(audit, httpContextAccessor);

            OldValues.Clear();
            KeyValues.Clear();
            NewValues.Clear();

            return audit;
        }

        private void EnrichWithUserInformation(AuditLog audit, IHttpContextAccessor httpContextAccessor)
        {
            var httpContext = httpContextAccessor.HttpContext;
            if (httpContext != null)
            {
                var user = httpContext.User;
                if (user != null)
                {
                    audit.UserId = GetUserId(user);
                    audit.UserFirstName = GetFirstName(user);
                    audit.UserLastName = GetLastName(user);
                    audit.UserRole = GetRole(user);
                    audit.UserEmail = GetEmail(user);
                }

                audit.UserIp = HttpContextHelper.GetConsumerIP(httpContext);
            }

            if (audit.UserId == null)
            {
                audit.UserId = "System";
            }
        }
    }

    #endregion

    public List<string> GetAllTableNames()
    {
        return Model.GetEntityTypes()
            .Where(y => !_excludedEntities.Contains(y.ClrType))
            .Select(x => x.GetTableName())
            .ToList();
    }

    private static string? GetUserId(ClaimsPrincipal? user)
    {
        return user?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
    }

    private static string GetFirstName(ClaimsPrincipal user)
    {
        return user?.FindFirst(ClaimTypes.GivenName)?.Value;
    }

    private static string GetLastName(ClaimsPrincipal user)
    {
        return user?.FindFirst(ClaimTypes.Surname)?.Value;
    }

    private static string GetEmail(ClaimsPrincipal user)
    {
        return user?.FindFirst(ClaimTypes.Email)?.Value;
    }

    private static string GetRole(ClaimsPrincipal user)
    {
        var role = user?.FindFirst(ClaimTypes.Role)?.Value;

        if (string.IsNullOrWhiteSpace(role))
            role = user?.FindFirst(MyClaimTypes.COGNITO_GROUP)?.Value;

        return role;
    }
}