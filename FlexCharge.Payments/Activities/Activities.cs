using FlexCharge.Common.Activities.Attributes;
using FlexCharge.Common.Shared.Activities;
using FlexCharge.Contracts.Activities;

namespace FlexCharge.Payments.Activities;

[Category(ActivityCategories.Payments_Processing_Errors, ActivityInformationLevelFlags.Error)]
public enum PaymentsErrorActivities
{
    [Statistics] Payments_AuthorizePayment_Error,
    [Statistics] Payments_CapturePayment_Error,
    [Statistics] Payments_SalePayment_Error,
    [Statistics] Payments_VoidPayment_Error,
    [Statistics] Payments_CreditPayment_Error,

    [Statistics] Payments_ChargePayment_Error,

    [Statistics] Payments_CanChargeExternalToken_Error,

    [Statistics] Payments_AchDebitPayment_Error,
    [Statistics] Payments_AchCreditPayment_Error,

    [Statistics] Payments_AchCancelPayment_Error,

    [Statistics] ResponseCodeMapping_MappingMissing,
}

[Category(ActivityCategories.Payments_Processing, ActivityInformationLevelFlags.Information)]
public enum PaymentsActivities
{
    [Statistics] Payments_AuthorizePayment_Succeeded,
    [Statistics] Payments_AuthorizePayment_Failed,
    [Statistics] Payments_CapturePayment_Succeeded,
    [Statistics] Payments_CapturePayment_Failed,
    [Statistics] Payments_SalePayment_Succeeded,
    [Statistics] Payments_SalePayment_Failed,
    [Statistics] Payments_VoidPayment_Succeeded,
    [Statistics] Payments_VoidPayment_Failed,
    [Statistics] Payments_CreditPayment_Succeeded,
    [Statistics] Payments_CreditPayment_Failed,

    [Statistics] Payments_ChargePayment_Succeeded,
    [Statistics] Payments_ChargePayment_Failed,

    [Statistics] Payments_CanChargeExternalToken_Succeeded,
    [Statistics] Payments_CanChargeExternalToken_Failed,

    [Statistics] Payments_AchDebitPayment_Succeeded,
    [Statistics] Payments_AchDebitPayment_Failed,
    [Statistics] Payments_AchCancel_Succeeded,
    [Statistics] Payments_AchCancel_Failed,

    [Statistics] Payments_AchCreditPayment_Succeeded,
    [Statistics] Payments_AchCreditPayment_Failed,
}

[Category(ActivityCategories.Payments_Orchestration, ActivityInformationLevelFlags.Information)]
public enum PaymentsOrchestrationActivities
{
    [Statistics] Payments_Orchestration_NoSupportedGateways,
    [Statistics] Payments_Orchestration_StrategyApplied,
}

[Category(ActivityCategories.PartnerPayments_Processing, ActivityInformationLevelFlags.Information)]
public enum PartnerPaymentsActivities
{
    [Statistics] PartnerPayments_AchDebitPayment_Succeeded,
    [Statistics] PartnerPayments_AchDebitPayment_Failed,

    [Statistics] PartnerPayments_AchCreditPayment_Succeeded,
    [Statistics] PartnerPayments_AchCreditPayment_Failed,

    [Statistics] PartnerPayments_AchCancel_Succeeded,
    [Statistics] PartnerPayments_AchCancel_Failed,
}

[Category(ActivityCategories.PartnerPayments_Processing, ActivityInformationLevelFlags.Error)]
public enum PartnerPaymentsErrorActivities
{
    [Statistics] PartnerPayments_AchDebitPayment_Error,
    [Statistics] PartnerPayments_AchCreditPayment_Error,
    [Statistics] PartnerPayments_AchCancelPayment_Error,
}

[Category(ActivityCategories.Transaction_Monitoring, ActivityInformationLevelFlags.Information)]
public enum TransactionMonitoringActivities
{
    Payments_UpdatingTransactionStatuses_Starting,
    Payments_TransactionMonitoring_Started,
    Payments_TransactionMonitoring_Stopped,
}

[Category(ActivityCategories.Transaction_Monitoring_Errors, ActivityInformationLevelFlags.Error)]
public enum TransactionMonitoringErrorActivities
{
    Payments_UpdatingTransactionStatuses_Error,
    Payments_UpdatingTransactionStatus_Error
}

[Category(ActivityCategories.PartnerTransaction_Monitoring, ActivityInformationLevelFlags.Information)]
public enum PartnerTransactionMonitoringActivities
{
    PartnerPayments_UpdatingTransactionStatuses_Starting,
    PartnerPayments_TransactionMonitoring_Started,
    PartnerPayments_TransactionMonitoring_Stopped,
}

[Category(ActivityCategories.PartnerTransaction_Monitoring_Errors, ActivityInformationLevelFlags.Error)]
public enum PartnerTransactionMonitoringErrorActivities
{
    PartnerPayments_UpdatingTransactionStatuses_Error,
    PartnerPayments_UpdatingTransactionStatus_Error
}

[Category(ActivityCategories.Payments_AchTransfer, ActivityInformationLevelFlags.Information)]
public enum AchTransferActivities
{
    Payments_AchTransfer_StatusProcessing_Starting,
    Payments_AchTransfer_StatusProcessing_StatusReceived,
    Payments_AchTransfer_StatusProcessing_StatusChanged,

    [InformationLevel(ActivityInformationLevelFlags.Error)]
    Payments_AchTransfer_Failed,
    Payments_AchTransfer_Held,
    Payments_AchTransfer_Processing,
    Payments_AchTransfer_Succeeded,
    Payments_AchTransfer_Canceled,

    [InformationLevel(ActivityInformationLevelFlags.Error)]
    Payments_AchTransfer_Returned,
}

[Category(ActivityCategories.Payments_PartnerAchTransfer, ActivityInformationLevelFlags.Information)]
public enum PartnerAchTransferActivities
{
    PartnerPayments_AchTransfer_StatusProcessing_Starting,

    [InformationLevel(ActivityInformationLevelFlags.Error)]
    PartnerPayments_AchTransfer_Failed,
    PartnerPayments_AchTransfer_Held,
    PartnerPayments_AchTransfer_Processing,
    PartnerPayments_AchTransfer_Succeeded,
    PartnerPayments_AchTransfer_Canceled,
    PartnerPayments_AchTransfer_Returned,

    PartnerPayments_AchTransfer_StatusProcessing_StatusReceived,
    PartnerPayments_AchTransfer_StatusProcessing_StatusChanged,
}

[Category(ActivityCategories.Payments_AchTransfer_Errors, ActivityInformationLevelFlags.Error)]
public enum AchTransferErrorActivities
{
    Payments_AchTransfer_StatusProcessing_UnknownStatus,
    Payments_AchTransfer_StatusProcessing_IncorectStatus,
    Payments_AchTransfer_StatusProcessing_Error,
}

[Category(ActivityCategories.Payments_PartnerAchTransfer_Errors, ActivityInformationLevelFlags.Error)]
public enum PartnerAchTransferErrorActivities
{
    PartnerPayments_AchTransfer_StatusProcessing_Error,
    PartnerPayments_AchTransfer_StatusProcessing_IncorectStatus,
    PartnerPayments_AchTransfer_StatusProcessing_UnknownStatus
}

[Category(ActivityCategories.Payments_DisputeProcessing, ActivityInformationLevelFlags.Information)]
public enum DisputeActivities
{
    [Statistics] DisputeProcessing_PreDisputeCreate_Started,
    [Statistics] DisputeProcessing_DisputeCreate_Succeeded,
    [Statistics] DisputeProcessing_DisputeAlertCreated,

    [Statistics] DisputeProcessing_PreDisputeUpdate_Succeeded,

    [Statistics] DisputeProcessing_IssuedRefund_PreDispute_Started,
    [Statistics] DisputeProcessing_IssuedRefund_PreDispute_Succeeded,

    [Statistics] DisputeProcessing_DisputeMatch_Started,
    [Statistics] DisputeProcessing_DisputeMatch_TransactionFound,
    [Statistics] DisputeProcessing_DisputeMatch_Succeeded,

    [Statistics] DisputeProcessing_DisputeDeleted,

    [Statistics] DisputeProcessing_DisputeReconcile_Started,
    [Statistics] DisputeProcessing_DisputeReconcile_Succeeded,
}

[Category(ActivityCategories.Payments_DisputeProcessing_Errors, ActivityInformationLevelFlags.Error)]
public enum DisputeErrorActivities
{
    [Statistics] DisputeProcessing_DisputeExists_Error,
    [Statistics] DisputeProcessing_PreDisputeCreate_Error,
    [Statistics] DisputeProcessing_DisputeAlertCreate_Error,
    [Statistics] DisputeProcessing_DisputeCreate_Error,
    [Statistics] DisputeProcessing_DisputeGet_Error,
    [Statistics] DisputeProcessing_DisputeMatch_Error,
}

[Category(ActivityCategories.Payments_ThreeDSecure, ActivityInformationLevelFlags.Information)]
public enum ThreeDSecureActivities
{
    ThreeDSecure_GetSupportedVersions_Succeeded,
    ThreeDSecure_GetChallengeResults_Succeeded,
    ThreeDSecure_Authenticate3DS_Succeeded
}

[Category(ActivityCategories.Payments_ThreeDSecure_Errors, ActivityInformationLevelFlags.Error)]
public enum ThreeDSecureErrorActivities
{
    ThreeDSecure_PreAuthenticationFailed,
    ThreeDSecure_GetChallengeResults_Error,
    ThreeDSecure_Authenticate3DS_Error
}

[Category(ActivityCategories.Payments_Gateway, ActivityInformationLevelFlags.Information)]
public enum GatewayActivities
{
}

[Category(ActivityCategories.Payments_Gateway_Errors, ActivityInformationLevelFlags.Error)]
public enum GatewayErrorActivities
{
    Gateway_GatewayInformationNotFound_Error,
    Gateway_MerchanNotFound_Error,
    Gateway_Global_Error
}

[Category(ActivityCategories.Payments_API_Errors, ActivityInformationLevelFlags.Error)]
public enum ApiErrorActivities
{
    ModelStateError,
    MissingMerchantId,
    EndpointCriticalApiError,
    EndpointValidationError,
}

[Category(ActivityCategories.Payments_PaymentInstrument, ActivityInformationLevelFlags.Information)]
public enum PaymentInstrumentActivities
{
}

[Category(ActivityCategories.Payments_PaymentInstrument_Errors, ActivityInformationLevelFlags.Error)]
public enum PaymentInstrumentErrorActivities
{
    PaymentInstrument_Error,
    PaymentInstrument_GetPaymentMethod_Error,
    PaymentInstrument_GetMethodById_Error,
    PaymentInstrument_GetMethodByToken_Error,
}

[Category(ActivityCategories.Payments_IncomingWebhooks_Processing, ActivityInformationLevelFlags.Information)]
public enum PaymentsIncomingWebhooksActivities
{
    [Statistics] Payments_IncomingWebhooks_AlertProvider_Requested,
    [Statistics] Payments_IncomingWebhooks_AlertProvider_Succeeded,

    [Statistics] Payments_IncomingWebhooks_Webhook_Requested,
    [Statistics] Payments_IncomingWebhooks_Webhook_Recieved,
    [Statistics] Payments_IncomingWebhooks_Webhook_Succeeded,
}

[Category(ActivityCategories.Payments_IncomingWebhooks_Errors, ActivityInformationLevelFlags.Error)]
public enum PaymentsIncomingWebhooksErrorActivities
{
    [Statistics] Payments_IncomingWebhooks_Error,
    [Statistics] Payments_IncomingWebhooks_DuplicateError,

    [Statistics] Payments_IncomingWebhooks_AlertProvider_Error,
    [Statistics] Payments_IncomingWebhooks_GetAlertProviderToken_Error,
}

[Category(ActivityCategories.ExternalPaymentProvider_Processing, ActivityInformationLevelFlags.Information)]
public enum ExternalSubscriptionActivities
{
    ExternalProvider_CreateSubscription_Succeeded,
    ExternalProvider_CancelSubscription_Succeded,
    ExternalProvider_CancelSubscription_Failed,

    // ExternalProvider_Subscription_Expired,
    // ExternalProvider_Subscription_Paid,
    // ExternalProvider_Subscription_Unpaid
    ExternalProvider_MarkInvoicePaidOutOfBand_Succeded,
    ExternalProvider_MarkInvoicePaidOutOfBand_Failed
}

[Category(ActivityCategories.ExternalPaymentProvider_Errors, ActivityInformationLevelFlags.Error)]
public enum ExternalSubscriptionErrorActivities
{
    ExternalProvider_CreateSubscription_Error,
    ExternalProvider_CancelSubscription_Error,
    ExternalProvider_MarkInvoicePaidOutOfBand_Error
}

[Category(ActivityCategories.Payments_DisputeReports_Processing, ActivityInformationLevelFlags.Information)]
public enum DisputeReportsActivities
{
    [Statistics] DisputeReports_RetryFailedRecords_Requested,

    [Statistics] DisputeReports_Import_Requested,
    [Statistics] DisputeReports_Import_Started,
    [Statistics] DisputeReports_Import_Succeeded,

    [Statistics] DisputeReports_GetAlert_Started,
    [Statistics] DisputeReports_GetAlert_Succeeded,

    [Statistics] DisputeReports_Match_Requested,

    [Statistics] DisputeReports_IncomingFileDownloadedAndUploaded,
    [Statistics] DisputeReports_IncomingFileDownloadedFromS3AndProcessed,
    [Statistics] DisputeReports_IncomingFileDownloadedAndUpload_Started,

    [Statistics] DisputeReports_ReportsPolling_Started,
    [Statistics] DisputeReports_ReportsPolling_Succeeded,
    [Statistics] DisputeReports_Reconcile_Requested,
}

[Category(ActivityCategories.Payments_DisputeReports_Errors, ActivityInformationLevelFlags.Error)]
public enum DisputeReportsErrorsActivities
{
    [Statistics] DisputeReports_Import_Error,
    [Statistics] DisputeReports_RetryDisputeReport_Error,
    [Statistics] DisputeReports_GetAlert_Error,
    [Statistics] DisputeReports_Validation_Error,
    [Statistics] DisputeReports_AddUnmatchedPreDispute_Error,
    [Statistics] DisputeReports_Match_Error,
    [Statistics] DisputeReports_Reconcile_Error,
    [Statistics] DisputeReports_IncomingFilesDownloaded_Error,
    [Statistics] DisputeReports_IncomingFileDownloadedFromS3_Error,
    [Statistics] DisputeReports_ReportsPolling_Error,
    [Statistics] DisputeProcessing_AlertProvider_Error,
    [Statistics] DisputeProcessing_GetAlertProviderToken_Error,
}

[Category(ActivityCategories.AccountUpdater, ActivityInformationLevelFlags.Information)]
public enum AccountUpdaterActivities
{
    [Statistics] AccountUpdater_RealTimeUpdate_Requested,
    [Statistics] AccountUpdater_UpdateSkipped_AlreadyInCache,

    [Statistics] AccountUpdater_CardUpdated,
    [Statistics] AccountUpdater_ExpiryUpdated,

    [Statistics] AccountUpdater_CardNotUpdated,

    [Statistics] AccountUpdater_CardNotUpdated_NoChanges,
    [Statistics] AccountUpdater_CardNotUpdated_NoMatch,
    [Statistics] AccountUpdater_CardNotUpdated_AccountClosed,
    [Statistics] AccountUpdater_CardNotUpdated_NotEligible,
    [Statistics] AccountUpdater_CardNotUpdated_Other,

    //AccountUpdater_CardNotUpdated_NewCard,
}

[Category(ActivityCategories.AccountUpdater_Errors, ActivityInformationLevelFlags.Error)]
public enum AccountUpdaterErrorActivities
{
    [Statistics] AccountUpdater_InternalValidation_Error,
    [Statistics] AccountUpdater_Request_Error,
}