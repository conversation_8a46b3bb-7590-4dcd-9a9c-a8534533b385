#See https://aka.ms/containerfastmode to understand how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src
COPY ["FlexCharge.Onboarding/FlexCharge.Onboarding.csproj", "FlexCharge.Onboarding/"]
COPY ["FlexCharge.Common/FlexCharge.Common.csproj", "FlexCharge.Common/"]
COPY ["FlexCharge.Utils/FlexCharge.Utils.csproj", "FlexCharge.Utils/"]
RUN dotnet restore "FlexCharge.Onboarding/FlexCharge.Onboarding.csproj"
COPY . .
WORKDIR "/src/FlexCharge.Onboarding"
RUN dotnet build "FlexCharge.Onboarding.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "FlexCharge.Onboarding.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "FlexCharge.Onboarding.dll"]