using System;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using FlexCharge.Common.Cache;
using FlexCharge.Common.Cache.DistributedMemoryDatabase;
using FlexCharge.Common.Shared.Tracking;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.Commands.Tracking;
using Microsoft.EntityFrameworkCore;

namespace FlexCharge.Tracking.Services.MerchantsInformationCacheService;

class MerchantInformationCacheService : IMerchantInformationCacheService
{
    private readonly IExternalRequestsDistributedCache _externalRequestsDistributedCache;
    private readonly IExternalRequestsDistributedMemoryDatabase _externalRequestsDistributedMemoryDatabase;
    private readonly PostgreSQLDbContext _dbContext;
    private readonly IMapper _mapper;

    public MerchantInformationCacheService(
        IExternalRequestsDistributedCache externalRequestsDistributedCache,
        IExternalRequestsDistributedMemoryDatabase externalRequestsDistributedMemoryDatabase,
        PostgreSQLDbContext dbContext,
        IMapper mapper)
    {
        _externalRequestsDistributedCache = externalRequestsDistributedCache;
        _externalRequestsDistributedMemoryDatabase = externalRequestsDistributedMemoryDatabase;
        _dbContext = dbContext;
        _mapper = mapper;
    }

    public async Task<MerchantInformation?> GetMerchantInformationAsync(Guid mid)
    {
        var merchantCacheKey = TrackingSharedCacheKeyFactory.CreateGetMerchantByMidKey(mid);

        var merchantInformation = await _externalRequestsDistributedCache
            .GetValueAsync<MerchantInformation>(merchantCacheKey.Key);

        if (merchantInformation == null)
        {
            var merchantEntity = await _dbContext.Merchants.Where(x => x.Mid == mid).SingleOrDefaultAsync();

            if (merchantEntity != null)
            {
                merchantInformation = _mapper.Map<MerchantInformation>(merchantEntity);
                await _externalRequestsDistributedCache.SetAsync(merchantCacheKey.Key, merchantInformation,
                    merchantCacheKey.CacheOptions);
            }
        }

        return merchantInformation;
    }


    public async Task InvalidateMerchantInformationCacheAsync(Guid mid)
    {
        var merchantCacheKey = TrackingSharedCacheKeyFactory.CreateGetMerchantByMidKey(mid);
        await _externalRequestsDistributedCache.RemoveAsync(merchantCacheKey.Key);
    }

    public async Task InvalidateMerchantSiteConfigurationCacheAsync(Guid mid, Guid? siteId)
    {
        if (siteId.HasValue)
        {
            var merchantSiteConfigurationKey =
                TrackingSharedCacheKeyFactory.CreateMerchantSiteConfigurationKey(mid, siteId);
            await _externalRequestsDistributedCache.RemoveAsync(merchantSiteConfigurationKey.Key);
        }
        else
        {
            await _externalRequestsDistributedMemoryDatabase.DeleteKeysWithPrefixAsync(
                TrackingSharedCacheKeyFactory.GetScopedMerchantSiteConfigurationKeysPrefix(mid).Key);
        }
    }

    public async Task<GetMerchantSiteConfigurationCommandResponse?> GetMerchantSiteConfigurationAsync(Guid mid,
        Guid siteId)
    {
        var merchantSiteConfigurationKey =
            TrackingSharedCacheKeyFactory.CreateMerchantSiteConfigurationKey(mid, siteId);

        var merchantSiteConfiguration = await _externalRequestsDistributedCache
            .GetValueAsync<GetMerchantSiteConfigurationCommandResponse>(merchantSiteConfigurationKey.Key);

        if (merchantSiteConfiguration == null)
        {
            var merchantSiteEntity = await _dbContext.Configurations
                .Where(x => x.MerchantId == mid && x.SiteId == siteId)
                .SingleOrDefaultAsync();

            if (merchantSiteEntity != null)
            {
                merchantSiteConfiguration =
                    _mapper.Map<GetMerchantSiteConfigurationCommandResponse>(merchantSiteEntity);
                await _externalRequestsDistributedCache.SetAsync(merchantSiteConfigurationKey.Key,
                    merchantSiteConfiguration,
                    merchantSiteConfigurationKey.CacheOptions);
            }
        }

        return merchantSiteConfiguration;
    }
}