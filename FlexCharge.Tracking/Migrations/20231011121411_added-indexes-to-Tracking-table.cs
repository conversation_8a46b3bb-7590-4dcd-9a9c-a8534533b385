using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlexCharge.Tracking.Migrations
{
    /// <inheritdoc />
    public partial class addedindexestoTrackingtable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "IX_Tracking_CreatedOn",
                table: "Tracking",
                column: "CreatedOn");

            migrationBuilder.CreateIndex(
                name: "IX_Tracking_MerchantId",
                table: "Tracking",
                column: "MerchantId");

            migrationBuilder.CreateIndex(
                name: "IX_Tracking_ModifiedOn",
                table: "Tracking",
                column: "ModifiedOn");

            migrationBuilder.CreateIndex(
                name: "IX_Tracking_SenseKey",
                table: "Tracking",
                column: "SenseKey");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_Tracking_CreatedOn",
                table: "Tracking");

            migrationBuilder.DropIndex(
                name: "IX_Tracking_MerchantId",
                table: "Tracking");

            migrationBuilder.DropIndex(
                name: "IX_Tracking_ModifiedOn",
                table: "Tracking");

            migrationBuilder.DropIndex(
                name: "IX_Tracking_SenseKey",
                table: "Tracking");
        }
    }
}
