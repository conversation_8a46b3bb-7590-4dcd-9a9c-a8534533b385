using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace FlexCharge.Common.Authentication.Filters
{
    public class ClaimRequirementAttribute : TypeFilterAttribute
    {
        public ClaimRequirementAttribute(string claimType, string claimValue) : base(typeof(ClaimRequirementFilter))
        {
            Arguments = new object[] { new Claim(claimType, claimValue) };
        }
    }
    public class ClaimRequirementFilter : IAsyncAuthorizationFilter
    {
        readonly Claim _claim;

        public ClaimRequirementFilter(Claim claim)
        {
            _claim = claim;
        }


        public async Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            var hasClaim = context.HttpContext.User.Claims.Any(c => c.Type == _claim.Type && c.Value == _claim.Value);
            if (!hasClaim)
            {
                context.Result = new ForbidResult();
            }
        }
    }

    //public class ClaimsRequirementAttribute : TypeFilterAttribute
    //{
    //    public ClaimsRequirementAttribute(string claimType, string[] claimValues) : base(typeof(ClaimsRequirementFilter))
    //    {
    //        foreach (var claimValue in claimValues)
    //        {
    //            Arguments = new object[] { new Claim(claimType, claimValue) };
    //        }
    //    }
    //}

    //public class ClaimsRequirementFilter : IAsyncAuthorizationFilter
    //{
    //    readonly Claim[] _claims;

    //    public ClaimsRequirementFilter(Claim[] claims)
    //    {
    //        _claims = claims;
    //    }


    //    public async Task OnAuthorizationAsync(AuthorizationFilterContext context)
    //    {
    //        var hasClaim = false;
    //        foreach (var _claim in _claims)
    //        {
    //            hasClaim = context.HttpContext.User.Claims.Any(c => c.Type == _claim.Type && c.Value == _claim.Value);
    //        }

    //        if (!hasClaim)
    //        {
    //            context.Result = new ForbidResult();
    //        }
    //    }
    //}


}
