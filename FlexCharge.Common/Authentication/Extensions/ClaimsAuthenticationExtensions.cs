using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;

namespace FlexCharge.Common.Authentication;

public static class ClaimsExtensions
{
    /// <summary>
    /// Checks if the user is in the specified group
    /// </summary>
    /// <param name="claims"></param>
    /// <param name="group"></param>
    /// <returns></returns>
    public static bool IsUserInGroup(this IEnumerable<Claim> claims, string group)
    {
        return claims.Any(x => x.Type == MyClaimTypes.COGNITO_GROUP && x.Value == group);
    }
 
}