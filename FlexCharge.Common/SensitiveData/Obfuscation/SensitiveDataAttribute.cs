using System;
using FlexCharge.Common.SensitiveData.Obfuscation.Obfuscators;
using FlexCharge.Common.SensitiveData.Obfuscation.Obfuscators.Implementations;

namespace FlexCharge.Common.SensitiveData.Obfuscation;

[AttributeUsage(AttributeTargets.Property, AllowMultiple = false)]
public class SensitiveDataAttribute : Attribute
{
    private readonly ObfuscationType _obfuscationType;
    //See: https://dev.to/krusty93/hide-sensitive-data-in-azure-application-insights-logs-of-our-aspnet-core-apis-4ji7

    private ObfuscatorBase _obfuscator;

    private ObfuscatorBase Obfuscator
    {
        get
        {
            // Lazy loading required to see obfuscator property values (e.g. LeaveUnobfuscatedSymbols)
            return _obfuscator ??= GetObfuscator(_obfuscationType);
        }
    }

    public SensitiveDataAttribute(ObfuscationType obfuscationType)
    {
        _obfuscationType = obfuscationType;
    }

    private ObfuscatorBase GetObfuscator(ObfuscationType obfuscationType)
    {
        return obfuscationType switch
        {
            ObfuscationType.HeadVisible => new MaskTailCharsObfuscator(LeaveUnobfuscatedSymbols),
            //ObfuscationType.TailVisible => new SensitiveTailVisible(LeaveUnobfuscatedSymbols),
            ObfuscationType.MaskAllChars => new MaskAllCharsObfuscator(),
            ObfuscationType.CreditCardMaskAllDigits => new CreditCardMaskAllDigitsObfuscator(),
            ObfuscationType.MakeNull => new MakeNullObfuscator(),
            _ => throw new NotImplementedException(
                $"Unknown obfuscator type {nameof(obfuscationType)}: {obfuscationType}")
        };
    }

    public int LeaveUnobfuscatedSymbols { get; set; }

    internal string Obfuscate(string strValue)
    {
        return Obfuscator.Obfuscate(strValue);
    }

    internal string Obfuscate(Guid guid) => Obfuscator.Obfuscate(guid.ToString());

    internal string Obfuscate(DateTime dateTime) => Obfuscator.Obfuscate(dateTime.ToString());

    internal string Obfuscate(Enum @enum) => Obfuscator.Obfuscate(@enum.ToString());

    internal virtual string ObfuscateDefault(object value) => Obfuscator.Obfuscate(value?.ToString());
}