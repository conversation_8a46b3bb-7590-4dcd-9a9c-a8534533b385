using System;

namespace FlexCharge.Common.Activities.Exceptions;

internal class ExceptionDescription
{
    public string Type { get; }
    public string Message { get; }
    public string StackTrace { get; }
    public ExceptionDescription InnerException { get; }

    public ExceptionDescription(Type exceptionType, string message,
        string stackTrace,
        ExceptionDescription? innerException)
    {
        Type = exceptionType.FullName;
        Message = message;
        StackTrace = stackTrace;
        InnerException = innerException;
    }
}