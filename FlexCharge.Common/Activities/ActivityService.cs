#define USE_CHANNELS

#if DEBUG
//#define MEASURE_PERFORMANCE
#endif

using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Channels;
using System.Threading.Tasks;
using FlexCharge.Common.Activities.Attributes;
using FlexCharge.Common.Activities.Exceptions;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts;
using FlexCharge.Utils;
using MassTransit;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.ObjectPool;
using Microsoft.Extensions.Options;
using Serilog.Events;
using IActivity = FlexCharge.Contracts.Activities.IActivity;

namespace FlexCharge.Common.Activities;

public class ActivityService : IActivityService
{
#if USE_CHANNELS
    private readonly IActivityChannel _activityChannel;
#else
    private readonly ObjectPool<ActivityCreatedEvent> _activityCreatedEventObjectPool;
    private readonly IPublishEndpoint _publisher;
#endif

    private ObjectPool<ActivityBuilder> _activityBuilderObjectPool;

    private readonly IServiceScope _serviceScope;

    private static string _source;
    private static string _domain;

    private static readonly ConcurrentDictionary<string, ActivityDescription> ActivityEnumEnumToMetadataCache = new();

    public static void Initialize(IServiceCollection services)
    {
#if USE_CHANNELS
        services.AddSingleton<IActivityChannel, ActivityChannel>();
        services.AddHostedService<ActivityChannelProcessor>();
#endif

        services.TryAddSingleton<ObjectPoolProvider, DefaultObjectPoolProvider>();

        ObjectPoolHelper.CreateObjectPool<ActivityCreatedEvent>(services,
            ActivityChannelProcessor.MAX_ACTIVITIES_IN_PUBLISH_BATCH);
        ObjectPoolHelper.CreateObjectPool<ActivityBuilder>(services);
    }

    public ActivityService(IServiceScopeFactory serviceScopeFactory)
    {
        _serviceScope = serviceScopeFactory.CreateScope();

#if USE_CHANNELS
        _activityChannel = _serviceScope.ServiceProvider.GetService<IActivityChannel>();
#else
        _publisher = _serviceScope.ServiceProvider.GetService<IPublishEndpoint>();
        _activityCreatedEventObjectPool = _serviceScope.ServiceProvider.GetService<ObjectPool<ActivityCreatedEvent>>();
#endif

        _activityBuilderObjectPool = _serviceScope.ServiceProvider.GetService<ObjectPool<ActivityBuilder>>();

        var activityServiceOptions = _serviceScope.ServiceProvider.GetService<IOptions<ActivityServiceOptions>>();

        _source = activityServiceOptions.Value.Source;
        _domain = activityServiceOptions.Value.Domain;

        if (_source == null)
        {
            var appOptions = _serviceScope.ServiceProvider.GetService<IOptions<AppOptions>>();
            _source = appOptions.Value.Name;
        }
    }

    ~ActivityService()
    {
        _serviceScope.Dispose();
    }

    #region Activity Factory Methods

    private void CreateActivity(Workspan workspan, ActivityBuilder activityBuilder,
        string name, object data = null, Guid? activityId = null)
    {
        var activity = new ActivityDTO
        {
            Name = name,
            ActionTimestamp = DateTime.UtcNow,
            Domain = _domain,
            Source = _source,
        };

        activityBuilder.Initialize(activity);


        if (activityId.HasValue) activity.Id = activityId.Value;
        else activity.Id = Guid.NewGuid();

        workspan?.Tag(nameof(activity.Domain), activity.Domain);
        workspan?.Tag(nameof(activity.Source), activity.Source);

        if (data != null)
        {
            activityBuilder.Data(data);
        }
    }


    public async ValueTask<IActivity> CreateActivityAsync<TActivityNameEnum>(TActivityNameEnum activity,
        object data,
        Guid? activityId, Action<ActivityBuilder> set)
        where TActivityNameEnum : Enum
    {
#if MEASURE_PERFORMANCE
        Stopwatch sw = Stopwatch.StartNew();
#endif

        Workspan workspan = Workspan.Current;
        Workspan workspanToDispose = null;

        var activityBuilder = _activityBuilderObjectPool.Get();
        try
        {
            // Reuse the current workspan if available or start a new one
            if (workspan == null)
            {
                workspan = workspanToDispose = Workspan.Start<ActivityService>();
            }

            CreateActivity(workspan, activityBuilder, activity.ToString(), data, activityId);

            PopulateActivityFromAttributes(activity, activityBuilder, out var activityDescription);

            if (set != null)
            {
                set(activityBuilder);
            }

            var createdActivity = activityBuilder.Activity;

            await EnrichActivityAsync(createdActivity);

            if (activityDescription.Notifications?.Length > 0)
                await ProcessNotificationsAsync(createdActivity, activityDescription);

            if (activityDescription.Statistics?.Length > 0)
                await ProcessStatisticsAsync(createdActivity, activityDescription);

            await PublishActivityAsync(workspan, createdActivity);

#if MEASURE_PERFORMANCE
            sw.Stop();
            workspan.Log.Information("Create activity: {ElapsedMilliseconds} ms", sw.ElapsedMilliseconds);
#endif

            return createdActivity;
        }
        finally
        {
            _activityBuilderObjectPool.Return(activityBuilder);

            workspanToDispose?.Dispose();
        }
    }

    private
#if !USE_CHANNELS
        async
#endif
        ValueTask PublishActivityAsync(Workspan workspan, IActivity createdActivity)
    {
        // try
        // {
        //     await SaveActivityLocallyAsync(createdActivity);
        // }
        // catch (Exception e)
        // {
        //     Workspan.Current?.RecordFatalException(e, "Failed to save activity locally");
        // }

#if USE_CHANNELS
        var writer = _activityChannel.Writer;

        // Fast path: Succeed without async state machine
        if (writer.TryWrite(createdActivity))
        {
            return ValueTask.CompletedTask; // No state machine
        }

        // Slow path: Create state machine only when needed
        workspan.Log.Warning("Activity channel is full - falling back to async write");

        return writer.WriteAsync(createdActivity);
#else
        await PublishActivityInternalAsync(new List<IActivity>() {createdActivity}, _publisher,
            _activityCreatedEventObjectPool);
#endif
    }

    private static async ValueTask PublishActivityInternalAsync(IEnumerable<IActivity> activities,
        IPublishEndpoint publisher,
        ObjectPool<ActivityCreatedEvent> activityCreatedEventObjectPool)
    {
        var activityCreatedEvents = activities.Select(activity =>
        {
            var activityCreatedEvent = activityCreatedEventObjectPool.Get();

            activityCreatedEvent.Activity = activity;

            return activityCreatedEvent;
        });

        try
        {
            await publisher.PublishBatch(activityCreatedEvents);
        }
        finally
        {
            foreach (var activityCreatedEvent in activityCreatedEvents)
            {
                activityCreatedEventObjectPool.Return(activityCreatedEvent);
            }
        }
    }


    public ValueTask<IActivity> CreateActivityAsync<TActivityNameEnum>(
        TActivityNameEnum activity,
        Exception data,
        Guid? activityId, Action<ActivityBuilder> set)
        where TActivityNameEnum : Enum
    {
        return CreateActivityAsync(activity, ExceptionsHelper.GetSimplifiedExceptionDescription(data), activityId,
            set);
    }


    public ValueTask<IActivity> CreateActivityAsync<TActivityNameEnum>(TActivityNameEnum activity,
        object data,
        Action<ActivityBuilder> set) where TActivityNameEnum : Enum
    {
        return CreateActivityAsync(activity, data, null, set);
    }

    public ValueTask<IActivity> CreateActivityAsync<TActivityNameEnum>(TActivityNameEnum activity,
        Exception data,
        Action<ActivityBuilder> set) where TActivityNameEnum : Enum
    {
        return CreateActivityAsync(activity, data, null, set);
    }

    public ValueTask<IActivity> CreateActivityAsync<TActivityNameEnum>(TActivityNameEnum activity,
        Action<ActivityBuilder> set) where TActivityNameEnum : Enum
    {
        return CreateActivityAsync(activity, null, null, set);
    }

    protected virtual ValueTask EnrichActivityAsync(IActivity activity)
    {
        return ValueTask.CompletedTask;
    }

    #endregion

    #region Populate Activity Metadata from Attributes

    private static void PopulateActivityFromAttributes<TActivityNameEnum>(
        TActivityNameEnum activityNameEnum, ActivityBuilder activityBuilder,
        out ActivityDescription activityDescription)
        where TActivityNameEnum : Enum
    {
        var activityNameEnumString = activityNameEnum.ToString();

        #region GetFullEnumName local function

        string GetFullEnumName()
        {
            var typeFullName = typeof(TActivityNameEnum).FullName;

            var totalLength = typeFullName!.Length + 1 + activityNameEnumString.Length; // +1 for the dot

            Span<char> buffer = totalLength <= 256 ? stackalloc char[totalLength] : new char[totalLength];
            var span = buffer;

            typeFullName.AsSpan().CopyTo(span);
            span = span.Slice(typeFullName.Length);
            span[0] = '.';
            span = span.Slice(1);
            activityNameEnumString.AsSpan().CopyTo(span);

            return new string(buffer);
        }

        #endregion

        activityDescription = ActivityEnumEnumToMetadataCache.GetOrAdd(activityNameEnumString, key =>
            ActivityDescription.Create(activityNameEnum));

        if (activityDescription != null)
        {
            if (activityDescription.EnumType != typeof(TActivityNameEnum))
            {
                // If there's more than one activity with the same name, but different enum types, we have to use the full enum name as a key
                activityDescription = ActivityEnumEnumToMetadataCache.GetOrAdd(
                    GetFullEnumName(), key =>
                        ActivityDescription.Create(activityNameEnum));
            }

            #region Enriching activity using metadata

            if (activityDescription.Category != null)
                activityBuilder.Category(activityDescription.Category);

            if (activityDescription.InformationLevel != null)
            {
                activityBuilder.InformationLevel(activityDescription.InformationLevel.Value);
            }

            #endregion
        }
    }

    #endregion

    #region Activity Notifications

    private ValueTask ProcessNotificationsAsync(IActivity activity, ActivityDescription activityDescription)
    {
        if (activityDescription.Notifications?.Length > 0)
        {
            using var workspan = Workspan.Start<ActivityService>()
                .Tag("TenantId", activity.TenantId)
                .Tag("CorrelationId", activity.CorrelationId)
                .Tag("ActionTimestamp", activity.ActionTimestamp)
                .Tag("ActivityId", activity.Id)
                .Tag("Source", activity.Source)
                .Tag("Domain", activity.Domain)
                .Tag("Category", activity.Category)
                .Tag("Subcategory", activity.SubCategory)
                .Tag("Meta", activity.Meta);

            foreach (var notification in activityDescription.Notifications)
            {
                switch (notification.Channel)
                {
                    case NotificationChannel.Log:
                        LogActivityNotification(activity, notification, workspan);
                        break;
                    default:
                        throw new Exception($"Unknown notification channel {notification.Channel}");
                }
            }
        }

        return ValueTask.CompletedTask;
    }

    private static void LogActivityNotification(IActivity activity, NotifyAttribute notification, Workspan workspan)
    {
        LogEventLevel logEventLevel = notification.Level switch
        {
            NotificationLevel.Information => LogEventLevel.Information,
            NotificationLevel.Warning => LogEventLevel.Warning,
            NotificationLevel.Error => LogEventLevel.Error,
            NotificationLevel.Fatal => LogEventLevel.Fatal,
            _ => throw new Exception($"Unknown activity notification level {notification.Level}")
        };

        if (notification.Tags?.Length > 0)
        {
            workspan.Tag("Tags", string.Join(",", notification.Tags));
        }

        foreach (var recipient in notification.Recipients)
        {
            workspan
                .Tag("Recipient", recipient);


            workspan
                .Log.Write(logEventLevel,
                    "{EventType}: {ActivityName}", "ACTIVITY", activity.Name);
        }
    }

    #endregion

    #region Activity Statistics

    private ValueTask ProcessStatisticsAsync(IActivity activity, ActivityDescription activityDescription)
    {
        if (activityDescription.Statistics?.Length > 0)
        {
            using var workspan = Workspan.Start<ActivityService>()
                .Tag("TenantId", activity.TenantId)
                .Tag("CorrelationId", activity.CorrelationId)
                .Tag("ActionTimestamp", activity.ActionTimestamp)
                .Tag("ActivityId", activity.Id)
                .Tag("Source", activity.Source)
                .Tag("Domain", activity.Domain)
                .Tag("Category", activity.Category)
                .Tag("Subcategory", activity.SubCategory)
                .Tag("Meta", activity.Meta);

            foreach (var statistic in activityDescription.Statistics)
            {
                switch (statistic.Channel)
                {
                    case StatisticsChannel.Log:
                        LogActivityStatistics(activity, statistic, workspan);
                        break;
                    default:
                        throw new Exception($"Unknown statistics channel {statistic.Channel}");
                }
            }
        }

        return ValueTask.CompletedTask;
    }

    private static void LogActivityStatistics(IActivity activity, StatisticsAttribute statistics, Workspan workspan)
    {
        workspan
            .Tag("TenantId", activity.TenantId)
            .Tag("Category", activity.Category)
            .Tag("Meta", activity.Meta);

        if (statistics.Tags?.Length > 0)
        {
            workspan.Tag("Tags", string.Join(",", statistics.Tags));
        }

        workspan
            .Log.Write(LogEventLevel.Information,
                "{EventType}: {ActivityName}", "STATISTICS", activity.Name);
    }

    #endregion

    #region Commented

    // /// <summary>
    // /// Implement this method to store microservice's activities locally (if required)
    // /// </summary>
    // /// <param name="activity"></param>
    // /// <returns></returns>
    // protected virtual ValueTask SaveActivityLocallyAsync(IActivity activity)
    // {
    //     return ValueTask.CompletedTask;
    // }

    #endregion

#if USE_CHANNELS
    class ActivityChannelProcessor : BackgroundService
    {
        public const int MAX_ACTIVITIES_IN_PUBLISH_BATCH = 100;
#if !DEBUG
        private const int MAX_ACTIVITIES_PUBLISH_DELAY_IN_MS = 3 * 1000;
        private const int MIN_SHUTDOWN_DRAIN_TIME_IN_SECONDS = 20; // If no new activities for N seconds, shutdown
        private const int MAX_SHUTDOWN_DRAIN_TIME_IN_SECONDS = 60; // Force shutdown after N seconds
#else
        private const int MAX_ACTIVITIES_PUBLISH_DELAY_IN_MS = 1 * 1000;
        private const int MIN_SHUTDOWN_DRAIN_TIME_IN_SECONDS = 3;
        private const int MAX_SHUTDOWN_DRAIN_TIME_IN_SECONDS = 10;
#endif

        private readonly IServiceScopeFactory _serviceScopeFactory;
        private readonly ChannelReader<IActivity> _reader;

        public ActivityChannelProcessor(IActivityChannel myChannel, IServiceScopeFactory serviceScopeFactory)
        {
            _serviceScopeFactory = serviceScopeFactory;
            _reader = myChannel.Reader;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            using var workspan = Workspan.Start<ActivityChannelProcessor>();

            using var serviceScope = _serviceScopeFactory.CreateScope();

            var publisher = serviceScope.ServiceProvider.GetService<IPublishEndpoint>();

            var activityCreatedEventObjectPool =
                serviceScope.ServiceProvider.GetService<ObjectPool<ActivityCreatedEvent>>();

            var activityBatch = new List<IActivity>();

            DateTime? cancellationRequestedAt = null;
            DateTime lastPublishTime = DateTime.UtcNow;
            int maxActivitiesInChannel = 0;
            DateTime? lastMaxActivitiesInChannelUpdate = null;
            TimeSpan lastMaxActivitiesInChannelUpdatePeriod = TimeSpan.FromMinutes(5);
            while (true)
            {
                try
                {
                    int activitiesInChannel = 0;

                    using var timeoutCts = CancellationTokenSource.CreateLinkedTokenSource(stoppingToken);
                    timeoutCts.CancelAfter(TimeSpan.FromMilliseconds(MAX_ACTIVITIES_PUBLISH_DELAY_IN_MS));
                    try
                    {
                        activityBatch.Add(await _reader.ReadAsync(timeoutCts.Token));
                        activitiesInChannel++;
                    }
                    catch (OperationCanceledException)
                    {
                        if (stoppingToken.IsCancellationRequested && cancellationRequestedAt == null)
                        {
                            cancellationRequestedAt = DateTime.UtcNow;
                            workspan.Log.Information("Activity channel processor is shutting down");
                        }
                    }

                    activitiesInChannel += _reader.Count;

                    maxActivitiesInChannel = int.Max(activitiesInChannel, maxActivitiesInChannel);

                    var utcNow = DateTime.UtcNow;

                    // Get all available messages into the batch
                    while (activityBatch.Count < MAX_ACTIVITIES_IN_PUBLISH_BATCH &&
                           _reader.TryRead(out var item))
                    {
                        activityBatch.Add(item);
                    }


                    // Batch is full or time to publish?
                    if (activityBatch.Count >= MAX_ACTIVITIES_IN_PUBLISH_BATCH ||
                        (utcNow - lastPublishTime).TotalMilliseconds >= MAX_ACTIVITIES_PUBLISH_DELAY_IN_MS)
                    {
                        if (activityBatch.Any())
                            await PublishActivityInternalAsync(activityBatch, publisher,
                                activityCreatedEventObjectPool);

                        lastPublishTime = DateTime.UtcNow;
                        activityBatch.Clear();

                        #region Observability

                        if (lastMaxActivitiesInChannelUpdate == null ||
                            utcNow - lastMaxActivitiesInChannelUpdate >= lastMaxActivitiesInChannelUpdatePeriod)
                        {
                            workspan.Log.Information("Max activities in channel: {ActivityCount}",
                                maxActivitiesInChannel);

                            lastMaxActivitiesInChannelUpdate = utcNow;
                            maxActivitiesInChannel = 0;
                        }

                        #endregion
                    }

                    if (cancellationRequestedAt != null)
                    {
                        #region Draining logic

                        bool anyUnprocessedActivityLeft = _reader.Count > 0;

                        bool forceShutdown = false;

                        if (anyUnprocessedActivityLeft == false &&
                            utcNow - lastPublishTime >= TimeSpan.FromSeconds(MIN_SHUTDOWN_DRAIN_TIME_IN_SECONDS))
                        {
                            // No activities left and we haven't published anything for a while
                            forceShutdown = true;

                            workspan.Log.Information("No activities in last {Seconds} seconds, shutting down",
                                MIN_SHUTDOWN_DRAIN_TIME_IN_SECONDS);
                        }
                        else if ((utcNow - cancellationRequestedAt.Value).TotalSeconds >=
                                 MAX_SHUTDOWN_DRAIN_TIME_IN_SECONDS)
                        {
                            // Can't wait any longer - force shutdown
                            forceShutdown = true;

                            workspan.Log.Warning("Forcing shutdown after {Seconds} seconds",
                                MAX_SHUTDOWN_DRAIN_TIME_IN_SECONDS);
                        }

                        if (forceShutdown)
                        {
                            #region Observability

                            if (anyUnprocessedActivityLeft)
                            {
                                workspan
                                    .Tag("ActivitiesLeft", _reader.Count)
                                    .Log.Warning(
                                        "Activity channel processor is shutting down, but there are still activities left in the channel");
                            }
                            else
                            {
                                workspan.Log.Information(
                                    "Activity channel processor has successfully drained all activities and is shutting down");
                                break;
                            }

                            workspan.Log.Information("Activity channel processor is shutting down");

                            #endregion

                            break; //!!!
                        }

                        #endregion
                    }
                }
                catch (Exception e)
                {
                    workspan.RecordFatalException(e, "Failed publishing activities");
                }
            }

            //
            // await foreach (var activity in _reader.ReadAllAsync(stoppingToken))
            // {
            //     await PublishActivityInternalAsync(activity, publisher, activityCreatedEventObjectPool);
            // }
        }
    }
#endif
}

#if USE_CHANNELS
public interface IActivityChannel
{
    ChannelReader<IActivity> Reader { get; }
    ChannelWriter<IActivity> Writer { get; }
}

public class ActivityChannel : IActivityChannel
{
    private const int MAX_ACTIVITIES_IN_CHANNEL_PER_PROCESSOR = 1000;

    private readonly Channel<IActivity> _channel;

    public ActivityChannel()
    {
        _channel = Channel.CreateBounded<IActivity>(
            new BoundedChannelOptions(MAX_ACTIVITIES_IN_CHANNEL_PER_PROCESSOR * Environment.ProcessorCount)
            {
                FullMode = BoundedChannelFullMode.Wait
            });
    }

    public ChannelReader<IActivity> Reader => _channel.Reader;
    public ChannelWriter<IActivity> Writer => _channel.Writer;
}


#endif