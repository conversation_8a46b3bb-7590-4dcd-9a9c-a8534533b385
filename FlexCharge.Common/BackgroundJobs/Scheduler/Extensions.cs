using Hangfire;
using Hangfire.PostgreSql;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Common.BackgroundJobs
{
    public static class Extensions
    {
        private static readonly string SectionName = "scheduler";

        public static IServiceCollection AddScheduler(this IServiceCollection services, string connectionString)
        {
            IConfiguration configuration;
            using (var serviceProvider = services.BuildServiceProvider())
            {
                configuration = serviceProvider.GetService<IConfiguration>();
            }
            
            services.Configure<BackgroundTasksOptions>(configuration.GetSection(SectionName));
            var options = configuration.GetOptions<BackgroundTasksOptions>(SectionName);

            if (options.StorageType == "PostgreSQL")
                services.AddHangfire(x => x.UsePostgreSqlStorage(connectionString));
            else //if (options.StorageType == "SQL Server")
                services.AddHangfire(x => x.UseSqlServerStorage(connectionString));
            
            return services;
        }

        public static IApplicationBuilder UseScheduler(this IApplicationBuilder builder)
        {
            var options = builder.ApplicationServices.GetService<IConfiguration>()
                .GetOptions<BackgroundTasksOptions>("scheduler");
            // if (!options.Enabled)
            // {
            //     return builder;
            // }


            if (options.DashboardEnabled)
            {
                // if (options.AuthorizeEnabled)
                // {
                //     builder.UseHangfireDashboard("/hangfire", new DashboardOptions
                //     {
                //         //Authorization = new[] { new HangFireAuthorizationAttribute(options.AccessRole) },
                //         IgnoreAntiforgeryToken = true
                //     });
                // }
                // else
                {
                    builder.UseHangfireDashboard("/hangfire", new DashboardOptions
                    {
                        Authorization = new[] { new HangFireAuthorization() },
                        IgnoreAntiforgeryToken = true
                    });
                }
            }


            return builder.UseHangfireServer();
        }
    }
}