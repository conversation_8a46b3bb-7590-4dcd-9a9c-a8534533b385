using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Common.MSSql
{
    public static class Extensions
    {
        private static readonly string SectionName = "MSSql";

        public static IServiceCollection AddMSSql<TContext>(this IServiceCollection services) where TContext : DbContext
        {
            IConfiguration configuration;
            using (var serviceProvider = services.BuildServiceProvider())
            {
                configuration = serviceProvider.GetService<IConfiguration>();
            }

            services.Configure<Options>(configuration.GetSection(SectionName));
            var options = configuration.GetOptions<Options>(SectionName);
            services.AddDbContext<TContext>(o =>
            {
                //override added while deploying to GCP/GKE
                if (System.Environment.GetEnvironmentVariable("OVERRIDE_DB_CONNECTION_STRING") != null)
                {
                    o.UseSqlServer(System.Environment.GetEnvironmentVariable("OVERRIDE_DB_CONNECTION_STRING"), x => x.EnableRetryOnFailure());
                }
                else
                {
                    o.UseSqlServer(options.ConnectionString, x => x.EnableRetryOnFailure());
                }
            }, ServiceLifetime.Scoped);

            return services;
        }


        //public static void UseMSSql<TContext>(this IApplicationBuilder builder) where TContext : DbContext
        //{
        //    var options = builder.ApplicationServices.GetService<IConfiguration>()
        //        .GetOptions<Options>(SectionName);

        //    using (var serviceScope = builder.ApplicationServices.GetService<IServiceScopeFactory>().CreateScope())
        //    {
        //        var context = serviceScope.ServiceProvider.GetRequiredService<TContext>();
        //        context.Database.Migrate();
        //    }
        //}
    }
}