using FlexCharge.Common.Telemetry;
using Grpc.Core;
using Grpc.Core.Interceptors;
using Microsoft.Extensions.Logging;

namespace FlexCharge.Common.Grpc;

public class ClientLoggingInterceptor : Interceptor
{
    private readonly ILogger _logger;

    public ClientLoggingInterceptor(ILoggerFactory loggerFactory)
    {
        _logger = loggerFactory.CreateLogger<ClientLoggingInterceptor>();
    }

    public override AsyncUnaryCall<TResponse> AsyncUnaryCall<TRequest, TResponse>(
        TRequest request,
        ClientInterceptorContext<TRequest, TResponse> context,
        AsyncUnaryCallContinuation<TRequest, TResponse> continuation)
    {
        
        using var workspan = Workspan.Start<ClientLoggingInterceptor>();

        workspan.Log.Information(
            "Starting call. {Type} {Method} ",
            context.Method.Type, context.Method.Name);
        
        return continuation(request, context);
    }
}