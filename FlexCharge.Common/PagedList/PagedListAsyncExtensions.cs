using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Telemetry;
using Microsoft.EntityFrameworkCore;

//using Microsoft.EntityFrameworkCore;

namespace FlexCharge.Common
{
    public static class PagedListAsyncExtensions
    {
        /// <summary>
        /// Async creates a subset of this collection of objects that can be individually accessed by index and
        /// containing metadata about the collection of objects the subset was created from.
        /// </summary>
        /// <typeparam name="T">The type of object the collection should contain.</typeparam>
        /// <param name="superset">The collection of objects to be divided into subsets. If the collection implements <see cref="IQueryable{T}"/>, it will be treated as such.</param>
        /// <param name="pageNumber">The one-based index of the subset of objects to be contained by this instance.</param>
        /// <param name="pageSize">The maximum size of any individual subset.</param>
        /// <param name="totalSetCount">The total size of set</param>
        /// <param name="cancellationToken"></param>
        /// <returns>
        /// A subset of this collection of objects that can be individually accessed by index and containing metadata
        /// about the collection of objects the subset was created from.
        /// </returns>
        /// <seealso cref="PagedList{T}"/>
        public static async Task<IPagedList<T>> ToPagedListAsync<T>(
            this IQueryable<T> superset,
            int pageNumber,
            int pageSize,
            int? totalSetCount,
            CancellationToken cancellationToken,
            IQueryable<int> countEsimator = null)
        {
            if (pageNumber < 1)
            {
                throw new ArgumentOutOfRangeException($"pageNumber = {pageNumber}. PageNumber cannot be below 1.");
            }

            if (pageSize < 1)
            {
                throw new ArgumentOutOfRangeException($"pageSize = {pageSize}. PageSize cannot be less than 1.");
            }

            var subset = new List<T>();
            var totalCount = 0;

            if (superset == null)
            {
                return new PagedList<T>(subset.AsQueryable(), 1, 0, 0);
            }

            var isCountEstimated = false;

            if (totalSetCount.HasValue)
            {
                totalCount = totalSetCount.Value;
            }
            else
            {
                if (countEsimator is not null)
                {
                    try
                    {
                        totalCount = await countEsimator.FirstOrDefaultAsync();
                        Workspan.Current.Log
                            .Information("Total estimated count: {0} ", totalCount);
                    }
                    catch (Exception e)
                    {
                        Workspan.Current.RecordException(e, "Error in CountEstimator");
                    }
                }

                if (totalCount < 20000) // re-calculate exact value{
                {
                    totalCount = await superset.CountAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
                    Workspan.Current.Log.Information("Total exact count: {0}", totalCount);
                }
                else
                {
                    isCountEstimated = true;
                }
            }


            if (totalCount > 0)
            {
                var skip = (pageNumber - 1) * pageSize;

                subset.AddRange(await superset
                    .Skip(skip).Take(pageSize)
                    .ToListAsync(cancellationToken)
                    .ConfigureAwait(false));
            }

            return new PagedList<T>(subset, pageNumber, pageSize, totalCount, true, isCountEstimated);
        }

        /// <summary>
        /// Async creates a subset of this collection of objects that can be individually accessed by index and
        /// containing metadata about the collection of objects the subset was created from.
        /// </summary>
        /// <typeparam name="T">The type of object the collection should contain.</typeparam>
        /// <param name="superset">The collection of objects to be divided into subsets. If the collection implements <see cref="IQueryable{T}"/>, it will be treated as such.</param>
        /// <param name="pageNumber">The one-based index of the subset of objects to be contained by this instance.</param>
        /// <param name="pageSize">The maximum size of any individual subset.</param>
        /// <param name="totalSetCount">The total size of set</param>
        /// <returns>
        /// A subset of this collection of objects that can be individually accessed by index and containing metadata
        /// about the collection of objects the subset was created from.
        /// </returns>
        /// <seealso cref="PagedList{T}"/>
        public static Task<IPagedList<T>> ToPagedListAsync<T>(this IQueryable<T> superset, int pageNumber, int pageSize,
            int? totalSetCount = null, IQueryable<int> countEsimator = null) =>
            ToPagedListAsync(superset, pageNumber, pageSize, totalSetCount, CancellationToken.None, countEsimator);
    }
}