using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Amazon.SQS;
using FlexCharge.Common.Cache;
using FlexCharge.Common.MassTransit.DeferredMassTransitStart;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Utils;
using MassTransit;
using MassTransit.NewIdProviders;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

namespace FlexCharge.Common.MassTransit
{
    public static class Extensions
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configureBusAction"></param>
        /// <param name="overrideServiceName"></param>
        /// <param name="oneQueuePerConsumer">True - one queue per consumer, False - one queue per microservice</param>
        /// <typeparam name="TStartup"></typeparam>
        public static void AddMassTransit<TStartup>(this IServiceCollection services,
            Action<IBusRegistrationConfigurator> configureBusAction = null,
            string? overrideServiceName = null,
            bool oneQueuePerConsumer = true)
        {
            ServiceName = GetServiceName<TStartup>();

            services.AddIdempotencyKeyCache();

            services.AddSingleton<IDeferredStartReceiveEndpointDependency, DeferredStartReceiveEndpointDependency>();

            // In cases where multiple processes are on the same host generating identifiers,
            // it may be necessary to include the processId when generating identifiers.
            // To enable the use of the processId, call the method below on startup.
            //see: https://masstransit.io/documentation/patterns/newid#the-problem
            NewId.SetProcessIdProvider(new CurrentProcessIdProvider());

            // //To avoid KeyNotFoundException thrown for MT receive endpoints.
            // //See : https://github.com/MassTransit/MassTransit/issues/1818
            // //Needs more testing
            // global::MassTransit.AmazonSqsTransport.ClientContextCacheDefaults.MinAge = TimeSpan.FromDays(365);

            services.AddMassTransit(x =>
            {
                x.SetKebabCaseEndpointNameFormatter();

                x.AddConsumers(typeof(TStartup).Assembly);

                if (configureBusAction != null)
                {
                    configureBusAction(x);
                }

                //see: https://masstransit.io/documentation/configuration/scheduling
                x.AddDelayedMessageScheduler();

                string scopeName;

                scopeName = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
#if DEBUG
                if (RuntimeEnvironment.EnvironmentHelper.IsInDevelopment)
                {
                    scopeName = $"local-{Environment.UserName}-{scopeName}";
                }
#endif

                x.UsingAmazonSqs((context, cfg) =>
                {
                    //cfg.UseMessagePackSerializer();

                    cfg.Host(Environment.GetEnvironmentVariable("SNS_IAM_REGION"), h =>
                    {
                        // SNS/SQS credentials are specified on container/user level

                        // Specify a scope for all queues
                        h.Scope(scopeName);
                        // Scope topics as well
                        h.EnableScopedTopics();
                    });

                    ConfigureBusSendFilters(cfg, context);

                    cfg.UseDelayedMessageScheduler();

                    var serviceName = string.IsNullOrWhiteSpace(overrideServiceName)
                        ? ServiceName
                        : overrideServiceName;

                    AddReceiveEndpoints<TStartup>(oneQueuePerConsumer, cfg, serviceName, context);

                    cfg.ConfigureEndpoints(context);
                });
            });

            ConfigureStartAndStopTimeouts(services);

            //services.AddHostedService<Worker>();
        }

        public static void UseMassTransit(this IApplicationBuilder app)
        {
            if (app == null)
                throw new ArgumentNullException(nameof(app));

            app.ApplicationServices.GetRequiredService<IDeferredStartReceiveEndpointDependency>()
                .StartMassTransitReceiveEndpoints();
        }

        private static void AddReceiveEndpoints<TStartup>(bool oneQueuePerConsumer,
            IAmazonSqsBusFactoryConfigurator cfg,
            string serviceName, IBusRegistrationContext context)
        {
            if (oneQueuePerConsumer == false)
            {
                cfg.ReceiveEndpoint(GenerateSingleReceiveEndpointQueueName<TStartup>(serviceName), e =>
                {
                    ConfigureReceiveEndpoint(e, context);
                    e.ConfigureConsumers(context);

                    e.AddDependency(context.GetRequiredService<IDeferredStartReceiveEndpointDependency>());
                });
            }
            else
            {
                //Configuring separate queues for each consumer

                var allConsumerTypes = GetAllConsumerTypes<TStartup>();

                foreach (var consumerType in allConsumerTypes)
                {
                    cfg.ReceiveEndpoint(GetSQSQueueName(serviceName, GetConsumerMessageType(consumerType)),
                        e =>
                        {
                            ConfigureReceiveEndpoint(e, context);
                            e.ConfigureConsumer(context, consumerType);

                            e.AddDependency(
                                context.GetRequiredService<IDeferredStartReceiveEndpointDependency>());
                        });
                }
            }
        }

        private static void ConfigureStartAndStopTimeouts(IServiceCollection services)
        {
            //see: https://masstransit.io/documentation/configuration
            //see: https://www.youtube.com/watch?v=ZZdpz9StKCA

            services.Configure<MassTransitHostOptions>(options =>
            {
                // false, because the bus isn't started until the configuration is received    
                options.WaitUntilStarted = false;

                //options.StartTimeout = TimeSpan.FromSeconds(20);
                //options.StopTimeout = TimeSpan.FromMinutes(2);

                /* If specified, the ConsumeContext.CancellationToken will be canceled after the specified
                   timeout when the bus is stopping. This allows long-running consumers to observe the
                   cancellation token and react accordingly. Must be <= the StopTimeout */
                //options.ConsumerStopTimeout = TimeSpan.FromMinutes(1);
            });

            services.Configure<HostOptions>(options =>
            {
                // To configure the Generic Host options so that the bus has sufficient time to stop,
                // configure the host options as shown.
                options.ShutdownTimeout = TimeSpan.FromMinutes(1);
            });
        }


        private static void ConfigureQueueAttributes(IDictionary<string, object> queueAttributes)
        {
            // All queue attributes are specified only on queue creation.
            // Existing queues will not be updated!!!

            // Set the message retention period
            // The value must be between 60 (1 minute) and 1209600 (14 days) seconds
            //see: https://stackoverflow.com/questions/69454666/how-to-force-masstransit-to-update-queue-settings-for-exisitng-aws-sqs-queues
            int messageRetentionPeriod = (int) TimeSpan.FromDays(14).TotalSeconds;

            queueAttributes.Remove(QueueAttributeName.MessageRetentionPeriod);
            queueAttributes.Add(QueueAttributeName.MessageRetentionPeriod, messageRetentionPeriod);
        }

        private static Type GetConsumerMessageType(Type consumerType)
        {
            var interfaces = consumerType.GetInterfaces();

            var consumerInterface = interfaces.FirstOrDefault(i =>
                i.IsGenericType && i.GetGenericTypeDefinition() == typeof(IConsumer<>));

            if (consumerInterface == null)
            {
                throw new InvalidOperationException(
                    $"Consumer type {consumerType} does not implement IConsumer<T> interface");
            }

            var messageType = consumerInterface.GetGenericArguments()[0];

            return messageType;
        }

        private static string GetSQSQueueName(string serviceName, Type messageType)
        {
            // SQS queue names can only include alphanumeric characters, hyphens, or underscores.
            // 1 to 80 in length

            StringBuilder queueName = new();

            var currentEnvironment = EnvironmentHelper.GetCurrentEnvironment();
            string environment = GetEnvironmentPrefix(currentEnvironment);

            queueName.Append(environment);
            queueName.Append('-');

            queueName.Append(serviceName);
            queueName.Append('-');


            StringBuilder localDebuggingPrefix = new();

            if (currentEnvironment == EnvironmentHelper.Environment.Development)
            {
                // To avoid queue name collisions for local debugging
                // This can lead to receiving messages sent from other developer machines in local debugging
                localDebuggingPrefix.Append(Environment.UserName + "-");
            }

            // if (messageType.Namespace != null)
            // {
            //     consumerNameSpaceAndUserForLocalDebugging.Append(messageType.Namespace);
            // }

            if (localDebuggingPrefix.Length != 0)
            {
                // To make queue name unique for each developer machine

                //We need this prefix to be of predictable length -> using hash

                //see: https://yetanotherchris.dev/csharp/friendly-unique-id-generation-part-2/
                var s = localDebuggingPrefix.ToString();
                var stableHash = StringNonCryptoHashingHelper.GetStableHashCode(s);
                queueName.Append(string.Format("{0:X}", stableHash).ToUpper());
                queueName.Append('-');
            }

            string consumedTypeName;
            if (IsTypeBatchOfT(messageType))
            {
                consumedTypeName = "B-" + messageType.GenericTypeArguments.First().Name;
            }
            else
            {
                consumedTypeName = messageType.Name;
            }


            if (consumedTypeName.EndsWith("Consumer"))
            {
                consumedTypeName = consumedTypeName.Substring(0, consumedTypeName.Length - "Consumer".Length);
            }

            queueName.Append(consumedTypeName);

            if (queueName.Length - "prod-".Length > 80)
            {
                throw new ArgumentOutOfRangeException(
                    $"Queue name {queueName} is too long. Max length is 80 characters");
            }

            return queueName.ToString();
        }

        public static bool IsTypeBatchOfT(Type type)
        {
            if (!type.IsGenericType || type.GenericTypeArguments.Length != 1)
                return false;

            var genericTypeDefinition = type.GetGenericTypeDefinition();

            return genericTypeDefinition == typeof(Batch<>);
        }

        private static string GetEnvironmentPrefix(EnvironmentHelper.Environment currentEnvironment)
        {
            switch (currentEnvironment)
            {
                case EnvironmentHelper.Environment.Production:
                    return "prod";
                    break;
                case EnvironmentHelper.Environment.Sandbox:
                    return "sand";
                    break;
                case EnvironmentHelper.Environment.Staging:
                    return "stg";
                    break;
                case EnvironmentHelper.Environment.Development:
                    return "dev";
                    break;
                default:
                    throw new ArgumentOutOfRangeException($"Unknown environment {currentEnvironment}");
            }
        }


        private static void ConfigureBusSendFilters(IAmazonSqsBusFactoryConfigurator cfg,
            IBusRegistrationContext context)
        {
            cfg.UseSendFilter(typeof(RetryOnMassTransitExceptionsSendFilter<>), context);
        }

        private static void ConfigureReceiveEndpointFilters(IAmazonSqsReceiveEndpointConfigurator cfg,
            IBusRegistrationContext context)
        {
            cfg.UseConsumeFilter(typeof(HardExceptionTransformerConsumeFilter<>), context);
            cfg.UseSendFilter(typeof(RetryOnMassTransitExceptionsSendFilter<>), context);
        }

        private static void ConfigureReceiveEndpoint(IAmazonSqsReceiveEndpointConfigurator cfg,
            IBusRegistrationContext context)
        {
            ConfigureQueueAttributes(cfg.QueueAttributes);

            ConfigureReceiveEndpointRetry(cfg, context);

            #region Commented

            //cfg.UseInMemoryOutbox();
            //cfg.UseMessageRetry(r => r.Immediate(2));
            // cfg.ConfigureConsumeTopology = false;

            //cfg.UseRetry(r=>r.None());
            //cfg.UseMessageRetry(r => r.None());

            #endregion

            #region Commented

            // if (configureReceiveEndpoint != null)
            // {
            //     configureReceiveEndpoint(cfg);
            // }

            #endregion

            #region Commented

            //cfg.ConfigureDeadLetter(c=>c.UseFilter(new DeadLetterTransportFilter()));

            #endregion

            ConfigureReceiveEndpointFilters(cfg, context);
        }

        private static void ConfigureReceiveEndpointRetry(IAmazonSqsReceiveEndpointConfigurator cfg,
            IBusRegistrationContext context)
        {
            #region Commented

            // cfg.UseMessageRetry(r =>
            //     {
            //         r.Handle<MassTransitRetryException>();
            //         r.Immediate(3);
            //     }
            // );

            #endregion
        }

        /// <summary>
        /// Use this method to generate a queue name for the receive endpoint in case of single endpoint per microservice.
        /// </summary>
        /// <param name="serviceName"></param>
        /// <typeparam name="TStartup"></typeparam>
        /// <returns></returns>
        private static string GenerateSingleReceiveEndpointQueueName<TStartup>(string serviceName)
        {
            string queueName = $"{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")}-{serviceName}-Service";

#if DEBUG
            if (RuntimeEnvironment.EnvironmentHelper.IsInDevelopment)
            {
                queueName = $"local-{Environment.UserName}-{queueName}";
            }
#endif

            return queueName;
        }

        public static string ServiceName { get; private set; }

        private static string GetServiceName<TStartup>()
        {
            string startupNamespace = typeof(TStartup).Assembly.GetName().Name;
            int indexOfLastDot = startupNamespace.LastIndexOf('.');

            return startupNamespace.Substring(indexOfLastDot + 1);
        }

        public static Uri GetConsumerServiceSendEndpointAddress<TMessage>(string serviceName,
            bool oneQueuePerConsumer = true)
        {
            string serviceEndpointName;

            if (oneQueuePerConsumer == false)
            {
                serviceEndpointName =
                    $"{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")}-{serviceName}-Service";
#if DEBUG
                if (RuntimeEnvironment.EnvironmentHelper.IsInDevelopment)
                {
                    serviceEndpointName = $"local-{Environment.UserName}-{serviceEndpointName}";
                }
#endif
            }
            else
            {
                serviceEndpointName = GetSQSQueueName(serviceName, typeof(TMessage));
            }

            return new Uri("queue:" + serviceEndpointName);
        }


        public static Uri GetServiceSendEndpointAddress(string serviceName)
        {
            string serviceEndpointName =
                $"{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")}-{serviceName}-Service";
#if DEBUG
            if (RuntimeEnvironment.EnvironmentHelper.IsInDevelopment)
            {
                serviceEndpointName = $"local-{Environment.UserName}-{serviceEndpointName}";
            }
#endif

            return new Uri("queue:" + serviceEndpointName);
        }

        public static async Task<ISendEndpoint> GetServiceSendEndpoint(this ISendEndpointProvider sendEndpointProvider,
            string serviceName, bool oneQueuePerConsumer = true)
        {
            var sendEndpointUri = GetServiceSendEndpointAddress(serviceName);
            return await sendEndpointProvider.GetSendEndpoint(sendEndpointUri);
        }

        public static async Task<ISendEndpoint> GetServiceSendEndpoint<TMessage>(
            this ISendEndpointProvider sendEndpointProvider,
            string serviceName, bool oneQueuePerConsumer = true)
        {
            var sendEndpointUri = GetConsumerServiceSendEndpointAddress<TMessage>(serviceName, oneQueuePerConsumer);
            return await sendEndpointProvider.GetSendEndpoint(sendEndpointUri);
        }

        static IReadOnlyList<Type> GetAllConsumerTypes<TStartup>()
        {
            var consumerTypes = typeof(TStartup).Assembly
                .GetTypes()
                .Where(t => t.GetInterfaces()
                    .Any(i => i.IsGenericType && i.GetGenericTypeDefinition() == typeof(IConsumer<>)))
                .ToList();

            return consumerTypes;
        }
    }
}