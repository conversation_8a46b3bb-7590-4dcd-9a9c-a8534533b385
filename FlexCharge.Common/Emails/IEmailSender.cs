using System.Collections.Generic;
using System.Threading.Tasks;
using SendGrid.Helpers.Mail;

namespace FlexCharge.Common.Emails;

public interface IEmailSender
{
    public Task SendBulkEmailAsync<T>(
        List<EmailRequestDTO> requestDTOs,
        T dynamicTemplateData,
        string templateId = null,
        Attachment attachment = null,
        string senderEmailOverride = null,
        string senderNameOverride = null,
        string replyTo = null,
        string bcc = null,
        EmailType emailType = EmailType.Other);


    public Task<bool> SendEmailAsync<T>(
        string email,
        string subject,
        string message,
        T dynamicTemplateData,
        string templateId = null,
        Attachment attachment = null,
        string senderEmailOverride = null,
        string senderNameOverride = null,
        string replyTo = null,
        string bcc = null,
        EmailType emailType = EmailType.Other);
}