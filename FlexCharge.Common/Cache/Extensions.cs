using System;
using FlexCharge.Common.Cache.BigPayload;
using FlexCharge.Common.Cache.DistributedMemoryDatabase;
using FlexCharge.Common.Cache.RequestsIdempotency;
using FlexCharge.Common.Redis;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.StackExchangeRedis;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using StackExchange.Redis;

namespace FlexCharge.Common.Cache
{
    public static class Extensions
    {
        private static readonly string SectionName = "cache";

        public static IServiceCollection AddRedisCache(this IServiceCollection services)
        {
            var options = GetCacheOptions(services);
            var configurationOptions = GetRedisOptions(options.ConnectionString);

            AddStackExchangeRedisCache<IDistributedCache, RedisCache>(services, options.Instance, o =>
            {
                o.ConfigurationOptions = configurationOptions;
                o.InstanceName = options.Instance;
            });

            return services;
        }

        private static RedisOptions GetCacheOptions(IServiceCollection services)
        {
            IConfiguration configuration;
            using (var serviceProvider = services.BuildServiceProvider())
            {
                configuration = serviceProvider.GetService<IConfiguration>();
            }

            services.Configure<RedisOptions>(configuration.GetSection(SectionName));

            var options = configuration.GetOptions<RedisOptions>(SectionName);
            return options;
        }

        /// <summary>
        /// Adds Redis distributed caching services to the specified <see cref="IServiceCollection" />.
        /// </summary>
        /// <param name="services">The <see cref="IServiceCollection" /> to add services to.</param>
        /// <param name="setupAction">An <see cref="Action{RedisCacheOptions}"/> to configure the provided
        /// <see cref="RedisCacheOptions"/>.</param>
        /// <returns>The <see cref="IServiceCollection"/> so that additional calls can be chained.</returns>
        public static IServiceCollection AddStackExchangeRedisCache<TDistributedCacheInterface,
            TDistributedCacheImplementation>(
            IServiceCollection services, string configurationKey, Action<RedisCacheOptions> setupAction)
            where TDistributedCacheInterface : class, IDistributedCache
            where TDistributedCacheImplementation : class, TDistributedCacheInterface
        {
            if (services == null)
            {
                throw new ArgumentNullException(nameof(services));
            }

            if (setupAction == null)
            {
                throw new ArgumentNullException(nameof(setupAction));
            }

            services.AddOptions();
            services.Configure(configurationKey, setupAction);

            services.AddSingleton<TDistributedCacheInterface, TDistributedCacheImplementation>(sp =>
            {
                var options = sp.GetRequiredService<IOptionsMonitor<RedisCacheOptions>>().Get(configurationKey);
                return ActivatorUtilities.CreateInstance<TDistributedCacheImplementation>(sp, options);
            });

            return services;
        }

        private static ConfigurationOptions GetRedisOptions(string connectionString)
        {
            ConfigurationOptions configurationOptions;


#if DEBUG
            //For local debugging purposes using increased timeouts
            configurationOptions = new ConfigurationOptions
            {
                EndPoints = {connectionString}, // Redis server's address
                ConnectTimeout = 60000, // connection timeout in milliseconds
                SyncTimeout = 60000, // synchronous operations timeout in milliseconds
                AbortOnConnectFail = false,
            };

            // see: https://stackexchange.github.io/StackExchange.Redis/ThreadTheft
            // Uncomment only if thread theft is a problem 
            //ConnectionMultiplexer.SetFeatureFlag("preventthreadtheft", true);


            //ConnectionMultiplexer connectionMultiplexer = ConnectionMultiplexer.Connect(configurationOptions);

#else
            configurationOptions = new ConfigurationOptions
            {
                EndPoints = {connectionString}, // Redis server's address
            };
#endif
            return configurationOptions;
        }


        #region BigPayload Support

        public static IServiceCollection AddBigPayloadSupport(this IServiceCollection services)
        {
            var options = GetCacheOptions(services);
            var configurationOptions = GetRedisOptions(options.BigPayloadCacheConnectionString);

            var instanceName = "BIG_PAYLOAD";
            AddStackExchangeRedisCache<IBigPayloadDistributedCache, RedisCacheForBigPayload>(services, instanceName,
                o =>
                {
                    o.ConfigurationOptions = configurationOptions;
                    //o.InstanceName = instanceName;
                });

            services.AddTransient<IBigPayloadService, RedisBigPayloadService>();

            return services;
        }

        public class RedisCacheForBigPayload : RedisCache, IBigPayloadDistributedCache
        {
            public RedisCacheForBigPayload(IOptions<RedisCacheOptions> optionsAccessor) : base(optionsAccessor)
            {
            }
        }

        #endregion

        #region Idempotency Keys Cache Support

        internal static IServiceCollection AddIdempotencyKeyCache(this IServiceCollection services)
        {
            var options = GetCacheOptions(services);
            var configurationOptions = GetRedisOptions(options.IdempotencyCacheConnectionString);

            var instanceName = "IDEMPOTENCY_KEYS";
            AddStackExchangeRedisCache<IIdempotencyKeysDistributedCache, RedisCacheForIdempotencyKeys>(services,
                instanceName, o =>
                {
                    o.ConfigurationOptions = configurationOptions;
                    //o.InstanceName = instanceName;
                });

            return services;
        }

        public class RedisCacheForIdempotencyKeys : RedisCache, IIdempotencyKeysDistributedCache
        {
            public RedisCacheForIdempotencyKeys(IOptions<RedisCacheOptions> optionsAccessor) : base(optionsAccessor)
            {
            }
        }

        #endregion

        #region External Requests Cache Support

        /// <summary>
        /// Use this cache for external requests. It should be separate from internal cache as
        /// it's susceptible to DDOS attacks or sudden volume increases.
        /// </summary>
        /// <param name="services"></param>
        /// <returns></returns>
        public static IServiceCollection AddExternalRequestsCache(this IServiceCollection services)
        {
            var options = GetCacheOptions(services);
            var configurationOptions = GetRedisOptions(options.ExternalRequestsCacheConnectionString);

            var instanceName = "EXTERNAL_REQUESTS";
            AddStackExchangeRedisCache<IExternalRequestsDistributedCache, RedisCacheForExternalRequests>(services,
                instanceName, o =>
                {
                    o.ConfigurationOptions = configurationOptions;
                    //o.InstanceName = instanceName;
                });

            return services;
        }

        public class RedisCacheForExternalRequests : RedisCache, IExternalRequestsDistributedCache
        {
            public RedisCacheForExternalRequests(IOptions<RedisCacheOptions> optionsAccessor) : base(optionsAccessor)
            {
            }
        }

        #endregion

        #region MemoryDatabase Support

        #region AddMemoryDatabase

        /// <summary>
        /// Use this distributed memory database for external requests. It should be separate from internal distributed memory databases as
        /// it's susceptible to DDOS attacks or sudden volume increases.
        /// </summary>
        /// <param name="services"></param>
        /// <returns></returns>
        public static IServiceCollection AddExternalRequestsDistributedMemoryDatabase(this IServiceCollection services)
        {
            var options = GetCacheOptions(services);
            var configurationOptions = GetRedisOptions(options.ExternalRequestsCacheConnectionString);

            var instanceName = "EXTERNAL_REQUESTS_MEMORY_DB";
            AddStackExchangeRedisMemoryDatabase<IExternalRequestsDistributedMemoryDatabase,
                RedisExternalRequestsDistributedMemoryDatabase>(services, instanceName, o =>
            {
                o.ConfigurationOptions = configurationOptions;
                //o.InstanceName = instanceName;
            });

            return services;
        }

        public class RedisExternalRequestsDistributedMemoryDatabase : RedisDistributedMemoryDatabase,
            IExternalRequestsDistributedMemoryDatabase
        {
            public RedisExternalRequestsDistributedMemoryDatabase(IOptions<RedisCacheOptions> optionsAccessor) : base(
                optionsAccessor)
            {
            }
        }

        #endregion


        /// <summary>
        /// Adds Redis distributed caching services to the specified <see cref="IServiceCollection" />.
        /// </summary>
        /// <param name="services">The <see cref="IServiceCollection" /> to add services to.</param>
        /// <param name="setupAction">An <see cref="Action{RedisCacheOptions}"/> to configure the provided
        /// <see cref="RedisCacheOptions"/>.</param>
        /// <returns>The <see cref="IServiceCollection"/> so that additional calls can be chained.</returns>
        public static IServiceCollection AddStackExchangeRedisMemoryDatabase<TDistributedMemoryDatabaseInterface,
            TDistributedMemoryDatabaseImplementation>(
            IServiceCollection services, string configurationKey, Action<RedisCacheOptions> setupAction)
            where TDistributedMemoryDatabaseInterface : class, IDistributedMemoryDatabase
            where TDistributedMemoryDatabaseImplementation : class, TDistributedMemoryDatabaseInterface
        {
            if (services == null)
                throw new ArgumentNullException(nameof(services));

            if (setupAction == null)
                throw new ArgumentNullException(nameof(setupAction));

            services.AddOptions();
            services.Configure(configurationKey, setupAction);

            services.AddSingleton<TDistributedMemoryDatabaseInterface, TDistributedMemoryDatabaseImplementation>(sp =>
            {
                var options = sp.GetRequiredService<IOptionsMonitor<RedisCacheOptions>>().Get(configurationKey);
                return ActivatorUtilities.CreateInstance<TDistributedMemoryDatabaseImplementation>(sp, options);
            });

            return services;
        }

        class ExternalRequestsDistributedMemoryDatabase : RedisDistributedMemoryDatabase,
            IExternalRequestsDistributedMemoryDatabase
        {
            public ExternalRequestsDistributedMemoryDatabase(IOptions<RedisCacheOptions> optionsAccessor) : base(
                optionsAccessor)
            {
            }
        }

        #endregion

        #region AddSqlCache

        public static IServiceCollection AddSqlCache(this IServiceCollection services)
        {
            IConfiguration configuration;
            using (var serviceProvider = services.BuildServiceProvider())
            {
                configuration = serviceProvider.GetService<IConfiguration>();
            }

            services.Configure<SqlCacheOptions>(configuration.GetSection(SectionName));
            var options = configuration.GetOptions<SqlCacheOptions>(SectionName);
            services.AddDistributedSqlServerCache(o =>
            {
                o.ConnectionString = options.CacheConnectionString;
                o.SchemaName = options.SchemaName;
                o.TableName = options.TableName;
            });

            return services;
        }

        #endregion
    }
}