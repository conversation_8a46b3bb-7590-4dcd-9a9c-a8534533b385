using System;
using FlexCharge.Utils;

namespace FlexCharge.Common.Vault;

public static class VaultHelper
{
    public static string GetCardFingerprint(string cardNumber)
    {
        if (string.IsNullOrWhiteSpace(cardNumber)) throw new ArgumentException("Empty card number");
        var strippedCard = cardNumber.GetNumbers();
        var retval = Utils.CryptoSigningHelper.ComputeContentHash(strippedCard);
        return retval;
    }
}