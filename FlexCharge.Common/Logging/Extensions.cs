using Microsoft.AspNetCore.Hosting;
using Serilog;
using Serilog.Events;
using Serilog.Sinks.Elasticsearch;
using System;
using System.IO;
using System.Text;
using Amazon;
using Amazon.CloudWatchLogs;
using FlexCharge.Common.Logging.LogSuppression;
using FlexCharge.Common.RuntimeEnvironment;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using NewRelic.LogEnrichers.Serilog;
using Serilog.Formatting;
using Serilog.Formatting.Compact;
using Serilog.Sinks.AwsCloudWatch;

namespace FlexCharge.Common.Logging
{
    public static partial class Extensions
    {
        public static IHostBuilder UseLogging(this IHostBuilder hostBuilder,
            string applicationName = null,
            string appOptionsSection = null,
            string elkOptionsSection = null,
            string seqOptionsSection = null,
            string serilogOptionsSection = null)
        {
            hostBuilder.UseSerilog((context, loggerConfiguration) =>
            {
                SetupSerilog(applicationName, context.HostingEnvironment.EnvironmentName, context.Configuration,
                    appOptionsSection, elkOptionsSection, seqOptionsSection, serilogOptionsSection,
                    loggerConfiguration);
            });

            return hostBuilder;
        }

        public static IWebHostBuilder UseLogging(this IWebHostBuilder webHostBuilder,
            string applicationName = null,
            string appOptionsSection = null,
            string elkOptionsSection = null,
            string seqOptionsSection = null,
            string serilogOptionsSection = null)
        {
            webHostBuilder.UseSerilog((context, loggerConfiguration) =>
            {
                SetupSerilog(applicationName, context.HostingEnvironment.EnvironmentName, context.Configuration,
                    appOptionsSection, elkOptionsSection, seqOptionsSection, serilogOptionsSection,
                    loggerConfiguration);
            });

            return webHostBuilder;
        }

        private static void SetupSerilog(
            string applicationName, string environmentName, IConfiguration configuration,
            string appOptionsSection, string elkOptionsSection,
            string seqOptionsSection, string serilogOptionsSection,
            //WebHostBuilderContext context,
            LoggerConfiguration loggerConfiguration)
        {
            var elkEnabled = Environment.GetEnvironmentVariable("ELK_ENABLED")?.ToLowerInvariant();

            var appOptions =
                configuration.GetOptions<AppOptions>(string.IsNullOrWhiteSpace(appOptionsSection)
                    ? "app"
                    : appOptionsSection);
            var elkOptions =
                configuration.GetOptions<ElkOptions>(string.IsNullOrWhiteSpace(elkOptionsSection)
                    ? "elk"
                    : elkOptionsSection);
            var seqOptions =
                configuration.GetOptions<SeqOptions>(string.IsNullOrWhiteSpace(seqOptionsSection)
                    ? "seq"
                    : seqOptionsSection);
            var serilogOptions =
                configuration.GetOptions<SerilogOptions>(
                    string.IsNullOrWhiteSpace(serilogOptionsSection)
                        ? "serilog"
                        : serilogOptionsSection);


            //Override elk configuration if env var is set on publish profile
            if (!string.IsNullOrWhiteSpace(elkEnabled))
                elkOptions.Enabled = elkEnabled == "true" || elkEnabled == "1";

            if (!Enum.TryParse<LogEventLevel>(serilogOptions.Level, true, out var level))
            {
                if (EnvironmentHelper.IsInProduction)
                {
                    level = LogEventLevel.Information;
                }
                else
                {
                    level = LogEventLevel.Debug;
                }
            }

            applicationName = string.IsNullOrWhiteSpace(applicationName) ? appOptions.Name : applicationName;
            loggerConfiguration.Enrich.FromLogContext()
                .MinimumLevel.Is(level)
                .MinimumLevel.Override("Microsoft", LogEventLevel.Error)
                .MinimumLevel.Override("System", LogEventLevel.Error)
                .Enrich.WithProperty("Environment", environmentName)
                .Enrich.WithProperty("ApplicationName", applicationName)
                .Enrich.WithProperty("ApplicationVersion", appOptions.Version)
                .Enrich.WithThreadName()
                .Enrich.WithThreadId()
                .Enrich.WithProperty("MachineName", Environment.MachineName)
                .Enrich.With<Telemetry.SerilogActivityFromLogEnricher>()
                .Enrich.With<Telemetry.Workspan.WorkspanEnricher>()
                .Enrich.WithNewRelicLogsInContext();


            if (serilogOptions?.LogIpAddress != false)
            {
                loggerConfiguration.Enrich.WithClientIp();
            }

            Configure(appOptions, loggerConfiguration, level, elkOptions, seqOptions, serilogOptions);
        }

        private static void Configure(
            AppOptions appOptions, LoggerConfiguration loggerConfiguration, LogEventLevel level,
            ElkOptions elkOptions, SeqOptions seqOptions, SerilogOptions serilogOptions)
        {
            if (string.IsNullOrEmpty(serilogOptions.Path))
            {
                throw new Exception(
                    $"wwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwww =======> {serilogOptions.Path} {serilogOptions.Level} {serilogOptions.ConsoleEnabled}");
            }

            if (elkOptions.Enabled)
            {
                loggerConfiguration.WriteTo.Elasticsearch(new ElasticsearchSinkOptions(new Uri(elkOptions.Url))
                {
                    MinimumLogEventLevel = level,
                    AutoRegisterTemplate = true,
                    AutoRegisterTemplateVersion = AutoRegisterTemplateVersion.ESv6,
                    IndexFormat = string.IsNullOrWhiteSpace(elkOptions.IndexFormat)
                        ? "logstash-{0:yyyy.MM.dd}"
                        : elkOptions.IndexFormat,
                    ModifyConnectionSettings = connectionConfiguration =>
                        elkOptions.BasicAuthEnabled
                            ? connectionConfiguration.BasicAuthentication(elkOptions.Username, elkOptions.Password)
                            : connectionConfiguration
                });
            }

            if (seqOptions.Enabled)
            {
                loggerConfiguration.WriteTo.Seq(seqOptions.Url, apiKey: seqOptions.ApiKey);
            }

            if (serilogOptions.ConsoleEnabled)
            {
                //loggerConfiguration.WriteTo.Console(new JsonFormatter(renderMessage: true));
#if DEBUG
                loggerConfiguration.WriteTo.Console(); // To make local debugging log less verbose
#else
                loggerConfiguration.WriteTo.Console(new RenderedCompactJsonFormatter());
#endif

                // loggerConfiguration.WriteTo.Console(new RemoveCrLf(
                //     new MessageTemplateTextFormatter("[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Exception}")));


                // loggerConfiguration.WriteTo.File(
                //     path: string.Format(serilogOptions.Path, DateTime.Now.ToString("yyyy-MM-dd")),
                //     fileSizeLimitBytes: 52428800);
            }

            if (serilogOptions.AWSCloudwatchEnabled)
            {
                string environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
                if (environment.ToLower() == "production") environment = "prod";
                string logGroupName = $"/ecs/fc-core-{appOptions.Name.ToLower()}-server-{environment}".ToLower();

                //see: https://blog.ivankahl.com/logging-dotnet-to-aws-cloudwatch-using-serilog/
                //see: https://stackoverflow.com/questions/68258492/how-to-show-log-message-with-substituted-parameters-in-structured-logging-output


                var options = new CloudWatchSinkOptions
                {
                    //"/ecs/fc-core-eligibility-server-staging"
                    LogGroupName = logGroupName,

                    // (Optional) Specify a custom text formatter for the output message.
                    TextFormatter = new RenderedCompactJsonFormatter(),
                    //TextFormatter = new JsonFormatter(renderMessage: true),

                    MinimumLogEventLevel = level,


                    CreateLogGroup = false,

                    LogStreamNameProvider = new DefaultLogStreamProvider(),


                    // (Optional) Maximum number of log events that should be sent in a batch to AWS CloudWatch
                    BatchSizeLimit = 100,
                    // (Optional) The maximum number of log messages that are stored locally before being sent
                    // to AWS Cloudwatch
                    QueueSizeLimit = 10000,
                    // (Optional) Similar to above, except the maximum amount of time that should pass before
                    // log events must be sent to AWS CloudWatch
                    Period = TimeSpan.FromSeconds(10),
                    // (Optional) The number of attempts we should make when logging log events that fail
                    RetryAttempts = 5,
                    // (Optional) Specify the time that logs should be kept for in AWS CloudWatch
                    //LogGroupRetentionPolicy = LogGroupRetentionPolicy.OneMonth,
                };


                var awsCloudWatchClient = new AmazonCloudWatchLogsClient(
                    Environment.GetEnvironmentVariable("CWATCH_IAM_ACCESS_KEY"),
                    Environment.GetEnvironmentVariable("CWATCH_IAM_SECRET_KEY"),
                    region: RegionEndpoint.USEast1
                );

                loggerConfiguration
                    .WriteTo.AmazonCloudWatch(options, awsCloudWatchClient);
            }


            #region Log Filters

            loggerConfiguration
                .Filter.ByExcluding(logEvent =>
                    {
                        #region DO NOT TOUCH - OTHERWISE IF BY MISTAKE TRUE IS RETURNED, IT WILL SUPPRESS ALL ERRORS FROM LOGGING

                        bool convertIntoWarning = false;
                        try
                        {
                            var errorMessage = logEvent.RenderMessage();

                            // Changes log level from Error to Warning for log events that are expected to happen to unclutter logs
                            // E.g. UniqueConstraintException when duplicates are expected
                            convertIntoWarning =
                                DatabaseLogSuppressor.ShouldSuppressLogErrorEvent(logEvent, errorMessage);


                            if (convertIntoWarning)
                            {
#if !DEBUG
                                // Replace Error with Warning
                                Serilog.Log.Logger.Warning(logEvent.Exception, "SUPPRESSED: {ErrorMessage}",
                                    errorMessage);
#endif
                                return true;
                            }
                        }
                        catch (Exception e)
                        {
                            Serilog.Log.Logger.Fatal(e, "ERROR IN LOG FILTER");
                        }

                        return false;

                        #endregion
                    }
                );

            #endregion
        }

        #region class TextFormatter

        public class RemoveCrLf : ITextFormatter
        {
            private const int DefaultWriteBuffer = 256;

            private readonly ITextFormatter _textFormatter;

            /// <summary>
            /// 
            /// </summary>
            /// <param name="textFormatter"></param>
            public RemoveCrLf(ITextFormatter textFormatter)
            {
                _textFormatter = textFormatter;
            }

            /// <summary>
            /// 
            /// </summary>
            /// <param name="logEvent"></param>
            /// <param name="output"></param>
            public void Format(LogEvent logEvent, TextWriter output)
            {
                var buffer = new StringWriter(new StringBuilder(DefaultWriteBuffer));

                _textFormatter.Format(logEvent, buffer);

                var logText = buffer.ToString();

                output.WriteLine(logText.Trim().Replace("\n", "\\n").Replace("\r", "\\r"));
                output.Flush();
            }
        }

        #endregion
    }
}