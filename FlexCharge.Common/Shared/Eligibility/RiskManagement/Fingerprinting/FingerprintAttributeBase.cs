using System;

namespace FlexCharge.Eligibility.RiskManagement.Fingerprinting;

public abstract class FingerprintAttributeBase : Attribute
{
    public FingerprintType Type { get; }
    public FingerprintValueNormalization ValueNormalization { get; }
    public abstract bool IsFingerprintSource { get; }
    public virtual bool UseValueAsMeta => false;


    protected FingerprintAttributeBase(FingerprintType type, FingerprintValueNormalization valueNormalization)
    {
        Type = type;
        ValueNormalization = valueNormalization;
    }
}