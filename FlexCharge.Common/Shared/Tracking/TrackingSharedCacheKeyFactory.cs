using System;
using FlexCharge.Common.Cache;
using Microsoft.Extensions.Caching.Distributed;

namespace FlexCharge.Common.Shared.Tracking;

/// <summary>
/// Note: this cache keys are used cross microservice!!!
/// </summary>
public class TrackingSharedCacheKeyFactory : CacheKeyFactoryBase
{
    #region Tracking

    public static CacheKey CreateTrackingKey_ByOrderSessionKey(Guid mid, string orderSessionKey) =>
        CreateScopedKey($"Tracking_OSK_{mid}_{orderSessionKey}",
            new DistributedCacheEntryOptions() {SlidingExpiration = TimeSpan.FromMinutes(6)});

    public static CacheKey CreateTrackingKey_ByExternalOrderId(Guid mid, string externalOrderId) =>
        CreateScopedKey($"Tracking_OID_{mid}_{externalOrderId}",
            new DistributedCacheEntryOptions() {SlidingExpiration = TimeSpan.FromMinutes(6)});

    public static CacheKey CreateTrackingKey_BySenseKey(Guid mid, Guid senseKey) =>
        CreateScopedKey($"Tracking_SK_{mid}_{senseKey}",
            new DistributedCacheEntryOptions() {SlidingExpiration = TimeSpan.FromMinutes(6)});

    #endregion

    #region Tracking Merchant Configuration

    // public static CacheKey CreateMerchantConfigurationKey(Guid mid) =>
    //     CreateScopedKey($"Tracking_MerchantConfiguration_{mid}",
    //         new DistributedCacheEntryOptions() {SlidingExpiration = TimeSpan.FromDays(1)});

    static readonly TimeSpan MERCHANT_SITE_CONFIGURATION_KEY_EXPIRATION = TimeSpan.FromDays(1);
    static string MERCHANT_SITE_CONFIGURATION_KEYS_PREFIX = "Tracking_MerchantSiteConfiguration";

    public static CacheKey GetScopedMerchantSiteConfigurationKeysPrefix(Guid mid) =>
        CreateScopedKey($"{MERCHANT_SITE_CONFIGURATION_KEYS_PREFIX}_{mid}_",
            new DistributedCacheEntryOptions()
                {AbsoluteExpirationRelativeToNow = MERCHANT_SITE_CONFIGURATION_KEY_EXPIRATION}
        );

    public static CacheKey CreateMerchantSiteConfigurationKey(Guid mid, Guid? siteId) =>
        CreateScopedKey($"{MERCHANT_SITE_CONFIGURATION_KEYS_PREFIX}_{mid}_{siteId}",
            new DistributedCacheEntryOptions() {SlidingExpiration = MERCHANT_SITE_CONFIGURATION_KEY_EXPIRATION});

    public static CacheKey CreateModulesKey() =>
        CreateScopedKey($"Tracking_Modules",
            new DistributedCacheEntryOptions() {SlidingExpiration = TimeSpan.FromDays(1)});

    public static CacheKey CreateGetMerchantByMidKey(Guid mid) =>
        CreateScopedKey($"Tracking_GetMerchantByMid_{mid}",
            new DistributedCacheEntryOptions() {SlidingExpiration = TimeSpan.FromDays(1)});

    #endregion
}