using System;
using System.Threading.Tasks;
using FlexCharge.Common.Cache;
using FlexCharge.Common.Cache.DistributedMemoryDatabase;
using FlexCharge.Common.MassTransit;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.Commands;
using MassTransit;
using Microsoft.Extensions.Caching.Distributed;

namespace FlexCharge.Common.Shared.Merchants.Sites;

class MerchantSitesService : IMerchantSitesService
{
    private readonly IExternalRequestsDistributedCache _externalRequestsDistributedCache;
    private readonly IExternalRequestsDistributedMemoryDatabase _externalRequestsDistributedMemoryDatabase;
    private readonly IRequestClient<GetMerchantSitesCommand> _getMerchantSitesCommandRequestClient;

    public MerchantSitesService(
        IExternalRequestsDistributedCache externalRequestsDistributedCache,
        IExternalRequestsDistributedMemoryDatabase externalRequestsDistributedMemoryDatabase,
        IRequestClient<GetMerchantSitesCommand> getMerchantSitesCommandRequestClient)
    {
        _externalRequestsDistributedCache = externalRequestsDistributedCache;
        _externalRequestsDistributedMemoryDatabase = externalRequestsDistributedMemoryDatabase;
        _getMerchantSitesCommandRequestClient = getMerchantSitesCommandRequestClient;
    }

    public async Task<Guid?> GetSiteIdByHostOrDefault(Guid mid, Uri url)
    {
        using var workspan = Workspan.Start<MerchantSitesService>()
            .Baggage("Mid", mid)
            .Baggage("Url", url.OriginalString);

        Guid? siteId = null;


        try
        {
            var host = url.Host.Trim().ToLowerInvariant();

            workspan
                .Tag("Host", url.Host);

            var cacheKey = CacheKeyFactory.CreateSiteIdByHostKey(mid, host);

            siteId = await _externalRequestsDistributedCache.GetGuidValueAsync(cacheKey.Key);

            if (siteId == null)
            {
                if (await LoadSiteIdByHostDataToCacheAsync(mid))
                {
                    siteId = await _externalRequestsDistributedCache.GetGuidValueAsync(cacheKey.Key);
                }
            }

            workspan
                .Tag("SiteId", siteId);


            #region Observability

            if (siteId != null)
            {
                workspan.Log.Information("SiteIdByHost > SiteId found by host");
            }
            else
            {
                workspan.Log.Information("SiteIdByHost > No SiteId found by host");
            }

            #endregion
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);
        }

        return siteId;
    }

    public async Task InvalidateSiteIdByHostCacheAsync(Guid mid)
    {
        using var workspan = Workspan.Start<MerchantSitesService>()
            .Baggage("Mid", mid);

        try
        {
            await _externalRequestsDistributedMemoryDatabase.DeleteKeysWithPrefixAsync(CacheKeyFactory
                .GetScopedSiteIdByHostRelatedKeysPrefix(mid).Key);

            workspan.Log.Information("SiteId by host cache has been invalidated");
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e);
        }
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="mid"></param>
    /// <returns>True if cache has been updated</returns>
    public async Task<bool> LoadSiteIdByHostDataToCacheAsync(Guid mid)
    {
        using var workspan = Workspan.Start<MerchantSitesService>()
            .Tag("Mid", mid);

        var alreadyLoadedFlagCacheKey = CacheKeyFactory.SiteIdByHostDataIsLoadedFlagKey(mid);

        bool areMerchantSitesAlreadyInCache =
            await _externalRequestsDistributedCache.GetBooleanValueAsync(alreadyLoadedFlagCacheKey.Key) == true;

        if (areMerchantSitesAlreadyInCache)
        {
            workspan.Log.Information("Merchant sites data is already in cache");
            return false;
        }

        var response = await _getMerchantSitesCommandRequestClient
            .RunCommandAsync<GetMerchantSitesCommand, GetMerchantSitesCommandResponse>(new GetMerchantSitesCommand()
            {
                Mid = mid
            });

        var anySiteLoaded = response?.Message.Sites?.Count > 0;
        if (anySiteLoaded)
        {
            foreach (var site in response.Message.Sites)
            {
                foreach (var whitelistedUrl in site.WhitelistedUrls)
                {
                    workspan
                        .Tag("SiteId", site.SiteId)
                        .Tag("Host", whitelistedUrl.Link.Host)
                        .Tag("Url", whitelistedUrl.Link);

                    try
                    {
                        if (!whitelistedUrl.Link.IsAbsoluteUri)
                        {
                            workspan.Log.Warning("Whitelisted URL is not absolute URI");
                            continue;
                        }

                        var host = whitelistedUrl.Link.Host.Trim().ToLowerInvariant();

                        var cacheKey = CacheKeyFactory.CreateSiteIdByHostKey(mid, host);

                        await _externalRequestsDistributedCache.SetStringAsync(cacheKey.Key, site.SiteId.ToString("D"),
                            cacheKey.CacheOptions);

                        workspan.Log
                            .Information("SiteId by host has been loaded to cache");
                    }
                    catch (Exception e)
                    {
                        workspan.RecordFatalException(e);
                    }
                }
            }

            workspan.Log.Information("Merchant sites data has been loaded to cache");
        }
        else
        {
            workspan.Log.Information("No merchant sites data has not been loaded");
        }

        // Set flag that data has been loaded
        // We should to set it even if no sites were loaded to protect database against DDOS attacks
        await _externalRequestsDistributedCache.SetAsync(alreadyLoadedFlagCacheKey.Key, true,
            alreadyLoadedFlagCacheKey.CacheOptions);

        return anySiteLoaded;
    }
}