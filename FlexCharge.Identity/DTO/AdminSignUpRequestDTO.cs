using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace FlexCharge.Identity.DTO;

public class AdminSignUpRequestDTO //: BaseResponse
{
    public string Group { get; set; }

    [Required]
    [EmailAddress]
    [Display(Name = "Email")]
    public string Email { get; set; }

    [Required]
    [Display(Name = "UserName")]
    public string UserName { get; set; }

    [Required] public string FirstName { get; set; }
    [Required] public string LastName { get; set; }
    [Required] public string Phone { get; set; }

    public Guid MerchantId { get; set; }

    public Guid PartnerId { get; set; }
    public Guid AccountId { get; set; }
    public int? Timezone { get; set; }
    public string? Language { get; set; }
    public bool? IsPiiMasked { get; set; }
    public bool IsEnforceMFAEnabled { get; set; }

    public string MerchantState { get; set; }

    // [Required]
    // [StringLength(100, ErrorMessage = "The {0} must be at least {2} and at max {1} characters long.", MinimumLength = 6)]
    // [DataType(DataType.Password)]
    // [Display(Name = "Password")]
    // public string Password { get; set; }
    //
    // [DataType(DataType.Password)]
    // [Display(Name = "Confirm password")]
    // [Compare("Password", ErrorMessage = "The password and confirmation password do not match.")]
    // public string ConfirmPassword { get; set; }
}

public enum MessageAction
{
    RESEND,
    SUPPRESS
}