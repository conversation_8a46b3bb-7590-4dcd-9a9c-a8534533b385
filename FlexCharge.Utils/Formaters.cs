using System;
using System.Linq;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace FlexCharge.Utils;

public static class Formatters
{
    public static string ConvertDateToExpirationUnified(string date)
    {
        if (DateTime.TryParse(date, out DateTime dateTime))
        {
            return dateTime.ToString("MM/yy");
        }

        return date;
    }

    public static string CleanPhoneNumber(string mobileNumber)
    {
        return CleanNumber(mobileNumber);
    }

    public static string CleanNumber(string dirtyNumber)
    {
        if (string.IsNullOrEmpty(dirtyNumber))
        {
            return string.Empty;
        }
        else
        {
            return new string(dirtyNumber.Where(char.IsDigit).ToArray());
        }
    }


    public static long DecimalToLong(decimal number)
    {
        return (long) (number * 100);
    }

    public static int DecimalToInt(decimal number)
    {
        return (int) (number * 100);
    }

    public static decimal LongToDecimal(long longNumber)
    {
        return ((decimal) longNumber / 100);
    }

    public static decimal IntToDecimalFixed2Digits(int x)
    {
        var n = IntToDecimal(x);
        return Decimal.Parse(n.ToString("0.00"));
    }

    public static decimal IntToDecimal(int longNumber)
    {
        return ((decimal) longNumber / 100);
    }

    public static decimal ToSignedDecimal(this int amount, MathHelpers.MathSign sign)
    {
        var decimalValue = IntToDecimalFixed2Digits(amount);
        return sign == MathHelpers.MathSign.Positive ? decimalValue : -decimalValue;
    }


    /// <summary>
    /// js analog to cut double numbers to fixed digits count after point
    /// </summary>
    public static decimal ToFixed(decimal number, int digitsCountAfterPoint = 2)
    {
        var multplier = (long) Math.Pow(10, digitsCountAfterPoint);
        return (decimal) (long) (number * multplier) / multplier;
    }

    public static string MaskSSN(string ssn)
    {
        //string pattern = @"(?:\d{3})-(?:\d{2})-(\d{4})";
        return (null != ssn) ? "*****" + ssn.Substring(ssn.Length - 4, 4) : string.Empty;
    }

    /// <summary>
    /// Mask card numbers with only the first 6 and last 4 digits left visible
    /// </summary>
    /// <param name="pan"></param>
    /// <returns></returns>
    public static string MaskPAN(string pan)
    {
        return pan.Mask(6, pan.Length - 6 - 4);
    }


    public static string MaskBankAccountNumber(string ba)
    {
        var lastDigits = ba.Substring(ba.Length - 4, 4);

        var requiredMask = new String('*', ba.Length - lastDigits.Length);

        var maskedString = string.Concat(requiredMask, lastDigits);
        var maskedCardNumberWithSpaces = Regex.Replace(maskedString, ".{4}", "$0 ");
        //string pattern = @"(?:\d{3})-(?:\d{2})-(\d{4})";
        return (null != maskedCardNumberWithSpaces) ? maskedCardNumberWithSpaces : string.Empty;
    }

    public static string JsonFormatAsIndentedString(string json)
    {
        object parsedJson = JsonConvert.DeserializeObject(!string.IsNullOrWhiteSpace(json) ? json : "{}");
        JsonSerializerSettings jsonSerializerSettings = new JsonSerializerSettings()
        {
            Formatting = Formatting.Indented,
            TypeNameHandling = TypeNameHandling.None
        };

        return JsonConvert.SerializeObject(parsedJson, jsonSerializerSettings);
    }
}